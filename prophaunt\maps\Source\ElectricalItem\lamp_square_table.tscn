[gd_scene load_steps=4 format=4 uid="uid://btrcia8otr5ok"]

[ext_resource type="Material" uid="uid://b6gd8jjb1kopc" path="res://prophaunt/Mat/Colormap2.tres" id="1_1pg3x"]

[sub_resource type="ArrayMesh" id="ArrayMesh_gye6r"]
_surfaces = [{
"aabb": AABB(-0.12, -3.88621e-08, -0.12, 0.24, 0.58, 0.24),
"format": 34359742465,
"index_count": 180,
"index_data": PackedByteArray("AQACAAAAAgABAAMABgABAAAAAQAGAAcACAAHAAUAAQAJAAoABQALAAgACwAFAAMACwADAAoACgADAAEABgAFAAcADAAEAAYABAAMAAIABgAPAAwACAAMAA8ADAAIAAsADgAIAA8ACAAOAAkADAAKAA0ACgAMAAsADgAKAAkACgAOAA0AEQASABAAEgARABMAFQAWABQAFgAVABcAFwAVABgAFwAYABkAFAAaABUAGgAbABkAGQAbABcAEgAXABsAEQAXABMAFwARABYAHwAYAB4AGAAfABkAGQAdABoAHQAZAB8AFAASABsAFgAQABQAFQAdABwAHQAVABoAFQAeABgAHgAVABwAAgAFAAQABQACAAMABwAIAAEAAQAIAAkABQAGAAQAAgAMAA0AAgANAA4ADwAGAAAADwAAAA4ADgAAAAIAGgAUABsAHQAeABwAHgAdAB8AFwASABMAEgAUABAAEAAWABEA"),
"name": "Material",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 32,
"vertex_data": PackedByteArray("jsL1vShcjz6TwvW9jsL1veB6FD+WwvW9jsL1vShcjz6LwvU9jsL1veB6FD+IwvU9kML1PShcjz6LwvU9kML1PeB6FD+IwvU9kML1PShcjz6TwvW9kML1PeB6FD+WwvW9CnygPeB6FD8QfKC9CHygveB6FD8QfKC9CHygveB6FD8CfKA9CnygPeB6FD8CfKA9CnygPShcjz4FfKA9CHygvShcjz4FfKA9CHygvShcjz4NfKC9CnygPShcjz4NfKC9kML1PWgtsrKOwvU9kML1PUzpJrOQwvW9jsL1vWgtsrKOwvU9jsL1vUzpJrOQwvW9ppvEPQTXIz2mm8Q96NhtPALXIz3r2G28ppvEPQDXIz2mm8S9ppvEvQDXIz2mm8S9wNhtvALXIz3r2G28wNhtvALXIz292G086NhtPALXIz292G08ppvEvQTXIz2mm8Q9kKeUPPUo3D6hp5S8kKeUPPUo3D53p5Q8iKeUvPUo3D6hp5S8iKeUvPUo3D53p5Q8")
}]
blend_shape_mode = 0

[sub_resource type="ArrayMesh" id="ArrayMesh_qr0gs"]
resource_name = "LampSquareTable_lampSquareTable"
_surfaces = [{
"aabb": AABB(-0.12, -3.88621e-08, -0.12, 0.24, 0.58, 0.24),
"attribute_data": PackedByteArray("ZJRHP/oUrT5klEc/+hStPmSURz/6FK0+ZJRHP/oUrT5klEc/+hStPmSURz/6FK0+ZJRHP/oUrT5klEc/+hStPmSURz/6FK0+ZJRHP/oUrT5klEc/+hStPmSURz/6FK0+ZJRHP/oUrT5klEc/+hStPmSURz/6FK0+ZJRHP/oUrT5klEc/+hStPmSURz/6FK0+ZJRHP/oUrT5klEc/+hStPmSURz/6FK0+ZJRHP/oUrT5klEc/+hStPmSURz/6FK0+ZJRHP/oUrT5klEc/+hStPmSURz/6FK0+ZJRHP/oUrT5klEc/+hStPmSURz/6FK0+ZJRHP/oUrT5klEc/+hStPmSURz/6FK0+ZJRHP/oUrT5klEc/+hStPmSURz/6FK0+ZJRHP/oUrT5klEc/+hStPmSURz/6FK0+ZJRHP/oUrT5klEc/+hStPmSURz/6FK0+ZJRHP/oUrT5klEc/+hStPmSURz/6FK0+ZJRHP/oUrT5klEc/+hStPmSURz/6FK0+mWtOP+CZJz2Za04/4JknPZlrTj/gmSc9mWtOP+CZJz2Za04/4JknPZlrTj/gmSc9mWtOP+CZJz2Za04/4JknPZlrTj/gmSc9mWtOP+CZJz2Za04/4JknPZlrTj/gmSc9mWtOP+CZJz2Za04/4JknPZlrTj/gmSc9mWtOP+CZJz2Za04/4JknPZlrTj/gmSc9mWtOP+CZJz2Za04/4JknPZlrTj/gmSc9mWtOP+CZJz2Za04/4JknPZlrTj/gmSc9mWtOP+CZJz2Za04/4JknPZlrTj/gmSc9mWtOP+CZJz2Za04/4JknPZlrTj/gmSc9mWtOP+CZJz2Za04/4JknPZlrTj/gmSc9mWtOP+CZJz2Za04/4JknPZlrTj/gmSc9mWtOP+CZJz2Za04/4JknPZlrTj/gmSc9mWtOP+CZJz2Za04/4JknPZlrTj/gmSc9mWtOP+CZJz2Za04/4JknPZlrTj/gmSc9mWtOP+CZJz2Za04/4JknPZlrTj/gmSc9"),
"format": 34359742487,
"index_count": 180,
"index_data": PackedByteArray("BQAIAAIACAAFAAsAEgADAAAAAwASABUAGQAWABAABAAcAB8AEAAiABkAIgAQAAoAIgAKAB8AHwAKAAQAFAARABcAJQANABMADQAlAAcAEwAuACUAGgAmAC8AJgAaACMAKgAYAC0AGAAqABsAJAAeACcAHgAkACEALAAgAB0AIAAsACkANAA3ADEANwA0ADoAPwBDAD0AQwA/AEYARgA/AEgARgBIAEsAPQBOAD8ATgBSAEsASwBSAEYAOABHAFMAMwBFADkARQAzAEIAXwBKAFwASgBfAE0ATABYAE8AWABMAF4APAA2AFEARAAyAD4AQQBZAFYAWQBBAFAAQABbAEkAWwBAAFUABgAPAAwADwAGAAkAFgAZAAQABAAZABwAEQAUAA4ABwAlACgABwAoACsALgATAAEALgABACsAKwABAAcATgA9AFIAVwBaAFQAWgBXAF0ARwA4ADsANgA8ADAAMgBEADUA"),
"material": ExtResource("1_1pg3x"),
"name": "Material",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 96,
"vertex_data": PackedByteArray("jsL1vShcjz6TwvW9jsL1vShcjz6TwvW9jsL1vShcjz6TwvW9jsL1veB6FD+WwvW9jsL1veB6FD+WwvW9jsL1veB6FD+WwvW9jsL1vShcjz6LwvU9jsL1vShcjz6LwvU9jsL1vShcjz6LwvU9jsL1veB6FD+IwvU9jsL1veB6FD+IwvU9jsL1veB6FD+IwvU9kML1PShcjz6LwvU9kML1PShcjz6LwvU9kML1PShcjz6LwvU9kML1PeB6FD+IwvU9kML1PeB6FD+IwvU9kML1PeB6FD+IwvU9kML1PShcjz6TwvW9kML1PShcjz6TwvW9kML1PShcjz6TwvW9kML1PeB6FD+WwvW9kML1PeB6FD+WwvW9kML1PeB6FD+WwvW9CnygPeB6FD8QfKC9CnygPeB6FD8QfKC9CnygPeB6FD8QfKC9CHygveB6FD8QfKC9CHygveB6FD8QfKC9CHygveB6FD8QfKC9CHygveB6FD8CfKA9CHygveB6FD8CfKA9CHygveB6FD8CfKA9CnygPeB6FD8CfKA9CnygPeB6FD8CfKA9CnygPeB6FD8CfKA9CnygPShcjz4FfKA9CnygPShcjz4FfKA9CnygPShcjz4FfKA9CHygvShcjz4FfKA9CHygvShcjz4FfKA9CHygvShcjz4FfKA9CHygvShcjz4NfKC9CHygvShcjz4NfKC9CHygvShcjz4NfKC9CnygPShcjz4NfKC9CnygPShcjz4NfKC9CnygPShcjz4NfKC9kML1PWgtsrKOwvU9kML1PWgtsrKOwvU9kML1PWgtsrKOwvU9kML1PUzpJrOQwvW9kML1PUzpJrOQwvW9kML1PUzpJrOQwvW9jsL1vWgtsrKOwvU9jsL1vWgtsrKOwvU9jsL1vWgtsrKOwvU9jsL1vUzpJrOQwvW9jsL1vUzpJrOQwvW9jsL1vUzpJrOQwvW9ppvEPQTXIz2mm8Q9ppvEPQTXIz2mm8Q9ppvEPQTXIz2mm8Q96NhtPALXIz3r2G286NhtPALXIz3r2G286NhtPALXIz3r2G28ppvEPQDXIz2mm8S9ppvEPQDXIz2mm8S9ppvEPQDXIz2mm8S9ppvEvQDXIz2mm8S9ppvEvQDXIz2mm8S9ppvEvQDXIz2mm8S9wNhtvALXIz3r2G28wNhtvALXIz3r2G28wNhtvALXIz3r2G28wNhtvALXIz292G08wNhtvALXIz292G08wNhtvALXIz292G086NhtPALXIz292G086NhtPALXIz292G086NhtPALXIz292G08ppvEvQTXIz2mm8Q9ppvEvQTXIz2mm8Q9ppvEvQTXIz2mm8Q9kKeUPPUo3D6hp5S8kKeUPPUo3D6hp5S8kKeUPPUo3D6hp5S8kKeUPPUo3D53p5Q8kKeUPPUo3D53p5Q8kKeUPPUo3D53p5Q8iKeUvPUo3D6hp5S8iKeUvPUo3D6hp5S8iKeUvPUo3D6hp5S8iKeUvPUo3D53p5Q8iKeUvPUo3D53p5Q8iKeUvPUo3D53p5Q8/////////7//fwAA////vwAA/3////+//////////7//f///////vwAA/3////+//3//f////z//fwAA////vwAA/3////+//3//f////z//f///////vwAA/3////+//3//f////z//fwAA////v////3////+//3//f////z//f///////v////3////+//////////7//fwAA////v////3////+//////////7//f///////v////3////+//3//f////z//f///////vwAA/3////+//3//f////z//f///////v////3////+//////////7//f///////v////3////+//////////7//f///////vwAA/3////+//////////7//fwAA////vwAA/3////+//////////7//fwAA////v////3////+//3//f////z//fwAA////v////3////+//3//f////z//fwAA////vwAA/3////+//3//r////z//fwAA////v//P/6////+//8///////7//fwAA////v//P/6////+//3//r////z//fwAA////v/8v/6////+//8///////7//fwAA////v/8v/6////+//3//r////z//f///////v//P/6////+//3///////7/R/gAA////v9H+0X7///+//8///////7//f///////v//P/6////+//8///////7//f///////v/8v/6////+//3///////7/R/gAA////vy0B0X7///+//3///////7//f9F+////Py0B0X7///+//3///////7//f9F+////P9H+0X7///+//3//r////z//f///////v/8v/6////+//3///////7/R/gAA////v9H+0X7///+//3///////7//f9F+////P9H+0X7///+//3///////7/R/gAA////vy0B0X7///+//3///////7//f9F+////Py0B0X7///+/")
}]
blend_shape_mode = 0
shadow_mesh = SubResource("ArrayMesh_gye6r")

[node name="lampSquareTable" type="MeshInstance3D"]
mesh = SubResource("ArrayMesh_qr0gs")
skeleton = NodePath("")
