extends Node


const FILE_LOCATION = "user://data.dat"
var save_http
var change_handle_http

signal handle_changed
signal handle_changed_error(response_code)

var data_sample = {
	"selected_character_id": 1,
	"server_ip": "**************",
	"port": 9000,
	"name": "Player",
	"JWT": "",
	"username": "",
	"sound": true,
	"music": true,
	"sound_volume": 1.0,
	"music_volume": 1.0,
	"particles": true,
	"cup": 0,
	"rate_showed": false,
	"has_rate": false,
	"rate_cup": 0,
	"today_win": false,
	"lang_select": false,
	"local": "en",
	"smooth_movement": false,
	"daily_claim_time": 0,
	"tutorial_started": false,
	"vpn_show": false,
	"name_ask": false,
	"email": "",
	"HINT_FIRST_GAME": false,
	"HINT_DAILY_REWARD": false,
	"HINT_CHARACTER_CHANGE": false,
	"HINT_UNLOCK_CHARACTER": false,
	"HINT_SECOND_GAME": false,
	"HINT_CHAT": false,
	"purchase_ids": [],
	"animation_unlocks": {},
	"graphics_setting": 1, #0 1 2 3
	"camera_sensitiviy": 1,
	"selected_game_mode": -1, # -1 means no mode selected, 0=Race, 1=FreeRide, 2=Prophaunt
	"shader_compile": true,
	"job_stats": {
		"menu": 0,
		"cook": 0,
		"deliver": 0,
		"clean": 0,
		
		"visit": 0,
		"heal": 0,
		
		"cash": 0
	},
	"arm": null,
	"im_dead": false,
	"mobile_id": -1,
	"mobile_color": 0,
	"mobile_purchases": [],
	"im_freezed": false,
	"inventory": {
		"hand": null,
		"inventory": [null, null, null]
	},
}

var data = {}


func _ready():
	save_http = HTTPRequest.new()
	save_http.request_completed.connect(on_save_complete)
	add_child(save_http)
	change_handle_http = HTTPRequest.new()
	change_handle_http.request_completed.connect(on_change_handle_complete)
	add_child(change_handle_http)


func init():
	data = data_sample
	save()


func save():
	if Constants.is_server:
		return
	var file = FileAccess.open(FILE_LOCATION, FileAccess.WRITE)
	file.store_var(data, true)
	file.close()


func load_all():
	if Constants.is_server and Constants.LOCAL_MODE == false:
		return
	var file = FileAccess.open(FILE_LOCATION, FileAccess.READ)
	if (file == null):
		init()
		return

	data = file.get_var(true)
	for key in data_sample:
		if data.has(key):
			continue
		data[key] = data_sample[key]
	JobManager.myJobStats = data["job_stats"]
	JobManager.fill_job_stats()


func get_item(key, default=null):
	if Constants.is_server and Constants.LOCAL_MODE == false:
		return
	if data.has(key):
		return data[key]
	
	return default


@warning_ignore("shadowed_variable")
func set_item(key, value, save=true):
	if Constants.is_server and Constants.LOCAL_MODE == false:
		return
	data[key] = value
	if save:
		save()


func send_save_request():
	if Constants.is_headless:
		return
	var url = Constants.BACKEND_URL + "/account/save_data/"
	data["hunger"] = Selector.my_hunger
	data["warmth"] = Selector.my_warmth
	data["inventory"] = InventoryManager.encode()
	var json = JSON.stringify(data)
	var headers = ["Content-Type: application/json", "Authorization: JWT " + get_item("JWT", "")]
	save_http.cancel_request()
	save_http.request(url, headers, HTTPClient.METHOD_POST, json)


func on_save_complete(_result, response_code, _headers, _body):
	if response_code == 200:
		print("data saved to cloud.")


func has_email():
	if data["email"] == null:
		return false
	
	return len(data["email"]) > 4


func send_change_handle_request(handle):
	var url = Constants.BACKEND_URL + "/account/change_handle/"
	var request_data = {
		"handle": handle,
	}
	var json = JSON.stringify(request_data)
	var headers = ["Content-Type: application/json", "Authorization: JWT " + get_item("JWT", "")]
	change_handle_http.cancel_request()
	change_handle_http.request(url, headers, HTTPClient.METHOD_POST, json)


func on_change_handle_complete(_result, response_code, _headers, _body):
	if response_code == 200:
		emit_signal("handle_changed")
	handle_changed_error.emit(response_code)


func add_purchase_id(purchase_id):
	var purchases = get_item("purchase_ids", [])
	purchases.append(purchase_id)
	set_item("purchase_ids", purchases, true)
	send_save_request()


func has_purchased(purchase_id):
	var purchases = get_item("purchase_ids", [])
	for id in purchases:
		if id == purchase_id:
			return true
	
	return false
