[gd_scene load_steps=4 format=3 uid="uid://dmwrnkabna3md"]

[ext_resource type="PackedScene" uid="uid://cy82bsxfj1ddv" path="res://prophaunt/scenes/PropObject.tscn" id="1_dpx5y"]
[ext_resource type="PackedScene" uid="uid://d0dq05bkx3t6e" path="res://prophaunt/maps/Source/Chairs/bench.tscn" id="2_natkv"]

[sub_resource type="BoxShape3D" id="BoxShape3D_qk47e"]
size = Vector3(1.61389, 1.90607, 0.817505)

[node name="BenchProp" instance=ExtResource("1_dpx5y")]

[node name="Bench" parent="Meshes" index="0" instance=ExtResource("2_natkv")]

[node name="CollisionShape3D" type="CollisionShape3D" parent="StaticBody3D" index="0"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0.00921637, 0.952911, -0.00726321)
shape = SubResource("BoxShape3D_qk47e")
