class_name RifleRunState
extends Node

@onready var character: Character = $"../.."

func _ready() -> void:
	pass

func run(delta):
	if character.controls != Constants.Controls.Player or Constants.is_server:
		return

	# Check for reload input while running
	if Input.is_action_just_pressed("reload"):
		character.rifleReloadRunState.start_state()
		return

	# Movement and jumping are handled in handle_rifle_mode
	character.handle_rifle_mode(delta, true)
	character.check_rifle_shoot(character.rifleShootRunState)

func start_state():
	if character.state == character.State.RIFLE_RUN:
		return
	
	character.state = character.State.RIFLE_RUN
	# Animation will be handled in handle_animation function


func end_state():
	# Clean up when leaving this state
	pass
