[gd_scene load_steps=4 format=3 uid="uid://b3l1oj1u4wrh5"]

[ext_resource type="PackedScene" uid="uid://cy82bsxfj1ddv" path="res://prophaunt/scenes/PropObject.tscn" id="1_0g3f7"]
[ext_resource type="PackedScene" uid="uid://dcgnggisjwq64" path="res://prophaunt/maps/Source/Bathroom/bathroom_cabinet_open.tscn" id="2_cgq0n"]

[sub_resource type="BoxShape3D" id="BoxShape3D_f8ox4"]
size = Vector3(1.16531, 1.97318, 0.705823)

[node name="BathroomCabinetOpenProp" instance=ExtResource("1_0g3f7")]

[node name="BathroomCabinetOpen" parent="Meshes" index="0" instance=ExtResource("2_cgq0n")]

[node name="CollisionShape3D" type="CollisionShape3D" parent="StaticBody3D" index="0"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0.00641644, 0.986592, 0.0332611)
shape = SubResource("BoxShape3D_f8ox4")
