[gd_scene load_steps=5 format=4 uid="uid://cgwhuwvhcpcx4"]

[ext_resource type="Material" uid="uid://dkmcjc7wc3ws6" path="res://prophaunt/Mat/GrassPath.tres" id="1_wh4ir"]

[sub_resource type="ArrayMesh" id="ArrayMesh_0y6hp"]
_surfaces = [{
"aabb": AABB(-1, -0.1, -1, 2, 0.1, 2),
"format": 34896613377,
"index_count": 222,
"index_data": PackedByteArray("AgAAAAEAAQADAAIAAQAEAAMAAQAFAAQAAQAGAAUAAQAHAAYACgAIAAkACQALAAoACwAMAAoACwANAAwADQAOAAwADQAPAA4AEgAQABEAEQATABIAEwAUABIAFAAVABIAFQAWABIAFgAXABIAGgAYABkAGQAbABoAGQAcABsAHAAdABsAHAAeAB0AHgAfAB0AIgAgACEAIQAjACIAIQAkACMAIwAlACIAIwAmACUAJgAnACUAJgAoACcAKAApACcAKgAkACEAKwAqACEAKwAhACwALQArACwALQAsAC4ALgAvAC0AAAAuACwALAABAAAABgAHACEAIQAgAAYADQALACcAJwApAA0ACAAiACUAJQAJAAgAIwAkABgAGAAaACMAJgAbAB0AHQAoACYALwAQABIAEgAtAC8AKwAXABYAFgAqACsAKwAtABIAEgAXACsACQAlACcAJwALAAkAJgAjABoAGgAbACYAAQAsACEAIQAHAAEAHAAZADAAMAAeABwAMAAfAB4AEwARADEAMQAVABMAFQAUABMAMgAKAAwADAAOADIADgAPADIAMwACAAMAAwAFADMAAwAEAAUA"),
"lods": [0.0816054, PackedByteArray("AgAAACwAAAAuACwALAAFAAIAAgAFADMALAAGAAUAIQAGACwAIQAgAAYACAAnAAoACAAlACcACAAiACUAJwANAAoAJwApAA0ADQAPAAoACgAPADIALQAQABEALwAQAC0AEQAVAC0AMQAVABEAFQAWAC0AFgArAC0AFgAqACsAGQAmABgAJgAjABgAIwAkABgAGQAdACYAHQAoACYAGQAfAB0AMAAfABkAIgAgACEAIQAjACIAIQAkACMAKgAkACEAIwAmACUAJgAnACUAJgAoACcAKAApACcAKwAhACwALQArACwALQAsAC4ALgAvAC0AIwAlACIAKwAqACEA")],
"name": "Material",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 52,
"vertex_data": PackedByteArray("///+/5M4AAD/3/7/kzgAAP///v/5HgAAgvD+//keAAAF4f7/fA8AAAXh/v8AAAAAa8f+/wAAAABrx/7//x8AAJM4/v8AAAAAkzj+//8fAAD5Hv7/AAAAAP8f/v+TOAAA+R7+/3wPAAAAAP7/kzgAAHwP/v/5HgAAAAD+//keAAD///7/a8cAAP///v8F4QAA/9/+/2vHAACC8P7/BeEAAAXh/v+C8AAABeH/////AABrx/////8AAGvH/v//3wAAkzj/////AAD5Hv////8AAJM4/v//3wAA/x/+/2vHAAD5Hv7/gvAAAAAA/v9rxwAAfA/+/wXhAAAAAP7/BeEAAIy3AAAAAAAAjLcAAP8fAABySAAAAAAAAHJIAAD/3wAAckgAAP//AABySAAA/x8AAP8fAACMtwAA/x8AAHJIAAAAAAAAjLcAAAAAAABySAAAjLcAAP//AACMtwAA/98AAP/fAABySAAA/98AAIy3AAD//wAAckgAAP//AACMtwAAAAD/////AAD///////8AAAAA/v8AAAAA///+/wAAAAA=")
}]
blend_shape_mode = 0

[sub_resource type="ArrayMesh" id="ArrayMesh_23ake"]
resource_name = "GroundPathCross_GroundPathCross"
_surfaces = [{
"aabb": AABB(-1, -0.1, -1, 2, 0.1, 2),
"attribute_data": PackedByteArray("Pdx4gz3ceINP2f2zT9n9s0/Z/bNzs1CCFLO9mgybQrOzgpWz8oOh3PKDodwptqrZKbaq2Sm2qtkS/D/uEvw/7kzj/OtM4/zrTOP861b9ENnW0UTZ1tFE2dbRRNkd8ebYTtCCwE7QgsDE5LXMuuRiwKeS04KnktOCA6dSgv2TNJv9kzSb/ZM0my6ni441s8qaY7/vms2+aq/NvmqvsaUErrGlBK6xpQSufr6l0H6+pdDCv9TluaXo0rml6NK5pejSQpSg5UKUoOVClKDlirP+5bqSYv66kmL+MKcv8iangv7IAbakB4eE/k0RwKRNEcCkNbyu+TW8rvmmAZfaQ/qC/qYBl9pHbt3aKaM5wymjOcNHbt3ay33n2q+8YsAqEd3avOCr+7zgq/sqEd3aXYRU46laeu5dhFTj8MGQ28gkeu7wwZDbUYIU/ala//3lv9DByCT//e59BaXufQWlUL1YwGluwKRpbsCkvqIDvr6iA76d+LK36yQjkZ34srdwhESdzFojkXCERJ0a/LGF6ySegVGC94PMWp6B3L+C/pK/UoJv/WLAUIJQgvKDodwS/D/uTtCCwM2+aq9+vqXQupJi/k0RwKQ1vK75pgGX2imjOcNHbt3avOCr+yoR3dpdhFTj8MGQ2+59BaVpbsCkvqIDvp34srdwhESd"),
"format": 34896613399,
"index_count": 222,
"index_data": PackedByteArray("BQABAAMAAwAGAAUAAwAHAAYAAwAIAAcAAwAJAAgAAwALAAkAEwAOABAAEAAVABMAFQAXABMAFQAZABcAGQAaABcAGQAbABoAIAAdAB4AHgAiACAAIgAjACAAIwAkACAAJAAlACAAJQAnACAALQAqACwALAAxAC0ALAAzADEAMwA1ADEAMwA2ADUANgA3ADUAPgA4ADoAOgBBAD4AOgBFAEEARABKAEAAQQBMAEcATABPAEcATABSAE8AUgBUAE8AVQBFADoAWQBWADsAWAA6AF0AYABYAF0AYABdAGMAYwBlAGAAAABiAFwAXAACAAAACgANAD0APQA5AAoAGAAUAE4ATgBTABgADwA/AEkASQASAA8AQwBGACsAKwAvAEMASwAwADQANABRAEsAZAAcAB8AHwBfAGQAWwApACYAJgBXAFsAWgBhACEAIQAoAFoAEQBIAFAAUAAWABEATQBCAC4ALgAyAE0ABABeADwAPAAMAAQAMwAsAGYAZgA2ADMAZgA3ADYAIgAeAGcAZwAkACIAJAAjACIAaAATABcAFwAaAGgAGgAbAGgAaQAFAAYABgAIAGkABgAHAAgA"),
"lods": [0.0816054, PackedByteArray("BQAAAHwAAABiAHwAfAAIAAUABQAIAGkAfABqAAgAcQBqAHwAcQA5AGoAawB4ABMAawB1AHgAawA/AHUAeABsABMAeABTAGwAbAAbABMAEwAbAGgAfQAcAB4AZAAcAH0AHgAkAH0AZwAkAB4AJABtAH0AbQB7AH0AbQBXAHsALAB3AG4AdwBzAG4AcwBGAG4ALABvAHcAbwBRAHcALAA3AG8AZgA3ACwAPgA4ADoAOgBBAD4AOgBFAEEAVQBFADoAQQBMAEcATABPAEcATABSAE8AUgBUAE8AWAA6AF0AYABYAF0AYABdAGMAYwBlAGAAdAB2AHIAegB5AHAA")],
"material": ExtResource("1_wh4ir"),
"name": "Material",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 126,
"vertex_data": PackedByteArray("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")
}]
blend_shape_mode = 0
shadow_mesh = SubResource("ArrayMesh_0y6hp")

[sub_resource type="ConcavePolygonShape3D" id="ConcavePolygonShape3D_ksyd4"]
data = PackedVector3Array(1, 0, -0.758, 1, 0, -0.558, 0.75, 0, -0.558, 0.75, 0, -0.558, 0.879, 0, -0.758, 1, 0, -0.758, 0.75, 0, -0.558, 0.758, 0, -0.879, 0.879, 0, -0.758, 0.75, 0, -0.558, 0.758, 0, -1, 0.758, 0, -0.879, 0.75, 0, -0.558, 0.558, 0, -1, 0.758, 0, -1, 0.75, 0, -0.558, 0.558, 0, -0.75, 0.558, 0, -1, -0.758, 0, -1, -0.558, 0, -1, -0.558, 0, -0.75, -0.558, 0, -0.75, -0.75, 0, -0.558, -0.758, 0, -1, -0.75, 0, -0.558, -0.758, 0, -0.879, -0.758, 0, -1, -0.75, 0, -0.558, -1, 0, -0.558, -0.758, 0, -0.879, -1, 0, -0.558, -0.879, 0, -0.758, -0.758, 0, -0.879, -1, 0, -0.558, -1, 0, -0.758, -0.879, 0, -0.758, 0.75, 0, 0.558, 1, 0, 0.558, 1, 0, 0.758, 1, 0, 0.758, 0.879, 0, 0.758, 0.75, 0, 0.558, 0.879, 0, 0.758, 0.758, 0, 0.879, 0.75, 0, 0.558, 0.758, 0, 0.879, 0.758, 0, 1, 0.75, 0, 0.558, 0.758, 0, 1, 0.558, 0, 1, 0.75, 0, 0.558, 0.558, 0, 1, 0.558, 0, 0.75, 0.75, 0, 0.558, -0.558, 0, 0.75, -0.558, 0, 1, -0.758, 0, 1, -0.758, 0, 1, -0.75, 0, 0.558, -0.558, 0, 0.75, -0.758, 0, 1, -0.758, 0, 0.879, -0.75, 0, 0.558, -0.758, 0, 0.879, -1, 0, 0.558, -0.75, 0, 0.558, -0.758, 0, 0.879, -0.879, 0, 0.758, -1, 0, 0.558, -0.879, 0, 0.758, -1, 0, 0.758, -1, 0, 0.558, -0.434, -0.1, -1, 0.434, -0.1, -1, 0.434, -0.1, -0.75, 0.434, -0.1, -0.75, -0.434, -0.1, 0.75, -0.434, -0.1, -1, 0.434, -0.1, -0.75, -0.434, -0.1, 1, -0.434, -0.1, 0.75, -0.434, -0.1, 0.75, -0.434, -0.1, -0.75, -0.434, -0.1, -1, -0.434, -0.1, 0.75, -0.75, -0.1, 0.434, -0.434, -0.1, -0.75, -0.75, -0.1, 0.434, -0.75, -0.1, -0.434, -0.434, -0.1, -0.75, -0.75, -0.1, 0.434, -1, -0.1, 0.434, -0.75, -0.1, -0.434, -1, -0.1, 0.434, -1, -0.1, -0.434, -0.75, -0.1, -0.434, 0.434, -0.1, 1, -0.434, -0.1, 1, 0.434, -0.1, -0.75, 0.434, -0.1, 0.75, 0.434, -0.1, 1, 0.434, -0.1, -0.75, 0.434, -0.1, 0.75, 0.434, -0.1, -0.75, 0.75, -0.1, -0.434, 0.75, -0.1, 0.434, 0.434, -0.1, 0.75, 0.75, -0.1, -0.434, 0.75, -0.1, 0.434, 0.75, -0.1, -0.434, 1, -0.1, -0.434, 1, -0.1, -0.434, 1, -0.1, 0.434, 0.75, -0.1, 0.434, 1, 0, -0.558, 1, -0.1, -0.434, 0.75, -0.1, -0.434, 0.75, -0.1, -0.434, 0.75, 0, -0.558, 1, 0, -0.558, 0.558, 0, -1, 0.558, 0, -0.75, 0.434, -0.1, -0.75, 0.434, -0.1, -0.75, 0.434, -0.1, -1, 0.558, 0, -1, -1, 0, -0.558, -0.75, 0, -0.558, -0.75, -0.1, -0.434, -0.75, -0.1, -0.434, -1, -0.1, -0.434, -1, 0, -0.558, -0.558, 0, -1, -0.434, -0.1, -1, -0.434, -0.1, -0.75, -0.434, -0.1, -0.75, -0.558, 0, -0.75, -0.558, 0, -1, -0.434, -0.1, 0.75, -0.434, -0.1, 1, -0.558, 0, 1, -0.558, 0, 1, -0.558, 0, 0.75, -0.434, -0.1, 0.75, -0.75, -0.1, 0.434, -0.75, 0, 0.558, -1, 0, 0.558, -1, 0, 0.558, -1, -0.1, 0.434, -0.75, -0.1, 0.434, 1, -0.1, 0.434, 1, 0, 0.558, 0.75, 0, 0.558, 0.75, 0, 0.558, 0.75, -0.1, 0.434, 1, -0.1, 0.434, 0.434, -0.1, 0.75, 0.558, 0, 0.75, 0.558, 0, 1, 0.558, 0, 1, 0.434, -0.1, 1, 0.434, -0.1, 0.75, 0.434, -0.1, 0.75, 0.75, -0.1, 0.434, 0.75, 0, 0.558, 0.75, 0, 0.558, 0.558, 0, 0.75, 0.434, -0.1, 0.75, -0.558, 0, -0.75, -0.434, -0.1, -0.75, -0.75, -0.1, -0.434, -0.75, -0.1, -0.434, -0.75, 0, -0.558, -0.558, 0, -0.75, -0.75, -0.1, 0.434, -0.434, -0.1, 0.75, -0.558, 0, 0.75, -0.558, 0, 0.75, -0.75, 0, 0.558, -0.75, -0.1, 0.434, 0.75, 0, -0.558, 0.75, -0.1, -0.434, 0.434, -0.1, -0.75, 0.434, -0.1, -0.75, 0.558, 0, -0.75, 0.75, 0, -0.558, -0.758, 0, 0.879, -0.758, 0, 1, -1, 0, 1, -1, 0, 1, -0.879, 0, 0.758, -0.758, 0, 0.879, -1, 0, 1, -1, 0, 0.758, -0.879, 0, 0.758, 0.879, 0, 0.758, 1, 0, 0.758, 1, 0, 1, 1, 0, 1, 0.758, 0, 1, 0.879, 0, 0.758, 0.758, 0, 1, 0.758, 0, 0.879, 0.879, 0, 0.758, -1, 0, -1, -0.758, 0, -1, -0.758, 0, -0.879, -0.758, 0, -0.879, -0.879, 0, -0.758, -1, 0, -1, -0.879, 0, -0.758, -1, 0, -0.758, -1, 0, -1, 1, 0, -1, 1, 0, -0.758, 0.879, 0, -0.758, 0.879, 0, -0.758, 0.758, 0, -1, 1, 0, -1, 0.879, 0, -0.758, 0.758, 0, -0.879, 0.758, 0, -1)

[node name="GroundPathCross" type="MeshInstance3D"]
mesh = SubResource("ArrayMesh_23ake")
skeleton = NodePath("")

[node name="StaticBody3D" type="StaticBody3D" parent="."]

[node name="CollisionShape3D" type="CollisionShape3D" parent="StaticBody3D"]
shape = SubResource("ConcavePolygonShape3D_ksyd4")
