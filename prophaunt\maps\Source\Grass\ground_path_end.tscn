[gd_scene load_steps=5 format=4 uid="uid://bvksnqctl6efc"]

[ext_resource type="Material" uid="uid://dkmcjc7wc3ws6" path="res://prophaunt/Mat/GrassPath.tres" id="1_c4xlu"]

[sub_resource type="ArrayMesh" id="ArrayMesh_h66r2"]
_surfaces = [{
"aabb": AABB(-1, -0.1, -1, 2, 0.1, 2),
"format": 34896613377,
"index_count": 156,
"index_data": PackedByteArray("AgAAAAEAAQADAAIABAACAAMAAwAFAAQACAAGAAcABwAJAAgACgAIAAkACQALAAoABwAGAAwADAANAAcADgANAAwAAAAPABAAEAABAAAAEQAQAA8AFAASABMAEwAVABQAEgAWABMAEwAXABUAGAAWABIAEgAZABgAGQARABgAGQAOABEAGQAaAA4AGQAbABoAGgAbABwAHAAdABoAGQAeABsAGQAfAB4AAwABAAYABgAFAAMABgAKAAUABgAIAAoAAQAQAAwADAAGAAEADAAQABEAEQAOAAwACwAdABwAHAAeAAsAHAAbAB4AGgAdAAsADgAaAAsADgALAAkADgAJAAcABwANAA4AFQAPAAAAAAAEABUAAAACAAQAGAAPABUAGAAVABcAGAAXABMAEwAWABgAGAARAA8A"),
"lods": [0.0331977, PackedByteArray("BAAPAAEAAQADAAQAAwAFAAQAEAABAA8ADwAEABUAEQAQAA8AEgAPABUAEgARAA8AEgAVABQAGQARABIAGQAOABEAGQAfAB4AGQAeAAsADgAZAAsACQALAAoACgAIAAkABwAJAAgADgAJAAcACAAGAAcABwAGAAwADgANAAwADAANAAcABwANAA4AAwABAAYABgAFAAMABgAKAAUABgAIAAoADAAGAAEAAQAQAAwADAAQABEAEQAOAAwADgALAAkA"), 0.143596, PackedByteArray("BAAVABEAEQAFAAQAEgARABUAEgAVABQAGQARABIAGQAOABEADgAZAAsAGQAeAAsAGQAfAB4ACQALAAoACQAMAA4ACgAMAAkADAAFABEADAAKAAUAEQAOAAwADgALAAkA")],
"name": "Material",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 32,
"vertex_data": PackedByteArray("Stf+//9fAABrxwAA/18AAGvH/v//HwAAjLcAAP8fAABrx///AAAAAIy3AAAAAAAAUVgAAP9fAABySP7//18AAHJIAAD/HwAAkzj+//8fAABySAAAAAAAAJM4//8AAAAAYVD/f/+fAABySP7//58AAJM4/v//3wAAStf+//+fAABbz/9//58AAGvH/v//3wAA///+////AADk8P7//18AAP////8AAAAABeH//wAAAADk8P7//58AAAXh/v//HwAAONT+///fAAAAAP7///8AAMYr/v//3wAA+R7+//8fAADYLv7//18AANgu/v//nwAA+R7//wAAAAAAAP//AAAAAA==")
}]
blend_shape_mode = 0

[sub_resource type="ArrayMesh" id="ArrayMesh_8xqdl"]
resource_name = "GroundPathEnd_GroundPathEnd"
_surfaces = [{
"aabb": AABB(-1, -0.1, -1, 2, 0.1, 2),
"attribute_data": PackedByteArray("PuqksD7qpLA+6qSwCgbYyAoG2MhF4NqwReDasM/iqZHP4qmRz+KpkdQOSOwE2dGRBNnRkebiUoLm4lKC1A7//QbZUIKPQ9jIj0PYyEKvdbFCr3Wxd6WrsXelq7F3pauxWUxI7PKncJLyp3CSBZ7AkgWewJIFnsCSBZ7AkllM//2bpw+D0Z0+g9GdPoPRnT6D9Ec6pVKqZ9BSqmfQYaU/0GGlP9BhpT/QI1WdgZ+dEe+fnRHvn50R7yDq/88g6v/PIOr/z6UBOqUv5d7PL+XezwoGnYGJ4grvieIK7+H9hP6z9vSwHP5mghXvX4Kl9gHQDu/lkbDoCu9VgoT+dJcR7zaR4ZLlmA2y5Zgv0DmRRYNQgkWD5uJSggWewJJSqmfQ5uJSgnelq7EFnsCSYaU/0CDq/88="),
"format": 34896613399,
"index_count": 156,
"index_data": PackedByteArray("CAABAAUABQALAAgADgAJAAwADAAQAA4AGQATABYAFgAcABkAIAAaAB0AHQAiACAAFwAUACYAJgApABcALAAoACUAAgAwADMAMwAGAAIANgAyAC8AOQA3ADgAOAA6ADkANwA7ADgAOAA8ADoAPQA7ADcANwA+AD0APgA1AD0APgArADUAPgA/ACsAPgBAAD8APwBAAEEAQQBCAD8APgBDAEAAPgBEAEMACgAEABIAEgAPAAoAEgAfAA8AEgAYAB8AAwAxACQAJAARAAMAJAAxADQANAAqACQAIQBCAEEAQQBDACEAQQBAAEMAPwBCACEAKwA/ACEALQAjAB4AKwAbABUAFQAnACsAOgAuAAAAAAANADoAAAAHAA0APQAuADoAPQA6ADwAPQA8ADgAOAA7AD0APQA1AC4A"),
"lods": [0.0331977, PackedByteArray("SABMAAUABQALAEgACwAQAEgAMgAFAEwATABIADoANQAyAEwANwBMADoANwA1AEwANwA6ADkAPgA1ADcAPgArADUAPgBEAEMAPgBDACEAKwA+ACEASgAhACAAIAAZAEoASQBKABkAKwBKAEkAGQATAEkASQATACUAKwBLACUAJQBLAEkASQBLACsACgADABEAEQAPAAoAEQAfAA8AEQAYAB8AJAARAAMAAwAxACQAJAAxADQANAAqACQALQAjAB4A"), 0.143596, PackedByteArray("RQA6ADUANQAQAEUANwA1ADoANwA6ADkAPgA1ADcAPgArADUAKwA+ACEAPgBDACEAPgBEAEMARgAhACAARgBHACsAIABHAEYAJAAPADQAJAAfAA8ANAAqACQALQAjAB4A")],
"material": ExtResource("1_c4xlu"),
"name": "Material",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 77,
"vertex_data": PackedByteArray("Stf+//9fNcBK1/7//1+fvkrX/v//X0/Ea8cAAP9f//9rxwAA/1///2vHAAD/X6O+a8cAAP9fP8Rrx/7//x8DwGvH/v//H7G+a8f+//8fUcSMtwAA/x///4y3AAD/H7W+jLcAAP8fZMRrx///AAD/v2vH//8AAHPEjLcAAAAA//+MtwAAAACGxFFYAAD/X///UVgAAP9f//9RWAAA/19uylFYAAD/X8zEckj+//9f/79ySP7//190ynJI/v//X7rEckgAAP8f//9ySAAA/x+HynJIAAD/H+/Ekzj+//8fAMCTOP7//x+NypM4/v//H9/Ekzj+//8fGy1ySAAAAAD//3JIAAAAANPEkzj//wAA/7+TOP//AADExJM4//8AAPXgYVD/f/+f//9hUP9//596vmFQ/3//nz3Eckj+//+fAcBySP7//596vnJI/v//nx7Ekzj+///f//+TOP7//9//v5M4/v//33q+kzj+///f9eBK1/7//58AwErX/v//n37KStf+//+f3cRbz/9//5///1vP/3//n37KW8//f/+fwMRrx/7//9///2vH/v//3/+/a8f+///ffsr///7/////v+Tw/v//X/+//////wAA/78F4f//AAD/v+Tw/v//n/+/BeH+//8f/7841P7//9//vwAA/v////+/xiv+///f/7/5Hv7//x8FwNgu/v//XyrA2C7+//+fHsD5Hv//AAD/vwAA//8AAP+/a8f//wAAu72TOP7//x+8wWFQ/3//n67Fa8f//wAAsLxySP7//18pwpM4/v//H4vBckj+//+f/71K1/7//581wOP3yW+H5Ye31+QPtf9/MMP/f/+/kuWHt6XkFrWe/T97ueWFt9/kDrX/f/+/xOWEtxzlBbV//wB/SuX/tP9//7+H5fe0/38ww/9//7+CzZFlJMtfZu7+332GzaNlHMsoZv9//7+VzedlMsvIZsz+mn2ZzfplLMuYZv9/AAD/f/+/J8t0ZsT+nIAgy0Rm/38AAP9/MMNxyB5l58qeZAH8/oFxyB5l2co5ZP9/MMM2/2OAccgeZf9/AACL/TmBx+VvspHm07T/fzDDx+Vvsjfm37T/fzDD+P8CgMflb7La/xGA+P8CgOX/zX+u/15/i/85gOz/2n/w/waA4f8OgND/FoDm/M15YfFOh1rzUYbQ/6J/+f8CgOrthZL85vZlJ9U+bi7jPJ1W525myOTEY+jnrGk66bOV")
}]
blend_shape_mode = 0
shadow_mesh = SubResource("ArrayMesh_h66r2")

[sub_resource type="ConcavePolygonShape3D" id="ConcavePolygonShape3D_rn13x"]
data = PackedVector3Array(0.558, 0, -0.75, 0.682, 0, -0.25, 0.558, -0.1, -0.25, 0.558, -0.1, -0.25, 0.434, -0.1, -0.75, 0.558, 0, -0.75, 0.558, 0, -1, 0.558, 0, -0.75, 0.434, -0.1, -0.75, 0.434, -0.1, -0.75, 0.434, -0.1, -1, 0.558, 0, -1, -0.434, -0.1, -0.75, -0.31, -0.1, -0.25, -0.434, 0, -0.25, -0.434, 0, -0.25, -0.558, 0, -0.75, -0.434, -0.1, -0.75, -0.434, -0.1, -1, -0.434, -0.1, -0.75, -0.558, 0, -0.75, -0.558, 0, -0.75, -0.558, 0, -1, -0.434, -0.1, -1, -0.434, 0, -0.25, -0.31, -0.1, -0.25, -0.372, -0.05, 0.25, -0.372, -0.05, 0.25, -0.434, 0, 0.25, -0.434, 0, -0.25, -0.558, 0, 0.75, -0.434, 0, 0.25, -0.372, -0.05, 0.25, 0.682, 0, -0.25, 0.682, 0, 0.25, 0.62, -0.05, 0.25, 0.62, -0.05, 0.25, 0.558, -0.1, -0.25, 0.682, 0, -0.25, 0.558, 0, 0.75, 0.62, -0.05, 0.25, 0.682, 0, 0.25, 1, 0, -1, 1, 0, 1, 0.882, 0, -0.25, 0.882, 0, -0.25, 0.758, 0, -1, 1, 0, -1, 1, 0, 1, 0.882, 0, 0.25, 0.882, 0, -0.25, 0.882, 0, -0.25, 0.758, 0, -0.75, 0.758, 0, -1, 0.658, 0, 0.75, 0.882, 0, 0.25, 1, 0, 1, 1, 0, 1, -1, 0, 1, 0.658, 0, 0.75, -1, 0, 1, 0.558, 0, 0.75, 0.658, 0, 0.75, -1, 0, 1, -0.558, 0, 0.75, 0.558, 0, 0.75, -1, 0, 1, -0.658, 0, 0.75, -0.558, 0, 0.75, -1, 0, 1, -0.758, 0, -0.75, -0.658, 0, 0.75, -0.658, 0, 0.75, -0.758, 0, -0.75, -0.634, 0, -0.25, -0.634, 0, -0.25, -0.634, 0, 0.25, -0.658, 0, 0.75, -1, 0, 1, -0.758, 0, -1, -0.758, 0, -0.75, -1, 0, 1, -1, 0, -1, -0.758, 0, -1, 0.434, -0.1, -0.75, 0.558, -0.1, -0.25, -0.31, -0.1, -0.25, -0.31, -0.1, -0.25, 0.434, -0.1, -1, 0.434, -0.1, -0.75, -0.31, -0.1, -0.25, -0.434, -0.1, -1, 0.434, -0.1, -1, -0.31, -0.1, -0.25, -0.434, -0.1, -0.75, -0.434, -0.1, -1, 0.558, -0.1, -0.25, 0.62, -0.05, 0.25, -0.372, -0.05, 0.25, -0.372, -0.05, 0.25, -0.31, -0.1, -0.25, 0.558, -0.1, -0.25, -0.372, -0.05, 0.25, 0.62, -0.05, 0.25, 0.558, 0, 0.75, 0.558, 0, 0.75, -0.558, 0, 0.75, -0.372, -0.05, 0.25, -0.558, 0, -1, -0.634, 0, 0.25, -0.634, 0, -0.25, -0.634, 0, -0.25, -0.758, 0, -1, -0.558, 0, -1, -0.634, 0, -0.25, -0.758, 0, -0.75, -0.758, 0, -1, -0.658, 0, 0.75, -0.634, 0, 0.25, -0.558, 0, -1, -0.558, 0, 0.75, -0.658, 0, 0.75, -0.558, 0, -1, -0.558, 0, 0.75, -0.558, 0, -1, -0.558, 0, -0.75, -0.558, 0, 0.75, -0.558, 0, -0.75, -0.434, 0, -0.25, -0.434, 0, -0.25, -0.434, 0, 0.25, -0.558, 0, 0.75, 0.758, 0, -1, 0.682, 0, 0.25, 0.682, 0, -0.25, 0.682, 0, -0.25, 0.558, 0, -1, 0.758, 0, -1, 0.682, 0, -0.25, 0.558, 0, -0.75, 0.558, 0, -1, 0.658, 0, 0.75, 0.682, 0, 0.25, 0.758, 0, -1, 0.658, 0, 0.75, 0.758, 0, -1, 0.758, 0, -0.75, 0.658, 0, 0.75, 0.758, 0, -0.75, 0.882, 0, -0.25, 0.882, 0, -0.25, 0.882, 0, 0.25, 0.658, 0, 0.75, 0.658, 0, 0.75, 0.558, 0, 0.75, 0.682, 0, 0.25)

[node name="GroundPathEnd" type="MeshInstance3D"]
mesh = SubResource("ArrayMesh_8xqdl")
skeleton = NodePath("")

[node name="StaticBody3D" type="StaticBody3D" parent="."]

[node name="CollisionShape3D" type="CollisionShape3D" parent="StaticBody3D"]
shape = SubResource("ConcavePolygonShape3D_rn13x")
