[gd_scene load_steps=5 format=4 uid="uid://c2h28eujx2140"]

[ext_resource type="Material" uid="uid://cfdv7vk03vl2u" path="res://Scenes/FreeRide/Assets/Material/WallMat.tres" id="1_q7mu6"]

[sub_resource type="ArrayMesh" id="ArrayMesh_f7ipl"]
_surfaces = [{
"aabb": AABB(-4, -4, -7.6, 8, 8, 7.6),
"format": 34359742465,
"index_count": 348,
"index_data": PackedByteArray("AgAAAAEAAQADAAIABQAHAAYABgAKAAkABgAJAAsABAALAAwADAANAAQAAwABAAYAAQARABAAAAACAAUABQAEAAAAEwALAAkACQAUABMADgAQABYAGQAXABgACgAgAB8ACgAPACAADwAhACAAEgARACMAJAAaABgAGAAiACQADAALABMAJwAmABwAHAAbACcAJwApACgAHwAUAAkAEgAlABYAFgAQABIABAAoACoAKAArACoAJgAoAAQABAANACYADQAcACYALAArACgAAQAAACoALwAZACoAGgAkAAEAJAARAAEAIwAxACEAIQAVABYAMAAyAB0AHgATADAANQAzADQANwAtACwAGwAdADcAHQAyADcAJwAbACwAMQAjACIAMgA5ADgAMAAxADoAOgA7ADAANQA4ADkANAA7ADoANQA2ADoABgAEAAUACgAIAAkABAAGAAsABgAHAAMACgAGAAEAAQAOAAoADgAPAAoAEAAOAAEAEQASABAAFgAVAA4AGAAaABkAHQAbABwAHQAcAA0ADQAMAB0AHgAdAAwAHwAIAAoAJAAiACMAEQAkACMAIwAlABIAEwAeAAwADgAVACEAIQAPAA4AKAAmACcACQAIAB8AAwAHAAUABQACAAMAKgAAAAQAKgArACwALAAtACoALQAuACoALgAvACoALgAXABkAGQAvAC4AKAApACwAKgAZAAEAGQAaAAEAMAAgACEAIQAxADAAJQAjACEAFgAlACEAHQAeADAAEwAfADAAHwAgADAAFAAfABMANAA2ADUALAAbADcALAApACcAGAAXAC4AIgAYAC4AMQAiAC4ANwAxAC4ALgAtADcAOAA3ADIAOQAyADAAMAA7ADkAMQA3ADgAOAA6ADEAOQAzADUAOgA2ADQAOgA4ADUAMwA5ADsAOwA0ADMA"),
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 60,
"vertex_data": PackedByteArray("AACAwAAAgMDNzEy/AACAwAAAgEDNzEy/exRuwHsUbsAAAACAexRuwHsUbkAAAACAAACAQAAAgMDNzEy/exRuQHsUbsAAAACAAACAQAAAgEDNzEy/exRuQHsUbkAAAACAAACAQAAAAECamRnAAACAQAAAAEBcjwLAAACAQAAAgECamRnAAACAQAAAAMBcjwLAAACAQAAAAMCamRnAAACAQAAAgMCamRnAAAAAQAAAgEBcjwLAAAAAQAAAgECamRnAAAAAwAAAgEBcjwLAAACAwAAAgECamRnAAAAAwAAAgECamRnAUptUQAAAAMBcjwLAUptUQAAAAEBcjwLAAAAAQFKbVEBcjwLAAAAAwFKbVEBcjwLAUptUwAAAAMBcjwLAUptUwAAAAEBcjwLAAACAwAAAAMBcjwLAAACAwAAAAEBcjwLAAAAAQFKbVMCamRnAAAAAQAAAgMCamRnAUptUQFKbVMCamRnAUptUQAAAAMCamRnAUptUQAAAAECamRnAUptUQFKbVECamRnAAAAAQFKbVECamRnAUptUwAAAAECamRnAUptUwFKbVECamRnAAACAwAAAAECamRnAAAAAwFKbVECamRnAAAAAQAAAgMBcjwLAAAAAQFKbVMBcjwLAAAAAwAAAgMBcjwLAAAAAwFKbVMBcjwLAAACAwAAAgMCamRnAAAAAwAAAgMCamRnAAAAAwFKbVMCamRnAUptUwFKbVMCamRnAUptUwAAAAMCamRnAAACAwAAAAMCamRnAUptUQFKbVEDsUTjAUptUwFKbVEDsUTjAUptUQFKbVMDsUTjAcxNTPnMTU74yM/PAcxNTPnMTUz4yM/PAcxNTvnMTU74yM/PAcxNTvnMTUz4yM/PAUptUwFKbVMDsUTjArIMlwKyDJcDhepTArIMlQKyDJcDhepTArIMlwKyDJUDhepTArIMlQKyDJUDhepTA")
}]
blend_shape_mode = 0

[sub_resource type="ArrayMesh" id="ArrayMesh_votg2"]
resource_name = "TowerSquareTopRoofRounded_tower-square-top-roof-rounded_002"
_surfaces = [{
"aabb": AABB(-4, -4, -7.6, 8, 8, 7.6),
"attribute_data": PackedByteArray("/////wAAWD/lNXQ//////wAAWD/lNXQ//////wAAWD/lNXQ//////wAAWD/lNXQ//////wAAWD/lNXQ//////wAAWD/lNXQ//////wAAWD/lNXQ//////wAAWD/lNXQ//////wAAWD+amXk//////wAAWD+amXk//////wAAWD+amXk//////wAAWD+amXk//////wAAWD+amXk//////wAAWD+amXk//////wAAWD/lNXQ//////wAAWD/lNXQ//////wAAWD/lNXQ//////wAAWD/lNXQ//////wAAWD+amXk//////wAAWD+amXk//////wAAWD+amXk//////wAAWD+amXk//////wAAWD/lNXQ//////wAAWD/lNXQ//////wAAWD/lNXQ//////wAAWD/lNXQ//////wAAWD/lNXQ//////wAAWD+amXk//////wAAWD+amXk//////wAAWD+amXk//////wAAWD98bmk//////wAAWD98bmk//////wAAWD98bmk//////wAAWD9a22s//////wAAWD9a22s//////wAAWD9a22s//////wAAWD98bmk//////wAAWD98bmk//////wAAWD98bmk//////wAAWD9a22s//////wAAWD9a22s//////wAAWD9a22s//////wAAWD98bmk//////wAAWD98bmk//////wAAWD98bmk//////wAAWD98bmk//////wAAWD98bmk//////wAAWD98bmk//////wAAWD9a22s//////wAAWD9a22s//////wAAWD9a22s//////wAAWD98bmk//////wAAWD98bmk//////wAAWD98bmk//////wAAWD9a22s//////wAAWD9a22s//////wAAWD9a22s//////wAAWD98bmk//////wAAWD98bmk//////wAAWD98bmk//////wAAWD98bmk//////wAAWD98bmk//////wAAWD98bmk//////wAAWD9a22s//////wAAWD9a22s//////wAAGD9a2ys//////wAAWD9a22s//////wAAWD9a22s//////wAAGD9a2ys//////wAAWD9a22s//////wAAGD9a2ys//////wAAWD9a22s//////wAAWD9a22s//////wAAGD9a2ys//////wAAWD9a22s//////wAAWD9a22s//////wAAWD9a22s//////wAAGD9a2ys//////wAAWD9a22s//////wAAWD9a22s//////wAAGD9a2ys//////wAAWD9a22s//////wAAWD9a22s//////wAAWD9a22s//////wAAWD9a22s//////wAAWD9a22s//////wAAWD9a22s//////wAAWD98bmk//////wAAGD98bik//////wAAWD98bmk//////wAAWD98bmk//////wAAWD98bmk//////wAAWD98bmk//////wAAWD98bmk//////wAAGD98bik//////wAAGD98bik//////wAAWD98bmk//////wAAWD98bmk//////wAAGD98bik//////wAAWD98bmk//////wAAWD98bmk//////wAAGD98bik//////wAAWD98bmk//////wAAGD98bik//////wAAGD98bik//////wAAWD98bmk//////wAAGD98bik//////wAAWD98bmk//////wAAWD98bmk//////wAAWD98bmk//////wAAGD98bik//////wAAWD98bmk//////wAAGD98bik//////wAAGD98bik//////wAAWD98bmk//////wAAWD98bmk//////wAAWD98bmk//////wAAWD98bmk//////wAAGD98bik//////wAAWD98bmk//////wAAWD9a22s//////wAAWD9a22s//////wAAWD9a22s//////wAAWD9a22s//////wAAGD9a2ys//////wAAWD9a22s//////wAAWD9a22s//////wAAWD9a22s//////wAAWD9a22s//////wAAWD9a22s//////wAAGD9a2ys//////wAAWD9a22s//////wAAWD98bmk//////wAAWD98bmk//////wAAWD98bmk//////wAAWD98bmk//////wAAWD98bmk//////wAAWD98bmk//////wAAWD98bmk//////wAAGD98bik//////wAAWD98bmk//////wAAWD98bmk//////wAAGD98bik//////wAAGD98bik//////wAAWD98bmk//////wAAWD98bmk//////wAAGD98bik//////wAAWD98bmk//////wAAWD98bmk//////wAAWD98bmk//////wAAGD+pMiY//////wAAGD+pMiY//////wAAGD+pMiY//////wAAGD+pMiY//////wAAGD+pMiY//////wAAGD+pMiY//////wAAGD+pMiY//////wAAGD+pMiY//////wAAGD+pMiY//////wAAGD+pMiY//////wAAGD+pMiY//////wAAGD+pMiY//////wAAGD9mZgY//////wAAGD9mZgY//////wAAGD9mZgY//////wAAGD9mZgY//////wAAGD9mZgY//////wAAGD9mZgY//////wAAGD9mZgY//////wAAGD9mZgY//////wAAGD9mZgY//////wAAGD9mZgY//////wAAGD9mZgY//////wAAGD9mZgY//////wAAGD+pMiY//////wAAGD+pMiY//////wAAGD+pMiY//////wAAGD+pMiY//////wAAGD9OVxo//////wAAGD9OVxo//////wAAGD9OVxo//////wAAGD9OVxo//////wAAGD9OVxo//////wAAGD9OVxo//////wAAGD9OVxo//////wAAGD9OVxo//////wAAGD9OVxo//////wAAGD9OVxo//////wAAGD9OVxo//////wAAGD9OVxo//////wAAGD9OVxo//////wAAGD9OVxo//////wAAGD9OVxo//////wAAGD9OVxo/"),
"format": 34359742495,
"index_count": 348,
"index_data": PackedByteArray("CgACAAYABgANAAoAFAAdABgAGgAmACMAGgAjACkAEQApACwALAAvABEADAAEABYABQA6ADcAAAAJABMAEwAOAAAAPwAnACEAIQBCAD8AMAA2AEgAUQBLAE4AJABmAGMAJAAzAGYAMwBpAGYAPAA5AG8AcwBVAE8ATwBtAHMAKwAoAEAAfQB6AFwAXABZAH0AewCBAH4AZABDACIAPgB3AEoASgA4AD4ADwB/AIUAfwCIAIUAeQB/AA8ADwAuAHkALgBbAHkAjACJAIAABwADAIYAlQBTAIYAVgB0AAcAdAA7AAcAcACbAGoAagBGAEkAmQChAF8AYgBBAJkAqACiAKUArwCOAIsAWABeAK8AXgCfAK8AfABYAIsAnQBxAG4AngC3ALMAlgCaALsAuwC/AJYAqQCyALYApgC+ALoAqgCtALwAGQAQABUAJgAgACMAEQAaACkAFgAcAAwAJQAXAAUABQAxACUAMQA0ACUANwAxAAUAOgA9ADcASABFADAATgBUAFEAXQBXAFoAXQBaAC0ALQAqAF0AYABdACoAYwAeACQAcgBsAG8AOQByAG8AbwB1ADwAQABhACsAMgBHAGsAawA1ADIAfgB4AHsAIgAfAGQACwAbABIAEgAIAAsAhQABAA8AhACHAIoAigCNAIQAjQCQAIQAkACTAIQAkQBMAFIAUgCUAJEAgACDAIwAhgBTAAcAUwBWAAcAlwBnAGoAagCbAJcAdgBwAGoASQB2AGoAXwBiAJkAQQBlAJkAZQBoAJkARABlAEEApQCrAKgAiwBYAK8AiwCCAHwAUABNAJIAbgBQAJIAnQBuAJIAsQCdAJIAkgCPALEAswCuAJ4AuQCgAJgAmADBALkAnACwALUAtQC9AJwAtgCjAKkAugCsAKYAvAC0AKoApAC4AMAAwACnAKQA"),
"material": ExtResource("1_q7mu6"),
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 194,
"vertex_data": PackedByteArray("AACAwAAAgMDNzEy/AACAwAAAgMDNzEy/AACAwAAAgMDNzEy/AACAwAAAgMDNzEy/AACAwAAAgEDNzEy/AACAwAAAgEDNzEy/AACAwAAAgEDNzEy/AACAwAAAgEDNzEy/exRuwHsUbsAAAACAexRuwHsUbsAAAACAexRuwHsUbsAAAACAexRuwHsUbkAAAACAexRuwHsUbkAAAACAexRuwHsUbkAAAACAAACAQAAAgMDNzEy/AACAQAAAgMDNzEy/AACAQAAAgMDNzEy/AACAQAAAgMDNzEy/exRuQHsUbsAAAACAexRuQHsUbsAAAACAexRuQHsUbsAAAACAexRuQHsUbsAAAACAAACAQAAAgEDNzEy/AACAQAAAgEDNzEy/AACAQAAAgEDNzEy/AACAQAAAgEDNzEy/AACAQAAAgEDNzEy/exRuQHsUbkAAAACAexRuQHsUbkAAAACAexRuQHsUbkAAAACAAACAQAAAAECamRnAAACAQAAAAECamRnAAACAQAAAAECamRnAAACAQAAAAEBcjwLAAACAQAAAAEBcjwLAAACAQAAAAEBcjwLAAACAQAAAgECamRnAAACAQAAAgECamRnAAACAQAAAgECamRnAAACAQAAAAMBcjwLAAACAQAAAAMBcjwLAAACAQAAAAMBcjwLAAACAQAAAAMCamRnAAACAQAAAAMCamRnAAACAQAAAAMCamRnAAACAQAAAgMCamRnAAACAQAAAgMCamRnAAACAQAAAgMCamRnAAAAAQAAAgEBcjwLAAAAAQAAAgEBcjwLAAAAAQAAAgEBcjwLAAAAAQAAAgECamRnAAAAAQAAAgECamRnAAAAAQAAAgECamRnAAAAAwAAAgEBcjwLAAAAAwAAAgEBcjwLAAAAAwAAAgEBcjwLAAACAwAAAgECamRnAAACAwAAAgECamRnAAACAwAAAgECamRnAAAAAwAAAgECamRnAAAAAwAAAgECamRnAAAAAwAAAgECamRnAUptUQAAAAMBcjwLAUptUQAAAAMBcjwLAUptUQAAAAMBcjwLAUptUQAAAAEBcjwLAUptUQAAAAEBcjwLAUptUQAAAAEBcjwLAAAAAQFKbVEBcjwLAAAAAQFKbVEBcjwLAAAAAQFKbVEBcjwLAAAAAwFKbVEBcjwLAAAAAwFKbVEBcjwLAAAAAwFKbVEBcjwLAUptUwAAAAMBcjwLAUptUwAAAAMBcjwLAUptUwAAAAMBcjwLAUptUwAAAAEBcjwLAUptUwAAAAEBcjwLAUptUwAAAAEBcjwLAAACAwAAAAMBcjwLAAACAwAAAAMBcjwLAAACAwAAAAMBcjwLAAACAwAAAAEBcjwLAAACAwAAAAEBcjwLAAACAwAAAAEBcjwLAAAAAQFKbVMCamRnAAAAAQFKbVMCamRnAAAAAQFKbVMCamRnAAAAAQAAAgMCamRnAAAAAQAAAgMCamRnAAAAAQAAAgMCamRnAUptUQFKbVMCamRnAUptUQFKbVMCamRnAUptUQFKbVMCamRnAUptUQAAAAMCamRnAUptUQAAAAMCamRnAUptUQAAAAMCamRnAUptUQAAAAECamRnAUptUQAAAAECamRnAUptUQAAAAECamRnAUptUQFKbVECamRnAUptUQFKbVECamRnAUptUQFKbVECamRnAAAAAQFKbVECamRnAAAAAQFKbVECamRnAAAAAQFKbVECamRnAUptUwAAAAECamRnAUptUwAAAAECamRnAUptUwAAAAECamRnAUptUwFKbVECamRnAUptUwFKbVECamRnAUptUwFKbVECamRnAAACAwAAAAECamRnAAACAwAAAAECamRnAAACAwAAAAECamRnAAAAAwFKbVECamRnAAAAAwFKbVECamRnAAAAAwFKbVECamRnAAAAAQAAAgMBcjwLAAAAAQAAAgMBcjwLAAAAAQAAAgMBcjwLAAAAAQFKbVMBcjwLAAAAAQFKbVMBcjwLAAAAAQFKbVMBcjwLAAAAAwAAAgMBcjwLAAAAAwAAAgMBcjwLAAAAAwAAAgMBcjwLAAAAAwFKbVMBcjwLAAAAAwFKbVMBcjwLAAAAAwFKbVMBcjwLAAACAwAAAgMCamRnAAACAwAAAgMCamRnAAACAwAAAgMCamRnAAAAAwAAAgMCamRnAAAAAwAAAgMCamRnAAAAAwAAAgMCamRnAAAAAwFKbVMCamRnAAAAAwFKbVMCamRnAAAAAwFKbVMCamRnAUptUwFKbVMCamRnAUptUwFKbVMCamRnAUptUwFKbVMCamRnAUptUwAAAAMCamRnAUptUwAAAAMCamRnAUptUwAAAAMCamRnAAACAwAAAAMCamRnAAACAwAAAAMCamRnAAACAwAAAAMCamRnAUptUQFKbVEDsUTjAUptUQFKbVEDsUTjAUptUQFKbVEDsUTjAUptUQFKbVEDsUTjAUptUwFKbVEDsUTjAUptUwFKbVEDsUTjAUptUwFKbVEDsUTjAUptUwFKbVEDsUTjAUptUQFKbVMDsUTjAUptUQFKbVMDsUTjAUptUQFKbVMDsUTjAUptUQFKbVMDsUTjAcxNTPnMTU74yM/PAcxNTPnMTU74yM/PAcxNTPnMTU74yM/PAcxNTPnMTUz4yM/PAcxNTPnMTUz4yM/PAcxNTPnMTUz4yM/PAcxNTvnMTU74yM/PAcxNTvnMTU74yM/PAcxNTvnMTU74yM/PAcxNTvnMTUz4yM/PAcxNTvnMTUz4yM/PAcxNTvnMTUz4yM/PAUptUwFKbVMDsUTjAUptUwFKbVMDsUTjAUptUwFKbVMDsUTjAUptUwFKbVMDsUTjArIMlwKyDJcDhepTArIMlwKyDJcDhepTArIMlwKyDJcDhepTArIMlwKyDJcDhepTArIMlQKyDJcDhepTArIMlQKyDJcDhepTArIMlQKyDJcDhepTArIMlQKyDJcDhepTArIMlwKyDJUDhepTArIMlwKyDJUDhepTArIMlwKyDJUDhepTArIMlwKyDJUDhepTArIMlQKyDJUDhepTArIMlQKyDJUDhepTArIMlQKyDJUDhepTArIMlQKyDJUDhepTA/38vIf///z//fwAA////vy8h/3////8/AAD/f////7//f8/e////P/9///////+/LyH/f////z8AAP9/////v/9//3////8//38vIf///z8vIf9/////P/9//3////8//3/P3v///z8vIf9/////P/9/LyH///8//38AAP///7/Q3v9/////P////3////+//3//f////z//fy8h////P8/e/3////8/0N7/f////z//f8/e////P/9///////+/z97/f////z/Q3v9/////P////3////+//3//f////z//f8/e////P8/e/3////8//////////7//fwAA////v////3////+//////////7//fwAA////v////3////+//////////7//f///////v////3////+//////////7//f///////v////3////+//////////7//f///////v////3////+//////////7//fwAA////v////3////+//////////7//f///////vwAA/3////+//////////7//f///////vwAA/3////+//////////7//f///////v////3////+//////////7//f///////vwAA/3////+//////////7//f///////v////3////+//////////7//f///////v////3////+//////////7//fwAA////v////3////+//////////7//f///////vwAA/3////+//////////7//f///////v////3////+//////////7//f///////vwAA/3////+//////////7//fwAA////vwAA/3////+//////////7//f///////vwAA/3////+//////////7//fwAA////vwAA/3////+//////////7//fwAA////vwAA/3////+//////////7//fwAA////vwAA/3////+//////////7//fwAA////v////3////+//////////7//f///////v////3////+//////////7//fwAA////v////3////+//////////7//f///////v////3////+//////////7//f///////vwAA/3////+//////////7//fwAA////vwAA/3////+//////////7//f///////vwAA/3////+//////////7//fwAA////vwAA/3////+//////////7//f///////v////3////+//////////7//fwAA////vwAA/3////+//////////7//fwAA////vwAA/3////+//////////7//fwAA////v////3////+//////////7//fwAA////v////3////+//////////7//fwAA////vwAA/3////+//////////7//fwAA////v////3////+//////////7//fwAA////v////3////+//////////7//fwAA////vwAA/3////+//////////7//f///////vwAA/3////+//////////7//f///////vwAA/3////+/u6X//////7//f///////v///u6X///+/////f////7+7pf//////v/9///////+/AAC7pf///78AAP9/////v7ulAAD///+//38AAP///7///7ul////v////3////+//////////78LuQAA////v///C7n///+//////////78Luf//////v///C7n///+//////////78LuQAA////vwAAC7n///+//////////78Luf//////vwAAC7n///+/u6UAAP///7//fwAA////vwAAu6X///+/AAD/f////78LuQAA////v7ulAAD///+/AAALuf///78AALul////vwu5AAD///+/u6UAAP///7///wu5////v///u6X///+/C7n//////7+7pf//////vwAAC7n///+/AAC7pf///78Luf//////v7ul//////+///8Luf///7///7ul////vw==")
}]
blend_shape_mode = 0
shadow_mesh = SubResource("ArrayMesh_f7ipl")

[sub_resource type="ConcavePolygonShape3D" id="ConcavePolygonShape3D_khtr5"]
data = PackedVector3Array(-3.72, -3.72, 0, -4, -4, -0.8, -4, 4, -0.8, -4, 4, -0.8, -3.72, 3.72, 0, -3.72, -3.72, 0, 3.72, -3.72, 0, 3.72, 3.72, 0, 4, 4, -0.8, 4, 4, -0.8, 4, 4, -2.4, 4, 2, -2.04, 4, 4, -0.8, 4, 2, -2.04, 4, -2, -2.04, 4, -4, -0.8, 4, -2, -2.04, 4, -2, -2.4, 4, -2, -2.4, 4, -4, -2.4, 4, -4, -0.8, -3.72, 3.72, 0, -4, 4, -0.8, 4, 4, -0.8, -4, 4, -0.8, -4, 4, -2.4, -2, 4, -2.04, -4, -4, -0.8, -3.72, -3.72, 0, 3.72, -3.72, 0, 3.72, -3.72, 0, 4, -4, -0.8, -4, -4, -0.8, 3.322, -2, -2.04, 4, -2, -2.04, 4, 2, -2.04, 4, 2, -2.04, 3.322, 2, -2.04, 3.322, -2, -2.04, 2, 4, -2.04, -2, 4, -2.04, -2, 3.322, -2.04, -4, -2, -2.04, -3.322, -2, -2.04, -3.322, 2, -2.04, 4, 4, -2.4, 3.322, 3.322, -2.4, 3.322, 2, -2.4, 4, 4, -2.4, 2, 4, -2.4, 3.322, 3.322, -2.4, 2, 4, -2.4, 2, 3.322, -2.4, 3.322, 3.322, -2.4, -2, 4, -2.4, -4, 4, -2.4, -3.322, 3.322, -2.4, -4, 2, -2.4, -4, 2, -2.04, -3.322, 2, -2.04, -3.322, 2, -2.04, -3.322, 2, -2.4, -4, 2, -2.4, 4, -2, -2.4, 4, -2, -2.04, 3.322, -2, -2.04, 2, -3.322, -2.04, 2, -4, -2.04, 2, -4, -2.4, 2, -4, -2.4, 2, -3.322, -2.4, 2, -3.322, -2.04, 2, -3.322, -2.04, -2, -3.322, -2.04, -2, -4, -2.04, 3.322, 2, -2.4, 3.322, 2, -2.04, 4, 2, -2.04, -2, 4, -2.4, -2, 3.322, -2.4, -2, 3.322, -2.04, -2, 3.322, -2.04, -2, 4, -2.04, -2, 4, -2.4, 4, -4, -0.8, -2, -4, -2.04, -4, -4, -2.4, -2, -4, -2.04, -2, -4, -2.4, -4, -4, -2.4, 2, -4, -2.04, -2, -4, -2.04, 4, -4, -0.8, 4, -4, -0.8, 4, -4, -2.4, 2, -4, -2.04, 4, -4, -2.4, 2, -4, -2.4, 2, -4, -2.04, -2, -3.322, -2.4, -2, -4, -2.4, -2, -4, -2.04, -4, 4, -0.8, -4, -4, -0.8, -4, -4, -2.4, -4, -2, -2.4, -4, -2, -2.04, -4, -4, -2.4, -4, 2, -2.04, -4, 2, -2.4, -4, 4, -0.8, -4, 2, -2.4, -4, 4, -2.4, -4, 4, -0.8, -3.322, 3.322, -2.4, -3.322, 3.322, -2.88, 2, 3.322, -2.4, 2, 3.322, -2.4, 2, 3.322, -2.04, -2, 3.322, -2.04, 3.322, 3.322, -2.88, 3.322, -3.322, -2.88, 3.322, -3.322, -2.4, 3.322, -2, -2.4, 3.322, -2, -2.04, 3.322, 3.322, -2.88, -0.2061, -0.2061, -7.6, 0.2061, -0.2061, -7.6, 0.2061, 0.2061, -7.6, -3.322, -3.322, -2.88, -3.322, -3.322, -2.4, -2, -3.322, -2.4, 2, -3.322, -2.4, 3.322, -3.322, -2.4, -3.322, -3.322, -2.88, 3.322, -3.322, -2.4, 3.322, -3.322, -2.88, -3.322, -3.322, -2.88, 2, -3.322, -2.04, 2, -3.322, -2.4, -2, -3.322, -2.4, -3.322, 3.322, -2.88, -3.322, 3.322, -2.4, -3.322, 2, -2.4, 3.322, -3.322, -2.88, 2.5862, -2.5862, -4.64, -2.5862, -2.5862, -4.64, 3.322, 3.322, -2.88, -3.322, 3.322, -2.88, -2.5862, 2.5862, -4.64, -2.5862, 2.5862, -4.64, 2.5862, 2.5862, -4.64, 3.322, 3.322, -2.88, -0.2061, -0.2061, -7.6, -2.5862, -2.5862, -4.64, 2.5862, -2.5862, -4.64, 0.2061, 0.2061, -7.6, 2.5862, 2.5862, -4.64, -2.5862, 2.5862, -4.64, -0.2061, -0.2061, -7.6, -0.2061, 0.2061, -7.6, -2.5862, 2.5862, -4.64, 4, 4, -0.8, 4, -4, -0.8, 3.72, -3.72, 0, 4, 4, -2.4, 4, 2, -2.4, 4, 2, -2.04, 4, -4, -0.8, 4, 4, -0.8, 4, -2, -2.04, 4, 4, -0.8, 3.72, 3.72, 0, -3.72, 3.72, 0, 4, 4, -2.4, 4, 4, -0.8, -4, 4, -0.8, -4, 4, -0.8, 2, 4, -2.04, 4, 4, -2.4, 2, 4, -2.04, 2, 4, -2.4, 4, 4, -2.4, -2, 4, -2.04, 2, 4, -2.04, -4, 4, -0.8, -4, 4, -2.4, -2, 4, -2.4, -2, 4, -2.04, -2, 3.322, -2.04, 2, 3.322, -2.04, 2, 4, -2.04, -3.322, 2, -2.04, -4, 2, -2.04, -4, -2, -2.04, 3.322, -3.322, -2.4, 2, -3.322, -2.4, 2, -4, -2.4, 3.322, -3.322, -2.4, 2, -4, -2.4, 4, -4, -2.4, 4, -4, -2.4, 4, -2, -2.4, 3.322, -3.322, -2.4, 3.322, -2, -2.4, 3.322, -3.322, -2.4, 4, -2, -2.4, 3.322, 2, -2.4, 4, 2, -2.4, 4, 4, -2.4, -4, 2, -2.4, -3.322, 2, -2.4, -3.322, 3.322, -2.4, -4, 4, -2.4, -4, 2, -2.4, -3.322, 3.322, -2.4, -3.322, 3.322, -2.4, -2, 3.322, -2.4, -2, 4, -2.4, 3.322, -2, -2.04, 3.322, -2, -2.4, 4, -2, -2.4, 2, 4, -2.04, 2, 3.322, -2.04, 2, 3.322, -2.4, 2, 3.322, -2.4, 2, 4, -2.4, 2, 4, -2.04, -2, -4, -2.04, 2, -4, -2.04, 2, -3.322, -2.04, 4, 2, -2.04, 4, 2, -2.4, 3.322, 2, -2.4, -3.72, 3.72, 0, 3.72, 3.72, 0, 3.72, -3.72, 0, 3.72, -3.72, 0, -3.72, -3.72, 0, -3.72, 3.72, 0, -4, -4, -2.4, -4, -4, -0.8, 4, -4, -0.8, -4, -4, -2.4, -2, -4, -2.4, -2, -3.322, -2.4, -2, -3.322, -2.4, -3.322, -3.322, -2.4, -4, -4, -2.4, -3.322, -3.322, -2.4, -3.322, -2, -2.4, -4, -4, -2.4, -3.322, -2, -2.4, -4, -2, -2.4, -4, -4, -2.4, -3.322, -2, -2.4, -3.322, -2, -2.04, -4, -2, -2.04, -4, -2, -2.04, -4, -2, -2.4, -3.322, -2, -2.4, -2, -4, -2.04, -2, -3.322, -2.04, -2, -3.322, -2.4, -4, -4, -2.4, -4, -2, -2.04, -4, 4, -0.8, -4, -2, -2.04, -4, 2, -2.04, -4, 4, -0.8, 3.322, 3.322, -2.88, 3.322, 3.322, -2.4, 2, 3.322, -2.4, 2, 3.322, -2.4, -3.322, 3.322, -2.88, 3.322, 3.322, -2.88, -2, 3.322, -2.4, -3.322, 3.322, -2.4, 2, 3.322, -2.4, -2, 3.322, -2.04, -2, 3.322, -2.4, 2, 3.322, -2.4, 3.322, -3.322, -2.4, 3.322, -2, -2.4, 3.322, 3.322, -2.88, 3.322, -2, -2.04, 3.322, 2, -2.4, 3.322, 3.322, -2.88, 3.322, 2, -2.4, 3.322, 3.322, -2.4, 3.322, 3.322, -2.88, 3.322, 2, -2.04, 3.322, 2, -2.4, 3.322, -2, -2.04, 0.2061, 0.2061, -7.6, -0.2061, 0.2061, -7.6, -0.2061, -0.2061, -7.6, -2, -3.322, -2.4, 2, -3.322, -2.4, -3.322, -3.322, -2.88, -2, -3.322, -2.4, -2, -3.322, -2.04, 2, -3.322, -2.04, -3.322, 2, -2.04, -3.322, -2, -2.04, -3.322, -2, -2.4, -3.322, 2, -2.4, -3.322, 2, -2.04, -3.322, -2, -2.4, -3.322, 3.322, -2.88, -3.322, 2, -2.4, -3.322, -2, -2.4, -3.322, -3.322, -2.88, -3.322, 3.322, -2.88, -3.322, -2, -2.4, -3.322, -2, -2.4, -3.322, -3.322, -2.4, -3.322, -3.322, -2.88, -2.5862, -2.5862, -4.64, -3.322, -3.322, -2.88, 3.322, -3.322, -2.88, 2.5862, -2.5862, -4.64, 3.322, -3.322, -2.88, 3.322, 3.322, -2.88, 3.322, 3.322, -2.88, 2.5862, 2.5862, -4.64, 2.5862, -2.5862, -4.64, -3.322, 3.322, -2.88, -3.322, -3.322, -2.88, -2.5862, -2.5862, -4.64, -2.5862, -2.5862, -4.64, -2.5862, 2.5862, -4.64, -3.322, 3.322, -2.88, 2.5862, -2.5862, -4.64, 0.2061, -0.2061, -7.6, -0.2061, -0.2061, -7.6, -2.5862, 2.5862, -4.64, -0.2061, 0.2061, -7.6, 0.2061, 0.2061, -7.6, -2.5862, 2.5862, -4.64, -2.5862, -2.5862, -4.64, -0.2061, -0.2061, -7.6, 0.2061, -0.2061, -7.6, 2.5862, -2.5862, -4.64, 2.5862, 2.5862, -4.64, 2.5862, 2.5862, -4.64, 0.2061, 0.2061, -7.6, 0.2061, -0.2061, -7.6)

[node name="tower-square-top-roof-rounded" type="MeshInstance3D"]
transform = Transform3D(1, 0, 0, 0, 0, -1, 0, 1, 0, 0, 0, 0)
mesh = SubResource("ArrayMesh_votg2")
skeleton = NodePath("")

[node name="StaticBody3D" type="StaticBody3D" parent="."]
collision_layer = 2

[node name="CollisionShape3D" type="CollisionShape3D" parent="StaticBody3D"]
shape = SubResource("ConcavePolygonShape3D_khtr5")
