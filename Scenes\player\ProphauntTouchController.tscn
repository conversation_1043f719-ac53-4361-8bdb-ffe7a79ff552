[gd_scene load_steps=17 format=3 uid="uid://cnxwv222lmpvj"]

[ext_resource type="Script" path="res://Scenes/player/ProphauntTouchController.gd" id="1_bscti"]
[ext_resource type="Texture2D" uid="uid://c0wwn07tvaobr" path="res://Scenes/ui/assets/Base.png" id="2_qoo6y"]
[ext_resource type="Texture2D" uid="uid://dhiqstiqvh5wt" path="res://Scenes/ui/assets/front.png" id="3_73avf"]
[ext_resource type="Texture2D" uid="uid://b4vx75hnc4fv1" path="res://Scenes/ui/assets/action.png" id="4_tbd7q"]
[ext_resource type="Texture2D" uid="uid://bdy5u6cbmnoqc" path="res://Scenes/ui/assets/jump.png" id="5_85ng2"]
[ext_resource type="Texture2D" uid="uid://c58ekxw3b2tuj" path="res://prophaunt/assets/ui/k1.png" id="6_63xba"]
[ext_resource type="Texture2D" uid="uid://bewim6tgcivjq" path="res://prophaunt/assets/ui/i1.png" id="6_n0t2c"]
[ext_resource type="Texture2D" uid="uid://bipr466sq685g" path="res://Scenes/ui/assets/holdster.png" id="6_piufh"]
[ext_resource type="Texture2D" uid="uid://bjgyqauimohe5" path="res://Scenes/ui/assets/shoot.png" id="7_qo80o"]
[ext_resource type="FontFile" uid="uid://djnxbn02v2032" path="res://Scenes/ui/assets/Font/Lalezar-Regular.ttf" id="8_1ptln"]
[ext_resource type="Texture2D" uid="uid://bdq65gedholbd" path="res://prophaunt/assets/ui/j1.png" id="8_hkryv"]
[ext_resource type="Texture2D" uid="uid://ccciwf7u4eqhn" path="res://Scenes/ui/assets/arrest.png" id="9_s8v7t"]
[ext_resource type="Texture2D" uid="uid://dis4uxy8abur3" path="res://Scenes/ui/assets/aim.png" id="10_vyw2o"]
[ext_resource type="Texture2D" uid="uid://cye5xouwx4r2k" path="res://Scenes/ui/assets/snowball.png" id="11_rhnt6"]
[ext_resource type="Texture2D" uid="uid://bfrc43inj20yc" path="res://Scenes/ui/assets/syring.png" id="12_mukh6"]
[ext_resource type="Texture2D" uid="uid://d3x37gwwtpiua" path="res://Scenes/ui/assets/action_button_bg.png" id="13_1u6vy"]

[node name="ProphauntTouchController" type="Control"]
custom_minimum_size = Vector2(160, 160)
layout_direction = 2
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
size_flags_horizontal = 3
size_flags_vertical = 3
mouse_filter = 2
script = ExtResource("1_bscti")

[node name="FullScreen" type="Control" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 500.0
offset_top = 165.0
offset_right = 500.0
grow_horizontal = 2
grow_vertical = 2
mouse_filter = 1

[node name="Controller" type="TextureRect" parent="."]
layout_mode = 1
anchors_preset = 2
anchor_top = 1.0
anchor_bottom = 1.0
offset_left = 56.0
offset_top = -272.0
offset_right = 293.0
offset_bottom = -35.0
grow_vertical = 0
mouse_force_pass_scroll_events = false
texture = ExtResource("2_qoo6y")
expand_mode = 2

[node name="fg" type="TouchScreenButton" parent="Controller"]
position = Vector2(35.5, 33.5)
scale = Vector2(0.75, 0.75)
texture_normal = ExtResource("3_73avf")

[node name="ActionButton" type="TextureRect" parent="."]
visible = false
layout_mode = 1
anchors_preset = 3
anchor_left = 1.0
anchor_top = 1.0
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = -274.0
offset_top = -482.0
offset_right = -2.0
offset_bottom = -286.0
grow_horizontal = 0
grow_vertical = 0
texture = ExtResource("4_tbd7q")
expand_mode = 1
stretch_mode = 3

[node name="JoystickControl" type="Control" parent="."]
layout_mode = 1
anchors_preset = 9
anchor_bottom = 1.0
offset_right = 530.0
grow_vertical = 2

[node name="HaunterParent" type="Control" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
mouse_filter = 2
metadata/_edit_lock_ = true

[node name="aim" type="TextureRect" parent="HaunterParent"]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -7.0
offset_top = -44.0
offset_right = 33.0
offset_bottom = -4.0
grow_horizontal = 2
grow_vertical = 2
texture = ExtResource("10_vyw2o")
expand_mode = 1
stretch_mode = 5

[node name="GrenadeButton" type="TextureRect" parent="HaunterParent"]
layout_mode = 1
anchors_preset = 3
anchor_left = 1.0
anchor_top = 1.0
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = -274.0
offset_top = -482.0
offset_right = -2.0
offset_bottom = -286.0
grow_horizontal = 0
grow_vertical = 0
texture = ExtResource("6_63xba")
expand_mode = 1
stretch_mode = 3

[node name="ShootButton" type="TextureRect" parent="HaunterParent"]
layout_mode = 1
anchors_preset = 3
anchor_left = 1.0
anchor_top = 1.0
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = -410.0
offset_top = -234.0
offset_right = -271.0
offset_bottom = -78.0
grow_horizontal = 0
grow_vertical = 0
texture = ExtResource("6_n0t2c")
expand_mode = 1
stretch_mode = 3

[node name="Jump" type="TextureRect" parent="HaunterParent"]
layout_mode = 1
anchors_preset = 3
anchor_left = 1.0
anchor_top = 1.0
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = -250.0
offset_top = -279.0
offset_right = -6.0
offset_bottom = -33.0
grow_horizontal = 0
grow_vertical = 0
expand_mode = 1
stretch_mode = 3

[node name="TextureRect" type="TextureRect" parent="HaunterParent/Jump"]
custom_minimum_size = Vector2(150, 150)
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -77.0
offset_top = -75.0
offset_right = 73.0
offset_bottom = 75.0
grow_horizontal = 2
grow_vertical = 2
mouse_filter = 2
texture = ExtResource("8_hkryv")
expand_mode = 1
stretch_mode = 5

[node name="PropParent" type="Control" parent="."]
visible = false
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
mouse_filter = 2
metadata/_edit_lock_ = true

[node name="aim" type="TextureRect" parent="PropParent"]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -7.0
offset_top = -44.0
offset_right = 33.0
offset_bottom = -4.0
grow_horizontal = 2
grow_vertical = 2
texture = ExtResource("10_vyw2o")
expand_mode = 1
stretch_mode = 5

[node name="GrenadeButton" type="TextureRect" parent="PropParent"]
layout_mode = 1
anchors_preset = 3
anchor_left = 1.0
anchor_top = 1.0
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = -274.0
offset_top = -482.0
offset_right = -2.0
offset_bottom = -286.0
grow_horizontal = 0
grow_vertical = 0
texture = ExtResource("4_tbd7q")
expand_mode = 1
stretch_mode = 3

[node name="ShootButton" type="TextureRect" parent="PropParent"]
layout_mode = 1
anchors_preset = 3
anchor_left = 1.0
anchor_top = 1.0
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = -410.0
offset_top = -234.0
offset_right = -271.0
offset_bottom = -78.0
grow_horizontal = 0
grow_vertical = 0
texture = ExtResource("4_tbd7q")
expand_mode = 1
stretch_mode = 3

[node name="TextureRect" type="TextureRect" parent="PropParent"]
layout_mode = 1
anchors_preset = 3
anchor_left = 1.0
anchor_top = 1.0
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = -250.0
offset_top = -279.0
offset_right = -6.0
offset_bottom = -33.0
grow_horizontal = 0
grow_vertical = 0
expand_mode = 1
stretch_mode = 3

[node name="TextureRect" type="TextureRect" parent="PropParent/TextureRect"]
custom_minimum_size = Vector2(150, 150)
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -77.0
offset_top = -75.0
offset_right = 73.0
offset_bottom = 75.0
grow_horizontal = 2
grow_vertical = 2
texture = ExtResource("5_85ng2")
expand_mode = 1
stretch_mode = 5

[node name="ArmParent" type="Control" parent="."]
visible = false
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
mouse_filter = 2
metadata/_edit_lock_ = true

[node name="HoldsterButton" type="Button" parent="ArmParent"]
layout_mode = 1
anchors_preset = 3
anchor_left = 1.0
anchor_top = 1.0
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = -351.0
offset_top = -337.0
offset_right = -257.0
offset_bottom = -226.0
grow_horizontal = 0
grow_vertical = 0
focus_mode = 0
mouse_filter = 1
action_mode = 0
flat = true

[node name="TextureRect" type="TextureRect" parent="ArmParent/HoldsterButton"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
texture = ExtResource("6_piufh")
expand_mode = 1
stretch_mode = 5

[node name="ShootButton" type="Button" parent="ArmParent"]
visible = false
layout_mode = 1
anchors_preset = 3
anchor_left = 1.0
anchor_top = 1.0
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = -376.0
offset_top = -481.0
offset_right = -233.0
offset_bottom = -359.0
grow_horizontal = 0
grow_vertical = 0
focus_mode = 0
mouse_filter = 1
action_mode = 0
flat = true

[node name="TextureRect" type="TextureRect" parent="ArmParent/ShootButton"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
texture = ExtResource("7_qo80o")
expand_mode = 1
stretch_mode = 5

[node name="ammo" type="Label" parent="ArmParent/ShootButton"]
layout_mode = 1
anchors_preset = 3
anchor_left = 1.0
anchor_top = 1.0
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = -60.0
offset_top = -40.0
offset_right = -24.0
offset_bottom = -6.0
grow_horizontal = 0
grow_vertical = 0
theme_override_colors/font_shadow_color = Color(0, 0, 0, 1)
theme_override_colors/font_outline_color = Color(0, 0, 0, 1)
theme_override_constants/outline_size = 2
theme_override_constants/shadow_outline_size = 2
theme_override_fonts/font = ExtResource("8_1ptln")
theme_override_font_sizes/font_size = 16
text = "10"
horizontal_alignment = 1
vertical_alignment = 1

[node name="ArrestButton" type="Button" parent="ArmParent"]
layout_mode = 1
anchors_preset = 3
anchor_left = 1.0
anchor_top = 1.0
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = -376.0
offset_top = -481.0
offset_right = -233.0
offset_bottom = -359.0
grow_horizontal = 0
grow_vertical = 0
focus_mode = 0
mouse_filter = 1
action_mode = 0
flat = true

[node name="TextureRect" type="TextureRect" parent="ArmParent/ArrestButton"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
texture = ExtResource("9_s8v7t")
expand_mode = 1
stretch_mode = 5

[node name="aim" type="TextureRect" parent="ArmParent"]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -20.0
offset_top = -92.0
offset_right = 20.0
offset_bottom = -52.0
grow_horizontal = 2
grow_vertical = 2
texture = ExtResource("10_vyw2o")
expand_mode = 1
stretch_mode = 5

[node name="SnowballParent" type="Control" parent="."]
visible = false
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
mouse_filter = 2
metadata/_edit_lock_ = true

[node name="SnowballButton" type="Button" parent="SnowballParent"]
layout_mode = 1
anchors_preset = 3
anchor_left = 1.0
anchor_top = 1.0
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = -376.0
offset_top = -481.0
offset_right = -233.0
offset_bottom = -359.0
grow_horizontal = 0
grow_vertical = 0
focus_mode = 0
mouse_filter = 1
action_mode = 0
flat = true

[node name="TextureRect" type="TextureRect" parent="SnowballParent/SnowballButton"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
texture = ExtResource("11_rhnt6")
expand_mode = 1
stretch_mode = 5

[node name="ammo" type="Label" parent="SnowballParent/SnowballButton"]
layout_mode = 1
anchors_preset = 3
anchor_left = 1.0
anchor_top = 1.0
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = -60.0
offset_top = -40.0
offset_right = -24.0
offset_bottom = -6.0
grow_horizontal = 0
grow_vertical = 0
theme_override_colors/font_shadow_color = Color(0, 0, 0, 1)
theme_override_colors/font_outline_color = Color(0, 0, 0, 1)
theme_override_constants/outline_size = 2
theme_override_constants/shadow_outline_size = 2
theme_override_fonts/font = ExtResource("8_1ptln")
theme_override_font_sizes/font_size = 16
text = "10"
horizontal_alignment = 1
vertical_alignment = 1

[node name="aim" type="TextureRect" parent="SnowballParent"]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -7.0
offset_top = -44.0
offset_right = 33.0
offset_bottom = -4.0
grow_horizontal = 2
grow_vertical = 2
texture = ExtResource("10_vyw2o")
expand_mode = 1
stretch_mode = 5

[node name="DrugParent" type="Control" parent="."]
visible = false
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
mouse_filter = 2
metadata/_edit_lock_ = true

[node name="DrugButton" type="Button" parent="DrugParent"]
layout_mode = 1
anchors_preset = 3
anchor_left = 1.0
anchor_top = 1.0
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = -376.0
offset_top = -481.0
offset_right = -233.0
offset_bottom = -359.0
grow_horizontal = 0
grow_vertical = 0
focus_mode = 0
mouse_filter = 1
action_mode = 0
flat = true

[node name="TextureRect" type="TextureRect" parent="DrugParent/DrugButton"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
texture = ExtResource("12_mukh6")
expand_mode = 1
stretch_mode = 5

[node name="ammo" type="Label" parent="DrugParent/DrugButton"]
visible = false
layout_mode = 1
anchors_preset = 3
anchor_left = 1.0
anchor_top = 1.0
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = -60.0
offset_top = -40.0
offset_right = -24.0
offset_bottom = -6.0
grow_horizontal = 0
grow_vertical = 0
theme_override_colors/font_shadow_color = Color(0, 0, 0, 1)
theme_override_colors/font_outline_color = Color(0, 0, 0, 1)
theme_override_constants/outline_size = 2
theme_override_constants/shadow_outline_size = 2
theme_override_fonts/font = ExtResource("8_1ptln")
theme_override_font_sizes/font_size = 16
text = "10"
horizontal_alignment = 1
vertical_alignment = 1

[node name="ActionParent" type="Control" parent="."]
visible = false
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
mouse_filter = 2
metadata/_edit_lock_ = true

[node name="ActionButton" type="Button" parent="ActionParent"]
layout_mode = 1
anchors_preset = 3
anchor_left = 1.0
anchor_top = 1.0
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = -376.0
offset_top = -481.0
offset_right = -233.0
offset_bottom = -359.0
grow_horizontal = 0
grow_vertical = 0
focus_mode = 0
mouse_filter = 1
action_mode = 0
flat = true

[node name="BG" type="TextureRect" parent="ActionParent/ActionButton"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
texture = ExtResource("13_1u6vy")
expand_mode = 1
stretch_mode = 5

[node name="TextureRect" type="TextureRect" parent="ActionParent/ActionButton"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
texture = ExtResource("12_mukh6")
expand_mode = 1
stretch_mode = 5

[node name="count" type="Label" parent="ActionParent/ActionButton"]
layout_mode = 1
anchors_preset = 3
anchor_left = 1.0
anchor_top = 1.0
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = -60.0
offset_top = -40.0
offset_right = -24.0
offset_bottom = -6.0
grow_horizontal = 0
grow_vertical = 0
theme_override_colors/font_shadow_color = Color(0, 0, 0, 1)
theme_override_colors/font_outline_color = Color(0, 0, 0, 1)
theme_override_constants/outline_size = 2
theme_override_constants/shadow_outline_size = 2
theme_override_fonts/font = ExtResource("8_1ptln")
theme_override_font_sizes/font_size = 16
text = "10"
horizontal_alignment = 1
vertical_alignment = 1

[node name="TextureRect" type="TextureRect" parent="."]
visible = false
layout_mode = 1
anchors_preset = 3
anchor_left = 1.0
anchor_top = 1.0
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = -250.0
offset_top = -279.0
offset_right = -6.0
offset_bottom = -33.0
grow_horizontal = 0
grow_vertical = 0
expand_mode = 1
stretch_mode = 3

[connection signal="gui_input" from="FullScreen" to="." method="_on_full_screen_gui_input"]
[connection signal="gui_input" from="Controller" to="." method="_on_controller_gui_input"]
[connection signal="gui_input" from="ActionButton" to="." method="_on_action_button_gui_input"]
[connection signal="gui_input" from="JoystickControl" to="." method="_on_joystick_control_gui_input"]
[connection signal="gui_input" from="HaunterParent/GrenadeButton" to="." method="_on_grenade_button_gui_input"]
[connection signal="gui_input" from="HaunterParent/ShootButton" to="." method="_on_haunter_shoot_button_gui_input"]
[connection signal="gui_input" from="HaunterParent/Jump" to="." method="_on_texture_rect_gui_input"]
[connection signal="gui_input" from="PropParent/GrenadeButton" to="." method="_on_grenade_button_gui_input"]
[connection signal="gui_input" from="PropParent/ShootButton" to="." method="_on_haunter_shoot_button_gui_input"]
[connection signal="gui_input" from="PropParent/TextureRect" to="." method="_on_texture_rect_gui_input"]
[connection signal="gui_input" from="ArmParent/HoldsterButton" to="." method="_on_holdster_button_gui_input"]
[connection signal="pressed" from="ArmParent/HoldsterButton" to="." method="_on_holdster_button_pressed"]
[connection signal="gui_input" from="ArmParent/ShootButton" to="." method="_on_shoot_button_gui_input"]
[connection signal="pressed" from="ArmParent/ShootButton" to="." method="_on_shoot_button_pressed"]
[connection signal="gui_input" from="ArmParent/ArrestButton" to="." method="_on_arrest_button_gui_input"]
[connection signal="pressed" from="ArmParent/ArrestButton" to="." method="_on_arrest_button_pressed"]
[connection signal="gui_input" from="SnowballParent/SnowballButton" to="." method="_on_shoot_button_gui_input"]
[connection signal="pressed" from="SnowballParent/SnowballButton" to="." method="_on_snowball_button_pressed"]
[connection signal="gui_input" from="DrugParent/DrugButton" to="." method="_on_shoot_button_gui_input"]
[connection signal="pressed" from="DrugParent/DrugButton" to="." method="_on_drug_button_pressed"]
[connection signal="gui_input" from="ActionParent/ActionButton" to="." method="_on_shoot_button_gui_input"]
[connection signal="pressed" from="ActionParent/ActionButton" to="." method="_on_item_action_button_pressed"]
[connection signal="gui_input" from="TextureRect" to="." method="_on_texture_rect_gui_input"]
