[gd_scene load_steps=5 format=4 uid="uid://df2lgv8w4qkc6"]

[ext_resource type="Material" uid="uid://b60k1mvxam058" path="res://prophaunt/Mat/ArenaColorMap.tres" id="1_wpp6v"]

[sub_resource type="ArrayMesh" id="ArrayMesh_6umui"]
_surfaces = [{
"aabb": AABB(-1, -7.54979e-08, -1, 2, 1, 2),
"format": 34359742465,
"index_count": 252,
"index_data": PackedByteArray("AAABAAIAAwACAAEABAACAAMABQADAAEABgAFAAEABwAIAAkACgAJAAgAAQAAAAsADAALAAAADQALAAwADgAPABAAEQAQAA8AEgATABQAFQAUABMAFgAUABUAEwAXABUAGAAVABcACwANABkAGgAZAA0AGwAcAB0AHgAdABwADgAfAA8ADwAfAAgADwAIAAcAHwAMAAgAAAAIAAwACAAAAAIAGgANABMADQAMABMAHwATAAwAGwATAB8AEwAbABcAGwAfABwAIAAhACIAIwAiACEAJAAlACYAJwAmACUAJAAmACEAIwAhACYAJQAgACcAIgAnACAAJwAiACYAIwAmACIAKAApACoAKwAqACkALAAtAC4ALwAuAC0ALAAuACkAKwApAC4ALQAoAC8AKgAvACgALwAqAC4AKwAuACoAFQAwABYAMAAVABgAAwAFADEABAADADEADgAQAB8AHgAfABAAHAAfAB4AAgAEAAgAMQAIAAQACAAxAAoACgAxABIAEgAxABoAEwASABoAGgAxABkAGQAxAAYABQAGADEACgASAAkAFAAJABIAAQALAAYAGQAGAAsABwAJAA8AFAAPAAkAEQAPABQAFgARABQAHQARABYAMAAdABYAGwAdADAAGAAbADAAFwAbABgAEQAdABAAHgAQAB0A"),
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 50,
"vertex_data": PackedByteArray("AACAP2khorMAAIC/AACAP97qkTNmZmY/AACAP6iqqj4AAIC/AACAP4iICD7LzEw+AACAP6uqqj6OLNiyAACAP6yqqj7LzMw+AACAP62qqj5mZmY/q6qqPqqqKj8AAIC/q6qqPqiqqj4AAIC/q6qqPquqKj/s7m4+q6qqPqyqqj7t7m4+ZmZmP2khojMAAIA/AACAv2khorMAAIC/AACAv2khojMAAIA/q6qqvv//fz8BAIC/q6qqvqqqKj8AAIC/q6qqvv//fz/g3d2+q6qqvqqqKj/f3d2+7+5uPqyqqj6qqqo+AACAv6yqqj6qqqo+7+5uPquqKj+pqqo+mpkZv/Du7j6qqqo+zMzMvquqKj+pqqo+AACAv6uqKj+pqqo+zMxMv6uqKj+pqqo+ZmZmP66qqj4AAIA/AACAv66qqj4AAIA/AACAv6uqKj+tqqq+AACAvwAAgD+uqqq+3d3dvquqKj+tqqq+3d3dvgAAgD+uqqq+AACAv///fz8BAIC/0JgVv62qqj5OtSY/4HpHv62qqj4ZgkM/q24fv3p3xz6lEiQ/N9hEv3p3xz4+rDk/q0dkv6yqqj4KoBE/nGUyv6yqqj56puk+0HFav3l3xz6zQhQ/RQg1v3l3xz4xUv0+hnyiPaqqKj+z1Sa/+5PsvaqqKj/nCAq/T5snPQ8ROT9ceCm/uH7XvQ8ROT/C3hO/MH1pvqqqKj/26ju/wtMHvaqqKj/Dt1i/wiVCvg8ROT9OSDm/Sf4xvQ8ROT/n4U6/mpkZv6uqKj+EiAg+zMxMP6yqqj7KzEw+")
}]
blend_shape_mode = 0

[sub_resource type="ArrayMesh" id="ArrayMesh_1llan"]
resource_name = "MiniArenaStairsCorner_MiniArenaStairsCorner"
_surfaces = [{
"aabb": AABB(-1, -7.54979e-08, -1, 2, 1, 2),
"attribute_data": PackedByteArray("AAB4P5qZeT8AAHg/mpl5PwAAeD+amXk/AAB4P5qZeT8AAMA9mpl5PwAAeD+amXk/AAB4P4iIaD8AAMA9iIhoPwAAeD+IiGg/AADAPfnFcj8AAHg/+cVyPwAAwD2IiGg/AADAPYiIaD8AAHg/iIhoPwAAwD2IiGg/AADAPYiIaD8AAHg/iIhoPwAAwD2IiGg/AADAPYiIaD8AAHg/iIhoPwAAeD93d1c/AADAPXd3Vz8AAHg/d3dXPwAAeD+IiGg/AADAPYiIaD8AAHg/iIhoPwAAwD13d1c/AADAPXd3Vz8AAHg/d3dXPwAAwD2IiGg/AADAPYiIaD8AAHg/iIhoPwAAeD+amXk/AAB4P5qZeT8AAMA9mpl5PwAAeD+amXk/AAB4P5qZeT8AAHg/mpl5PwAAeD+amXk/AAB4P5qZeT8AAHg/mpl5PwAAeD9mZkY/AADAPWZmRj8AAHg/ZmZGPwAAwD13d1c/AAB4P3d3Vz8AAHg/d3dXPwAAwD1mZkY/AADAPWZmRj8AAHg/ZmZGPwAAwD13d1c/AADAPXd3Vz8AAHg/d3dXPwAAeD+IiGg/AADAPYiIaD8AAMA9iIhoPwAAeD+IiGg/AADAPYiIaD8AAHg/iIhoPwAAeD93d1c/AADAPXd3Vz8AAMA9d3dXPwAAeD/otGE/AADAPei0YT8AAHg/d3dXPwAAwD13d1c/AADAPXd3Vz8AAHg/d3dXPwAAwD13d1c/AAB4P3d3Vz8AAHg/d3dXPwAAwD13d1c/AADAPXd3Vz8AAHg/iIhoPwAAwD2IiGg/AADAPYiIaD8AAHg/iIhoPwAAwD2IiGg/AAB4P4iIaD8AAHg/d3dXPwAAwD13d1c/AAB4P3d3Vz8AAHg/ZmZGPwAAwD1mZkY/AAB4P2ZmRj8AAHg/d3dXPwAAwD13d1c/AADAPXd3Vz8AAHg/ZmZGPwAAwD1mZkY/AADAPWZmRj8AAHg/ZmZGPwAAwD1mZkY/AAB4P2ZmRj8AAHg/zcxsPwAAeD/NzGw/AAB4PzMzUz8AAHg/MzNTPwAAeD8zM1M/AAB4PzMzUz8AAHg/zcxsPwAAeD/NzGw/AAB4PzMzUz8AAHg/MzNTPwAAeD8zM1M/AAB4PzMzUz8AAHg/zcxsPwAAeD/NzGw/AAB4PzMzUz8AAHg/MzNTPwAAeD8zM1M/AAB4PzMzUz8AAHg/zcxsPwAAeD/NzGw/AAB4PzMzUz8AAHg/MzNTPwAAeD8zM1M/AAB4PzMzUz8AAMA9d3dXPwAAwD13d1c/AADAPYiIaD8AAMA9iIhoPw=="),
"format": ***********,
"index_count": 252,
"index_data": PackedByteArray("AgAFAAgACgAIAAUADQAIAAoAEAAKAAUAEwAQAAUAFgAZABwAHwAcABkAAwABACEAJAAhAAEAJwAhACQAKwAuADEANAAxAC4ANQA4ADsAPgA7ADgAQAA7AD4AOABDAD4ARgA+AEMAIAAmAEkATABJACYATwBSAFUAWABVAFIAKQBbAC0ALQBbABcALQAXABQAWwAjABcAAAAXACMAFwAAAAYATgAoADoAKAAlADoAXQA6ACUAUQA6AF0AOgBRAEUAUQBdAFQAXgBfAGEAYwBhAF8AZABlAGcAaQBnAGUAZABnAF8AYwBfAGcAZQBeAGkAYQBpAF4AaABgAGYAYgBmAGAAagBrAG0AbwBtAGsAcABxAHMAdQBzAHEAcABzAGsAbwBrAHMAcQBqAHUAbQB1AGoAdABsAHIAbgByAGwAPwB2AEIAdgA/AEgACQAPAHkADAAJAHkAKgAvAFwAWQBcAC8AUwBcAFkABwALABgAeAAYAAsAGAB4AB0AHQB4ADYANgB4AE0AOQA2AE0ATQB4AEoASgB4ABEADgARAHgAHgA3ABsAPQAbADcABAAiABIASwASACIAFQAaACwAPAAsABoAMgAsADwAQQAyADwAVgAyAEEAdwBWAEEAUABWAHcARwBQAHcARABQAEcAMwBXADAAWgAwAFcA"),
"material": ExtResource("1_wpp6v"),
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 122,
"vertex_data": PackedByteArray("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")
}]
blend_shape_mode = 0
shadow_mesh = SubResource("ArrayMesh_6umui")

[sub_resource type="ConcavePolygonShape3D" id="ConcavePolygonShape3D_83p7q"]
data = PackedVector3Array(-0.276, 0.445, -0.1361, -0.2002, -0.0051, 0.8704, -0.3692, 0.445, -0.1429, -0.276, 0.445, -0.1361, 0.1874, -0.0051, 0.9155, -0.2002, -0.0051, 0.8704, -0.1297, 0.445, -0.2604, 0.7223, -0.0051, 0.7224, -0.1738, 0.445, -0.1719, -0.1297, 0.445, -0.2604, 0.8775, -0.0051, 0.3375, 0.7223, -0.0051, 0.7224, -0.1738, 0.445, -0.1719, 0.1874, -0.0051, 0.9155, -0.276, 0.445, -0.1361, -0.1738, 0.445, -0.1719, 0.7223, -0.0051, 0.7224, 0.1874, -0.0051, 0.9155, 0.7069, -0.0051, -0.4443, -0.1269, 0.445, -0.3611, -0.1464, 0.445, -0.4443, 0.7069, -0.0051, -0.4443, 0.8534, -0.0051, -0.0946, -0.1269, 0.445, -0.3611, -0.2002, -0.0051, 0.8704, -0.4411, 0.445, -0.1509, -0.3692, 0.445, -0.1429, -0.2002, -0.0051, 0.8704, -0.4411, -0.0051, 0.6975, -0.4411, 0.445, -0.1509, -0.1269, 0.445, -0.3611, 0.8775, -0.0051, 0.3375, -0.1297, 0.445, -0.2604, -0.1269, 0.445, -0.3611, 0.8534, -0.0051, -0.0946, 0.8775, -0.0051, 0.3375, -0.1269, 0.445, -0.3611, -0.4411, 0.445, -0.4443, -0.1464, 0.445, -0.4443, -0.1297, 0.445, -0.2604, -0.4411, 0.445, -0.4443, -0.1269, 0.445, -0.3611, -0.1738, 0.445, -0.1719, -0.4411, 0.445, -0.4443, -0.1297, 0.445, -0.2604, -0.276, 0.445, -0.1361, -0.4411, 0.445, -0.4443, -0.1738, 0.445, -0.1719, -0.3692, 0.445, -0.1429, -0.4411, 0.445, -0.4443, -0.276, 0.445, -0.1361, -0.4411, 0.445, -0.1509, -0.4411, 0.445, -0.4443, -0.3692, 0.445, -0.1429, -0.4411, 0.445, -0.1509, -0.4411, -0.0051, -0.4443, -0.4411, 0.445, -0.4443, -0.4411, 0.445, -0.1509, -0.4411, -0.0051, -0.1509, -0.4411, -0.0051, -0.4443, -0.4411, 0.445, -0.1509, -0.4411, -0.0051, 0.6975, -0.4411, -0.0051, -0.1509, -0.1464, -0.0051, -0.4443, -0.4411, 0.445, -0.4443, -0.4411, -0.0051, -0.4443, -0.1464, -0.0051, -0.4443, -0.1464, 0.445, -0.4443, -0.4411, 0.445, -0.4443, 0.7069, -0.0051, -0.4443, -0.1464, 0.445, -0.4443, -0.1464, -0.0051, -0.4443)

[node name="MiniArenaStairsCorner" type="MeshInstance3D"]
mesh = SubResource("ArrayMesh_1llan")
skeleton = NodePath("")

[node name="StaticBody3D" type="StaticBody3D" parent="."]
transform = Transform3D(2.27392, 0, 0, 0, 2.27392, 0, 0, 0, 2.27392, 0, 0, 0)
collision_layer = 2

[node name="CollisionShape3D" type="CollisionShape3D" parent="StaticBody3D"]
shape = SubResource("ConcavePolygonShape3D_83p7q")
