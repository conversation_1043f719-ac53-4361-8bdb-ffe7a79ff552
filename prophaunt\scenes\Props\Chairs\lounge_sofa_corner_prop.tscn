[gd_scene load_steps=4 format=3 uid="uid://bdy4nqirpgpqm"]

[ext_resource type="PackedScene" uid="uid://cy82bsxfj1ddv" path="res://prophaunt/scenes/PropObject.tscn" id="1_gu2h4"]
[ext_resource type="PackedScene" uid="uid://c2tleoaqj0fet" path="res://prophaunt/maps/Source/Furniture/lounge_sofa_corner.tscn" id="2_fwesq"]

[sub_resource type="BoxShape3D" id="BoxShape3D_f8ox4"]
size = Vector3(3.91586, 1.86847, 1.65776)

[node name="LoungeSofaCornerProp" instance=ExtResource("1_gu2h4")]

[node name="LoungeSofaCorner" parent="Meshes" index="0" instance=ExtResource("2_fwesq")]

[node name="CollisionShape3D" type="CollisionShape3D" parent="StaticBody3D" index="0"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0.193903, 0.934234, 0.654732)
shape = SubResource("BoxShape3D_f8ox4")

[node name="CollisionShape3D2" type="CollisionShape3D" parent="StaticBody3D" index="1"]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -0.94487, 0.934234, -0.487109)
shape = SubResource("BoxShape3D_f8ox4")
