extends Control

signal game_mode_selected(mode: Constants.GameMode)
signal back_pressed

@onready var race_button = $CenterContainer/Panel/VBoxContainer/RaceButton
@onready var freeride_button = $CenterContainer/Panel/VBoxContainer/FreeRideButton
@onready var prophaunt_button = $CenterContainer/Panel/VBoxContainer/ProphauntButton
@onready var back_button = $CenterContainer/Panel/VBoxContainer/BackButton

func _ready():
	# Set button texts from translation if available
	race_button.text = tr("RACEMODE")
	freeride_button.text = tr("FREERIDEMODE") 
	prophaunt_button.text = tr("PROPHAUNTMODE")
	back_button.text = tr("BACK")


func _on_race_button_pressed():
	SoundManager.play_click_sound()
	game_mode_selected.emit(Constants.GameMode.Race)


func _on_free_ride_button_pressed():
	SoundManager.play_click_sound()
	game_mode_selected.emit(Constants.GameMode.FreeRide)


func _on_prophaunt_button_pressed():
	SoundManager.play_click_sound()
	game_mode_selected.emit(Constants.GameMode.Prophaunt)


func _on_back_button_pressed():
	SoundManager.play_click_sound()
	back_pressed.emit()


func _process(_delta):
	if Input.is_action_just_pressed("exit"):
		_on_back_button_pressed()
