[gd_scene load_steps=4 format=3 uid="uid://cg21fljixpyvo"]

[ext_resource type="Script" path="res://prophaunt/assets/Guns/Scene/GrenadeBody.gd" id="1_8gork"]
[ext_resource type="PackedScene" uid="uid://btkt6y08bhy5s" path="res://prophaunt/assets/Guns/Scene/grinade.tscn" id="2_dvs72"]

[sub_resource type="SphereShape3D" id="SphereShape3D_gtgab"]
radius = 0.2

[node name="GrenadeBody" type="RigidBody3D"]
collision_layer = 2
collision_mask = 3
script = ExtResource("1_8gork")

[node name="Grinade" parent="." instance=ExtResource("2_dvs72")]

[node name="CollisionShape3D" type="CollisionShape3D" parent="."]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0.196207, 0)
shape = SubResource("SphereShape3D_gtgab")

[node name="Timer" type="Timer" parent="."]
wait_time = 3.0
one_shot = true
autostart = true

[connection signal="timeout" from="Timer" to="." method="_on_timer_timeout"]
