[gd_scene load_steps=4 format=4 uid="uid://crsygjh44810o"]

[ext_resource type="Material" uid="uid://b6gd8jjb1kopc" path="res://prophaunt/Mat/Colormap2.tres" id="1_n023u"]

[sub_resource type="ArrayMesh" id="ArrayMesh_1s52c"]
_surfaces = [{
"aabb": AABB(-1.38564, 6.31477e-05, -1.6, 2.77128, 1.46694, 3.2),
"format": 34359742465,
"index_count": 240,
"index_data": PackedByteArray("AQACAAAAAgABAAMAAAAGAAEAAAAHAAYABgAHAAUAAAAIAAcACAAAAAkABQAHAAoADgANAA8ADgAPABAAEAAPABEABAAPAAIADwAEABEAEAALAAoABwAQAAoAEAAHAA4ACAAOAAcADgAIAAwACAANAAwADQAJAAAAAgANAAAADQACAA8AEwAFABIAEwABAAYAAQATABQAAQAVAAMAFwAYABYAGAAXABkAFwAaABkAGgAXABsAHAAYAB0AGQATABgAGAATABIAGgAUABMAFAAaAB0AFAAdABUAFQAdABgAHwAgAB4AIAAfACEAIgAlACMAHgAXACUAJQAXABYAJQAWABwAGwAgACMAGwAjABwAHAAjACUAJwAoACYAIQAoACIAKAAkACIAJAAnAB8AJgAfACcAHwAmACEAGgAcAB0AAgADAAQABAADAAUABQAKAAQABAAKAAsADQAOAAwACwAQABEACwARAAQADQAIAAkABQATAAYAFQABABQABQAVABIAFQAFAAMAGAAcABYAEwAZABoAGAASABUAIgAgACEAIAAiACMAHwAlACQAJQAfAB4AJQAiACQAFwAeACAAIAAbABcAKAAnACkAKAAhACYAJAAoACkAJwAkACkAHAAaABsA"),
"name": "Material",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 42,
"vertex_data": PackedByteArray("r1yxP9FeoT/JzEw/GNcjPtBeoT/21iM+AAAANNFeoT/KzMw/4NYjvtBeoT/21iM+qVyxv9FeoT/JzEw/4NYjvtBeoT8a1yO+GNcjPtBeoT8a1yO+AAAANM9eoT/QzMy/r1yxP89eoT/SzEy/r1yxP9BeoT88/Ze0qVyxv89eoT/SzEy/qVyxv9BeoT88/Ze0r1yxP5/Guz/SzEy/r1yxP6HGuz/JzEw/AAAANJ/Guz/QzMy/AAAANKHGuz/KzMw/qVyxv5/Guz/SzEy/qVyxv6HGuz/JzEw/4NYjvnBkjz4V1yO+GNcjPnBkjz4V1yO+GNcjPnBkjz771iM+4NYjvnBkjz771iM+gMJ1vl/dTD6dwnW+mMJ1Pl/dTD6dwnW+gMJ1vm9kjz6dwnW+mMJ1Pm9kjz6dwnW+mMJ1PnFkjz6DwnU+mMJ1PmHdTD6DwnU+gMJ1vmHdTD6DwnU+gMJ1vnFkjz6DwnU+4MzMPl7dTD7TzMy+4MzMPjz4oz3SzMy+4MzMPmLdTD7FzMw+4MzMPkT4oz3GzMw+uMzMvkT4oz3GzMw+uMzMvmLdTD7FzMw+uMzMvjz4oz3SzMy+uMzMvl7dTD7TzMy+tEfhPtaRhDimR+E+tEfhPipuhDi2R+G+qEfhvtaRhDimR+E+qEfhvipuhDi2R+G+")
}]
blend_shape_mode = 0

[sub_resource type="ArrayMesh" id="ArrayMesh_ks21x"]
resource_name = "TableRound_tableRound"
_surfaces = [{
"aabb": AABB(-1.38564, 6.31477e-05, -1.6, 2.77128, 1.46694, 3.2),
"attribute_data": PackedByteArray("+ndOP6xW4z76d04/rFbjPvp3Tj+sVuM++ndOP6xW4z76d04/rFbjPvp3Tj+sVuM++ndOP6xW4z76d04/rFbjPvp3Tj+sVuM++ndOP6xW4z76d04/rFbjPvp3Tj+sVuM++ndOP6xW4z76d04/rFbjPvp3Tj+sVuM++ndOP6xW4z76d04/rFbjPvp3Tj+sVuM++ndOP6xW4z76d04/rFbjPvp3Tj+sVuM++ndOP6xW4z76d04/rFbjPvp3Tj+sVuM++ndOP6xW4z76d04/rFbjPvp3Tj+sVuM++ndOP6xW4z76d04/rFbjPvp3Tj+sVuM++ndOP6xW4z76d04/rFbjPvp3Tj+sVuM++ndOP6xW4z76d04/rFbjPvp3Tj+sVuM++ndOP6xW4z76d04/rFbjPvp3Tj+sVuM++ndOP6xW4z76d04/rFbjPvp3Tj+sVuM++ndOP6xW4z76d04/rFbjPvp3Tj+sVuM++ndOP6xW4z76d04/rFbjPvp3Tj+sVuM++ndOP6xW4z76d04/rFbjPvp3Tj+sVuM++ndOP6xW4z76d04/rFbjPvp3Tj+sVuM++ndOP6xW4z4FflA/sFBKPfp3Tj+sVuM++ndOP6xW4z4FflA/sFBKPfp3Tj+sVuM++ndOP6xW4z4FflA/sFBKPfp3Tj+sVuM++ndOP6xW4z4FflA/sFBKPfp3Tj+sVuM+BX5QP7BQSj0FflA/sFBKPQV+UD+wUEo9BX5QP7BQSj0FflA/sFBKPQV+UD+wUEo9BX5QP7BQSj0FflA/sFBKPQV+UD+wUEo9BX5QP7BQSj0FflA/sFBKPQV+UD+wUEo9BX5QP7BQSj0FflA/sFBKPQV+UD+wUEo9BX5QP7BQSj0FflA/sFBKPQV+UD+wUEo9BX5QP7BQSj0FflA/sFBKPQV+UD+wUEo9BX5QP7BQSj0FflA/sFBKPQV+UD+wUEo9BX5QP7BQSj0FflA/sFBKPQV+UD+wUEo9BX5QP7BQSj0FflA/sFBKPQV+UD+wUEo9BX5QP7BQSj0FflA/sFBKPQV+UD+wUEo9BX5QP7BQSj0FflA/sFBKPQV+UD+wUEo9BX5QP7BQSj0FflA/sFBKPQV+UD+wUEo9BX5QP7BQSj0FflA/sFBKPQV+UD+wUEo9BX5QP7BQSj0FflA/sFBKPQV+UD+wUEo9BX5QP7BQSj0FflA/sFBKPQV+UD+wUEo9BX5QP7BQSj0FflA/sFBKPQV+UD+wUEo9BX5QP7BQSj0FflA/sFBKPQV+UD+wUEo9BX5QP7BQSj0FflA/sFBKPQV+UD+wUEo9BX5QP7BQSj0FflA/sFBKPQV+UD+wUEo9BX5QP7BQSj0FflA/sFBKPQV+UD+wUEo9BX5QP7BQSj0="),
"format": 34359742487,
"index_count": 240,
"index_data": PackedByteArray("BQAHAAEABwAFAAsAAQAUAAUAAQAWABQAFAAWABEAAQAaABYAGQAAAB0AEQAWAB8AKgAnAC0AKgAtADAAMAAtADMADgAvAAkALwAOADQAMgAjACEAGAAxACAAMQAYACwAGwArABcAKwAbACUAHAApACYAKQAeAAMACAAoAAIAKAAIAC4AOQAQADYAOwAGABUABgA7AD4ABAA/AAoARQBIAEIASABFAEsARwBQAE0AUABHAFMAVgBKAFkATAA6AEkASQA6ADcATwA9ADoAPQBPAFgAPQBYAEAAQABYAEkAYABjAFwAYwBgAGcAawB1AG4AWwBGAHQAdABGAEMAdABDAFUAUgBiAG0AUgBtAFUAVQBtAHQAegB9AHcAZQB8AGkAfgBxAGoAcAB5AF4AeABfAHsAXwB4AGYATgBUAFcABwALAA0ADQALABEAEQAfAA0ADQAfACIAJwAqACQAIwAyADUAIwA1AA8AKQAcAB4AEAA5ABMAPwAEADwAEgBBADgAQQASAAwASgBWAEQAOgBMAE8ASQA3AEAAaABhAGQAYQBoAGwAXQBzAG8AcwBdAFoAdQBrAHIARgBbAGIAYgBSAEYAfQB6AIAAfABlAHYAcQB+AIEAeQBwAH8AVABOAFEA"),
"material": ExtResource("1_n023u"),
"name": "Material",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 130,
"vertex_data": PackedByteArray("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")
}]
blend_shape_mode = 0
shadow_mesh = SubResource("ArrayMesh_1s52c")

[node name="tableRound" type="MeshInstance3D"]
mesh = SubResource("ArrayMesh_ks21x")
skeleton = NodePath("")
