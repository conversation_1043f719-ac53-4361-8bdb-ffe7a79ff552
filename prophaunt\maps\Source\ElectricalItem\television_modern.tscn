[gd_scene load_steps=4 format=4 uid="uid://fa2a3ldvjtfv"]

[ext_resource type="Material" uid="uid://b6gd8jjb1kopc" path="res://prophaunt/Mat/Colormap2.tres" id="1_ytf1r"]

[sub_resource type="ArrayMesh" id="ArrayMesh_2jp5y"]
_surfaces = [{
"aabb": AABB(-1.3696, -0.2568, -1.819, 2.7392, 0.5136, 1.819),
"format": 34359742465,
"index_count": 216,
"index_data": PackedByteArray("AQACAAAAAgABAAMABQAGAAQABgAFAAcACQAKAAgADAALAAkACwAMAA0ADwAQAA4ACQASAAwAEgAJAAgAEAATAAUADgAFAAQADgAEABYABwAYAAYABgAYAAAAGQAaAAIAIAALAA0ACwAgAAoADQAcACAAHAANACIAEgAKACIABQAXAAcAFwAFABMAAQAYACMAAQAjACAAFgAlABsAJQAWABUAEgANAAwADQASACIAFAAGABoAEAAlABUAJQAQABEAJAAgACMAJAAdACEADwAlABEACgAJAAsAEAAPABEAEwAQABQAFAAQABUAFAAVABYABQAOABAAFgAEABQAGAAHABcABgAAAAIAFwAZABgAGQAXABoAAgAaAAYAFAAXABMAFwAUABoAFgAPAA4ADwAWABsAHQAeABwAHgAdAB8ACgAgACEACgAhAB0AHAAiAB0AHQAiAAoAGAAkACMAJAAYABkACgASAAgAGAABAAAAAQAgABwAAQAcAB4ABgAUAAQAIAAkACEAHQAkAAMAHQADAB8AJAACAAMAAgAkABkAHwABAB4AAQAfAAMAJQAPABsA"),
"name": "Material",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 38,
"vertex_data": PackedByteArray("O3AOPrZphDyze3K/O3AOPph/uj2ze3K/O3AOvrZphDyze3K/O3AOvph/uj2ze3K/Dk+vP9Ei273+1Oi/Dk+vP9Ei2707cI6+Dk+vP7ZphDz+1Oi/Dk+vP7ZphDw7cI6+hsk0v0p7g74AAACAhsk0P0p7g74AAACAHVokv+84Rb4OT6+9HVokP+84Rb4OT6+9hsk0P0p7gz4AAACAHVokP7MMcT4OT6+9nwarP9Ei273skZ++nwarPw5Pr73skZ++nwarv9Ei273skZ++nwarvw5Pr73skZ++hsk0v0p7gz4AAACADk+vv9Ei2707cI6+Dk+vv9Ei273+1Oi/nwarv9Ei272TjOS/nwarP9Ei272TjOS/Dk+vv7ZphDw7cI6+O3AOPrZphDzgLVC/O3AOvrZphDzgLVC/Dk+vv7ZphDz+1Oi/nwarPw5Pr72TjOS/O3AOPmLyKT4OT6+9O3AOvmLyKT4OT6+9O3AOPmLyKT4OT1+/O3AOvmLyKT4OT1+/O3AOPuaDcT0OT6+9O3AOvuaDcT0OT6+9HVokv7MMcT4OT6+9O3AOPuaDcT3gLVC/O3AOvuaDcT3gLVC/nwarvw5Pr72TjOS/")
}]
blend_shape_mode = 0

[sub_resource type="ArrayMesh" id="ArrayMesh_qh6jc"]
resource_name = "TelevisionModern_TelevisionModern"
_surfaces = [{
"aabb": AABB(-1.3696, -0.2568, -1.819, 2.7392, 0.5136, 1.819),
"attribute_data": PackedByteArray("VLlKP4DKaD1UuUo/gMpoPVS5Sj+Aymg9VLlKP4DKaD1UuUo/gMpoPVS5Sj+Aymg9VLlKP4DKaD1UuUo/gMpoPVS5Sj+Aymg9VLlKP4DKaD1UuUo/gMpoPVS5Sj+Aymg9VLlKP4DKaD1UuUo/gMpoPVS5Sj+Aymg9VLlKP4DKaD1UuUo/gMpoPVS5Sj+Aymg9VLlKP4DKaD1UuUo/gMpoPVS5Sj+Aymg9VLlKP4DKaD1UuUo/gMpoPVS5Sj+Aymg9VLlKP4DKaD1UuUo/gMpoPVS5Sj+Aymg9VLlKP4DKaD1UuUo/gMpoPVS5Sj+Aymg9VLlKP4DKaD1UuUo/gMpoPVS5Sj+Aymg9VLlKP4DKaD1UuUo/gMpoPVS5Sj+Aymg9VLlKP4DKaD1UuUo/gMpoPVS5Sj+Aymg9VLlKP4DKaD1UuUo/gMpoPVS5Sj+Aymg9VLlKP4DKaD1UuUo/gMpoPVS5Sj+Aymg9VLlKP4DKaD0AvSk7qNVeP1S5Sj+Aymg9VLlKP4DKaD1UuUo/gMpoPVS5Sj+Aymg9VLlKP4DKaD3qBvc9fhNfP1S5Sj+Aymg9VLlKP4DKaD1UuUo/gMpoPVS5Sj+Aymg9VLlKP4DKaD1UuUo/gMpoPVS5Sj+Aymg9VLlKP4DKaD1UuUo/gMpoPVS5Sj+Aymg9VLlKP4DKaD1UuUo/gMpoPVS5Sj+Aymg9VLlKP4DKaD1UuUo/gMpoPVS5Sj+Aymg9VLlKP4DKaD1UuUo/gMpoPVS5Sj+Aymg9VLlKP4DKaD1UuUo/gMpoPVS5Sj+Aymg9VLlKP4DKaD1UuUo/gMpoPVS5Sj+Aymg9VLlKP4DKaD1UuUo/gMpoPVS5Sj+Aymg9VLlKP4DKaD1A2Es7ZCtOP1S5Sj+Aymg9VLlKP4DKaD1UuUo/gMpoPVS5Sj+Aymg9VLlKP4DKaD1UuUo/gMpoPVS5Sj+Aymg9VLlKP4DKaD1UuUo/gMpoPVS5Sj+Aymg9VLlKP4DKaD1UuUo/gMpoPVS5Sj+Aymg9VLlKP4DKaD1UuUo/gMpoPVS5Sj+Aymg9VLlKP4DKaD1UuUo/gMpoPVS5Sj+Aymg9VLlKP4DKaD1UuUo/gMpoPVS5Sj+Aymg9VLlKP4DKaD1UuUo/gMpoPVS5Sj+Aymg9VLlKP4DKaD1UuUo/gMpoPVS5Sj+Aymg9VLlKP4DKaD3EF/g9O2lOP1S5Sj+Aymg9"),
"format": 34359742487,
"index_count": 216,
"index_data": PackedByteArray("AwAGAAAABgADAAkAEQAUAA4AFAARABcAHAAfABkAJgAjAB0AIwAmACkALQAwACoAGwA2ACQANgAbABgAMQA6ABAAKwAQAA0AKwANAEMAFgBJABMAEwBJAAEATABPAAcAYAAhACcAIQBgAB4AJwBUAGAAVAAnAGYAOAAgAGgADwBFABUARQAPADkABQBKAGsABQBrAGIAQgBvAFEAbwBCAD8ANwAoACUAKAA3AGcAPAASAE4AMgBxAEEAcQAyADUAbQBhAGoAbgBZAGUALgBwADQAHwAcACIAMAAtADMAOgAxAD0APQAxAEAAPQBAAEMAEAArADEAQwANAD0ASQAWAEYAEwABAAcARgBMAEkATABGAE8ABwBPABMAPgBHADsARwA+AFAARAAvACwALwBEAFMAWABbAFUAWwBYAF4AHgBgAGMAHgBjAFcAVABmAFcAVwBmAB4ASABsAGkAbABIAEsAIAA4ABoASgAFAAIABQBiAFYABQBWAFwAEgA8AAwAYQBtAGQAWQBuAAsAWQALAF8AbgAIAAsACABuAE0AXQAEAFoABABdAAoAcAAuAFIA"),
"material": ExtResource("1_ytf1r"),
"name": "Material",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 114,
"vertex_data": PackedByteArray("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")
}]
blend_shape_mode = 0
shadow_mesh = SubResource("ArrayMesh_2jp5y")

[node name="TelevisionModern" type="MeshInstance3D"]
transform = Transform3D(1, 0, 0, 0, 0, -1, 0, 1, 0, 0, 0, 0)
mesh = SubResource("ArrayMesh_qh6jc")
skeleton = NodePath("")
