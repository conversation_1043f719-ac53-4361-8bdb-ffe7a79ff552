[gd_scene load_steps=5 format=4 uid="uid://c122nnm6o7te6"]

[ext_resource type="Material" uid="uid://b60k1mvxam058" path="res://prophaunt/Mat/ArenaColorMap.tres" id="1_cgdc5"]

[sub_resource type="ArrayMesh" id="ArrayMesh_krqkh"]
_surfaces = [{
"aabb": AABB(-0.999985, -0.000185132, -0.991957, 5.31609, 4.68635, 1.98391),
"format": 34359742465,
"index_count": 312,
"index_data": PackedByteArray("AAABAAIAAwACAAEABAAFAAYABwAGAAUACAAJAAoACwAKAAkADAAKAAsADQAKAAwADgAKAA0ADwAOAA0AEAAPAA0AAQAOAA8AAwABAA8AEQAOAAEAEgARAAEAEwAOABEAFAAOABMAFQATABEAFgAUABMABAAOABQAFwAEABQAGAAOAAQABgAYAAQAGQAOABgAGgAZABgAGwAOABkAHAAOABsAHQAOABwAHgAOAB0AHwAgAAoACAAKACAACAAgAAkAIQAJACAAEQAiABUAIwAVACIAJAATACMAFQAjABMAAQAAABIAJQASAAAAJgAeACcAHQAnAB4ADwAoAAMAAgADACgAKQAYAAcABgAHABgAKgAOACYAHgAmAA4AKwAcACwAGwAsABwAGAApABoALQAaACkADQAuABAALwAQAC4ABQAEADAAFwAwAAQAMQAZAC0AGgAtABkAHAArAB0AJwAdACsAGQAxABsALAAbADEALgANADIADAAyAA0ACwAzAAwAMgAMADMAFAA0ABcAMAAXADQAKgAfAA4ACgAOAB8AIgARACUAEgAlABEAMgAzAC4ALgAzAB8AHwAzACAAIQAgADMAHwAoAC4ALwAuACgAKgAoAB8AAAAoACoAIgAAACoAAgAoAAAAJQAAACIAJAAiACoAIwAiACQANAAkACoANQAkADQABQA0ACoAMAA0AAUAKQAFACoABwAFACkAMQApACoALQApADEAKwAxACoALAAxACsAJgArACoAJwArACYANAAUADUAFgA1ABQAKAAPAC8AEAAvAA8AEwAkABYANQAWACQAMwALACEACQAhAAsA"),
"name": "colormap",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 54,
"vertex_data": PackedByteArray("o6yqPg52yD/l8H2/o6yqPg52yD/k8H0/AIB5Nw52yD/l8H2/AIB5Nw52yD/k8H0/01XVP8YVSEDk8H0/01XVP8YVSEDm8H2/01XVP3YMYUDk8H0/01XVP3YMYUDm8H2//v5/vwAAALTm8H0//v5/v7C3yj7m8H0/Epd7PwAgQrnm8H0/rqkqv7C3yj7m8H0/rqkqv5g2ST/m8H0/raiqvpg2ST/m8H0/jB2KQBsAekDi8H0/AMCAN62Ilj/k8H0/raiqvqyIlj/m8H0/qqsqP21j+j/k8H0/o6yqPm1j+j/k8H0/fQCAP2QoFkDk8H0/KauqPxYfL0Dk8H0/qqsqP2QoFkDk8H0/fQCAPxYfL0Dk8H0/KauqP8YVSEDk8H0/PwAAQHYMYUDk8H0/lVUVQCQDekDk8H0/PwAAQCQDekDk8H0/lVUVQOx8iUDk8H0/6aoqQOx8iUDi8H0/6aoqQAb1lUDi8H0/jB2KQAb1lUDi8H0/Epd7PwAgQrnj8H2//v5/vwAAALTj8H2/Bv9/v7C3yj7j8H2/qqsqP21j+j/l8H2/oqsqP2QoFkDl8H2/fQCAP2QoFkDl8H2/o6yqPm1j+j/l8H2/jB2KQAb1lUDn8H2/56oqQAb1lUDn8H2/AIB5N62Ilj/l8H2/PwAAQHYMYUDm8H2/jB2KQBsAekDn8H2/6aoqQOx8iUDn8H2/lVUVQOx8iUDm8H2/PwAAQCQDekDm8H2/raiqvpg2ST/j8H2/raiqvqyIlj/j8H2/KauqP8YVSEDm8H2/lVUVQCQDekDm8H2/rqkqv5g2ST/j8H2/rqkqv7C3yj7j8H2/KauqPxYfL0Dm8H2/fQCAPxYfL0Dl8H2/")
}]
blend_shape_mode = 0

[sub_resource type="ArrayMesh" id="ArrayMesh_gw6ws"]
resource_name = "ArenaStairsStone_StairsStone"
_surfaces = [{
"aabb": AABB(-0.999985, -0.000185132, -0.991957, 5.31609, 4.68635, 1.98391),
"attribute_data": PackedByteArray("AABYP4iIaD8AAFg/iIhoPwAAWD+IiGg/AABYP4iIaD8AAFg/iIhoPwAAWD+IiGg/AABYP4iIaD8AAFg/iIhoPwAAWD+IiGg/AABYP4iIaD8AAFg/iIhoPwAAWD+IiGg/AABYP3d3Vz8AAFg/d3dXPwAAWD93d1c/AABYP3d3Vz8AAFg/d3dXPwAAWD93d1c/AABYPzMzUz8AAFg/MzNTPwAAWD8zM1M/AABYPzMzUz8AAFg/MzNTPwAAWD8zM1M/AABYP5qZeT8AAFg/mpl5PwAAWD+amXk/AABYP1VVdT8AAFg/VVV1PwAAWD9VVXU/AABYP5qZeT8AAFg/mpl5PwAAWD+amXk/AABYP1VVdT8AAFg/VVV1PwAAWD9VVXU/AABYPxERcT8AAFg/ERFxPwAAWD8REXE/AABYPxERcT8AAFg/ERFxPwAAWD8REXE/AABYP+/uTj8AAFg/7+5OPwAAWD/v7k4/AABYP83MbD8AAFg/zcxsPwAAWD/NzGw/AABYP83MbD8AAFg/zcxsPwAAWD/NzGw/AABYP0REZD8AAFg/RERkPwAAWD9ERGQ/AABYP0REZD8AAFg/RERkPwAAWD9ERGQ/AABYPwAAYD8AAFg/AABgPwAAWD8AAGA/AABYP7y7Wz8AAFg/vLtbPwAAWD+8u1s/AABYPwAAYD8AAFg/AABgPwAAWD8AAGA/AABYP7y7Wz8AAFg/vLtbPwAAWD+8u1s/AABYP3d3Vz8AAFg/d3dXPwAAWD93d1c/AABYPzMzUz8AAFg/MzNTPwAAWD8zM1M/AABYP+/uTj8AAFg/7+5OPwAAWD/v7k4/AABYP+/uTj8AAFg/7+5OPwAAWD/v7k4/AABYP6qqSj8AAFg/qqpKPwAAWD+qqko/AABYP6qqSj8AAFg/qqpKPwAAWD+qqko/AABYP2ZmRj8AAFg/ZmZGPwAAWD9mZkY/AABYP2ZmRj8AAFg/ZmZGPwAAWD9mZkY/AABYP5qZeT8AAFg/mpl5PwAAWD+amXk/AABYP5qZeT8AAFg/mpl5PwAAWD+amXk/AABYP1VVdT8AAFg/VVV1PwAAWD9VVXU/AABYP0REZD8AAFg/RERkPwAAWD9ERGQ/AABYPwAAYD8AAFg/AABgPwAAWD8AAGA/AABYPwAAYD8AAFg/AABgPwAAWD8AAGA/AABYP0REZD8AAFg/RERkPwAAWD9ERGQ/AABYP2ZmRj8AAFg/ZmZGPwAAWD9mZkY/AABYP2ZmRj8AAFg/ZmZGPwAAWD9mZkY/AABYP83MbD8AAFg/zcxsPwAAWD/NzGw/AABYPzMzUz8AAFg/MzNTPwAAWD8zM1M/AABYP+/uTj8AAFg/7+5OPwAAWD/v7k4/AABYP6qqSj8AAFg/qqpKPwAAWD+qqko/AABYP6qqSj8AAFg/qqpKPwAAWD+qqko/AABYP+/uTj8AAFg/7+5OPwAAWD/v7k4/AABYPxERcT8AAFg/ERFxPwAAWD8REXE/AABYP83MbD8AAFg/zcxsPwAAWD/NzGw/AABYP3d3Vz8AAFg/d3dXPwAAWD93d1c/AABYP+/uTj8AAFg/7+5OPwAAWD/v7k4/AABYPxERcT8AAFg/ERFxPwAAWD8REXE/AABYP1VVdT8AAFg/VVV1PwAAWD9VVXU/AABYP7y7Wz8AAFg/vLtbPwAAWD+8u1s/AABYP7y7Wz8AAFg/vLtbPwAAWD+8u1s/"),
"format": 34359742487,
"index_count": 312,
"index_data": PackedByteArray("AQAEAAcACgAHAAQADgARABQAFwAUABEAGAAbAB4AIQAeABsAJAAeACEAJwAeACQAKgAeACcALQAqACcAMAAtACcAAwAqAC0ACQADAC0AMwAqAAMANgAzAAMAOQAqADMAPAAqADkAPwA5ADMAQgA8ADkADAAqADwARQAMADwASAAqAAwAEgBIAAwASwAqAEgATgBLAEgAUQAqAEsAVAAqAFEAVwAqAFQAWgAqAFcAXwBhACAAGQAgAGEAGgBiAB0AZQAdAGIANQBoAEEAawBBAGgAbQA6AGoAQABqADoABQACADgAcQA4AAIAcwBbAHYAWAB2AFsALwB6AAsACAALAHoAfABJABYAEwAWAEkAgAAsAHQAXAB0ACwAggBVAIUAUgCFAFUASgB9AFAAiQBQAH0AKQCMADIAjwAyAIwAEAANAJEARgCRAA0AlABMAIgATwCIAEwAVgCDAFkAdwBZAIMATQCVAFMAhgBTAJUAiwAoAJcAJQCXACgAIwCbACYAmAAmAJsAPgCeAEcAkgBHAJ4AfwBeACsAHwArAF4AZwA0AHAANwBwADQAlgCZAIoAigCZAF0AXQCZAGAAYwBgAJkAXQB4AIoAjQCKAHgAfgB4AF0AAAB4AH4AZgAAAH4ABgB4AAAAbwAAAGYAbABmAH4AaQBmAGwAnABsAH4AnwBsAJwADwCcAH4AkACcAA8AewAPAH4AFQAPAHsAkwB7AH4AhwB7AJMAgQCTAH4AhACTAIEAcgCBAH4AdQCBAHIAnQA9AKAAQwCgAD0AeQAuAI4AMQCOAC4AOwBuAEQAoQBEAG4AmgAiAGQAHABkACIA"),
"material": ExtResource("1_cgdc5"),
"name": "colormap",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 162,
"vertex_data": PackedByteArray("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")
}]
blend_shape_mode = 0
shadow_mesh = SubResource("ArrayMesh_krqkh")

[sub_resource type="ConcavePolygonShape3D" id="ConcavePolygonShape3D_t6j6e"]
data = PackedVector3Array(2.6667, 4.6862, 0.992, 4.3161, 4.6862, 0.992, 1.9988, 3.9097, 0.992, 0.9828, 0, -0.992, -1.3568, 0, -0.992, 0.9828, 0, 0.992, -1.3568, 0, 0.992, 0.9828, 0, 0.992, -1.3568, 0, -0.992, 4.3161, 4.6862, -0.992, 4.3161, 4.6862, 0.992, 2.6667, 4.6862, -0.992, 2.6667, 4.6862, 0.992, 2.6667, 4.6862, -0.992, 4.3161, 4.6862, 0.992, 4.3161, 3.908, -0.992, 4.3161, 3.9064, 0.992, 4.3161, 4.6862, -0.992, 4.3161, 4.6862, 0.992, 4.3161, 4.6862, -0.992, 4.3161, 3.9064, 0.992, 2.6667, 4.6862, -0.992, 2.6667, 4.6862, 0.992, 1.9988, 3.9097, -0.992, 1.9988, 3.9097, -0.992, 1.9988, 3.9097, 0.992, -1.3568, 0, -0.992, 4.3161, 3.908, -0.992, 0.9828, 0, -0.992, 4.3161, 3.9064, 0.992, 0.9828, 0, 0.992, 4.3161, 3.9064, 0.992, 0.9828, 0, -0.992, 4.3161, 3.908, -0.992, 1.9988, 3.9097, -0.992, -1.3568, 0, -0.992, 4.3161, 3.9064, 0.992, -1.3568, 0, 0.992, 1.9988, 3.9097, 0.992, 4.3161, 3.908, -0.992, 2.6667, 4.6862, -0.992, 1.9988, 3.9097, -0.992, 2.6667, 4.6862, -0.992, 4.3161, 3.908, -0.992, 4.3161, 4.6862, -0.992, 2.6667, 4.6862, 0.992, 1.9988, 3.9097, 0.992, 1.9988, 3.9097, -0.992, 4.3161, 4.6862, 0.992, 4.3161, 3.9064, 0.992, 1.9988, 3.9097, 0.992, 1.9988, 3.9097, 0.992, -1.3568, 0, 0.992, -1.3568, 0, -0.992, -1.3568, 0, -0.992, 0.9828, 0, -0.992, 4.3161, 3.908, -0.992, -1.3568, 0, 0.992, 4.3161, 3.9064, 0.992, 0.9828, 0, 0.992)

[node name="StairsStone" type="MeshInstance3D"]
mesh = SubResource("ArrayMesh_gw6ws")
skeleton = NodePath("")

[node name="StaticBody3D" type="StaticBody3D" parent="."]
collision_layer = 2

[node name="CollisionShape3D" type="CollisionShape3D" parent="StaticBody3D"]
shape = SubResource("ConcavePolygonShape3D_t6j6e")
