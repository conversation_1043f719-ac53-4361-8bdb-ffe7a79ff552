<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:versionCode="1"
    android:versionName="1.0"
    android:installLocation="auto" >

    <uses-permission android:name="android.permission.CAMERA" tools:node="remove" />

    <!-- Foreground service permissions for Android 14+ -->
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_DATA_SYNC" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_MICROPHONE" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_SPECIAL_USE" />
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />

    <!-- Microphone permissions for LiveKit -->
    <uses-permission android:name="android.permission.RECORD_AUDIO" />
    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />

    <!-- Internet and network permissions -->
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />

    <supports-screens
        android:smallScreens="true"
        android:normalScreens="true"
        android:largeScreens="true"
        android:xlargeScreens="true" />

    <uses-feature
        android:glEsVersion="0x00030000"
        android:required="true" />

    <application
        android:label="@string/godot_project_name_string"
        android:allowBackup="false"
        android:icon="@mipmap/icon"
        android:appCategory="game"
        android:isGame="true"
        android:hasFragileUserData="false"
        android:requestLegacyExternalStorage="false"
        tools:ignore="GoogleAppIndexingWarning" >
        <profileable
            android:shell="true"
            android:enabled="true"
            tools:targetApi="29" />
        
        <!-- Required: set your sentry.io project identifier (DSN) -->
        <meta-data android:name="io.sentry.dsn" 
        android:value="https://<EMAIL>/4508519819902976" />

        <!-- enable automatic breadcrumbs for user interactions (clicks, swipes, scrolls) -->
        <meta-data android:name="io.sentry.traces.user-interaction.enable" android:value="true" />
        <!-- enable screenshot for crashes -->
        <meta-data android:name="io.sentry.attach-screenshot" android:value="true" />
        <!-- enable view hierarchy for crashes -->
        <meta-data android:name="io.sentry.attach-view-hierarchy" android:value="true" />

        <!-- enable the performance API by setting a sample-rate, adjust in production env -->
        <meta-data android:name="io.sentry.traces.sample-rate" android:value="1.0" />

        <meta-data android:name="io.sentry.debug" 
        android:value="false" />

                
        <receiver android:exported="true" android:name="com.google.firebase.iid.FirebaseInstanceIdReceiver" android:permission="com.google.android.c2dm.permission.SEND">
                        
            <intent-filter>
                                
                <action android:name="com.google.android.c2dm.intent.RECEIVE"/>
                            
            </intent-filter>
                        
            <meta-data android:name="com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED" android:value="true"/>
                    
        </receiver>
                
        <!--
             FirebaseMessagingService performs security checks at runtime,
             but set to not exported to explicitly avoid allowing another app to call it.
        -->
                
        <service
            android:directBootAware="true"
            android:exported="false"
            android:name="com.google.firebase.messaging.FirebaseMessagingService"
            android:foregroundServiceType="dataSync">

            <intent-filter android:priority="-500">

                <action android:name="com.google.firebase.MESSAGING_EVENT"/>

            </intent-filter>

        </service>

        <!-- LiveKit Audio Service for microphone usage -->
        <service
            android:name="io.livekit.android.audio.AudioService"
            android:exported="false"
            android:foregroundServiceType="microphone" />

        <!-- Generic LiveKit service for real-time communication -->
        <service
            android:name="io.livekit.android.LiveKitService"
            android:exported="false"
            android:foregroundServiceType="microphone|dataSync" />

        <!-- Records the version of the Godot editor used for building -->
        <meta-data
            android:name="org.godotengine.editor.version"
            android:value="${godotEditorVersion}" />

        <activity
            android:name=".GodotApp"
            android:label="@string/godot_project_name_string"
            android:theme="@style/GodotAppSplashTheme"
            android:launchMode="singleInstancePerTask"
            android:excludeFromRecents="false"
            android:exported="true"
            android:screenOrientation="landscape"
            android:configChanges="orientation|keyboardHidden|screenSize|smallestScreenSize|density|keyboard|navigation|screenLayout|uiMode"
            android:resizeableActivity="false"
            tools:ignore="UnusedAttribute" >

            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
            
            <intent-filter>
                <action android:name="android.intent.action.VIEW"/>
                <category android:name="android.intent.category.DEFAULT"/>
                <category android:name="android.intent.category.BROWSABLE"/>

                <data android:scheme="http" />
                <data android:scheme="https" />

                <data android:host="sizakgames.ir" />
                <data android:host="api.sizakgames.ir" />

                <data android:path="/" />
                <data android:path="/scan" />
                <data android:path="/gateway" />

            </intent-filter>

        </activity>

    </application>

</manifest>
