[gd_resource type="Resource" script_class="ProphauntGunItem" load_steps=4 format=3 uid="uid://bhw8ha1v1m4pe"]

[ext_resource type="Texture2D" uid="uid://61qv1x64sjvc" path="res://prophaunt/assets/Guns/Scene/blaster_q.png" id="1_b1ed5"]
[ext_resource type="PackedScene" uid="uid://0qd1ekc6wl0l" path="res://prophaunt/assets/Guns/Scene/blaster_q.tscn" id="2_blaster_q_scene"]
[ext_resource type="Script" path="res://Inventory/ProphauntGunItem.gd" id="3_blaster_q_script"]

[resource]
script = ExtResource("3_blaster_q_script")
salam = 10
damage = 75
ammo = 28
gun_type = "range"
range = 30
animation_speed = 4.0
scene = ExtResource("2_blaster_q_scene")
scale = Vector3(150, 150, 150)
position = Vector3(19.5, 14.6, 0)
rotation = Vector3(-148, 194, 74)
ui = 2
id = 4
name = "BLASTER_Q"
icon = ExtResource("1_b1ed5")
price = 200
type = "Gun"
in_hand = true
usable = false
remove_on_ban = true
