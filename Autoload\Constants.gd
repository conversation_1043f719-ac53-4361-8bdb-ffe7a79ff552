@tool
extends Node

var LOCAL_MODE = false
#var LOCAL_MAP = "res://Scenes/maps/Mlevel.tscn"
#var LOCAL_MAP = "res://Scenes/maps/Mazzlevel.tscn"
#var LOCAL_MAP = "res://Scenes/maps/blank_map.tscn"
#var LOCAL_MAP = "res://Scenes/maps/CannonLevel.tscn"
#var LOCAL_MAP = "res://Scenes/maps/DynamicLevel.tscn"
#var LOCAL_MAP = "res://Scenes/maps/seesaw_level.tscn"
#var LOCAL_MAP = "res://Scenes/maps/MoveLevel.tscn"
#var LOCAL_MAP = "res://Scenes/maps/RunningLevel.tscn"
#var LOCAL_MAP = "res://Scenes/maps/HighLevel.tscn"
#var LOCAL_MAP = "res://Scenes/maps/DoorLevel.tscn"
#var LOCAL_MAP = "res://Scenes/maps/ElevatorLevel.tscn"
#var LOCAL_MAP = "res://Scenes/maps/Trampoline_level.tscn"
#var LOCAL_MAP = "res://Scenes/maps/UpDownDoor_level.tscn"
#var LOCAL_MAP = "res://Scenes/maps/test_elimination_level.tscn"
#var LOCAL_MAP = "res://Scenes/maps/IntoNatureLevel.tscn"
#var LOCAL_MAP = "res://Scenes/maps/Hexagon_elimination_level.tscn"
#var LOCAL_MAP = "res://Scenes/maps/GiantRollet_elimination_level.tscn"
#var LOCAL_MAP = "res://Scenes/maps/HideAndSeek_level.tscn"
#var LOCAL_MAP = "res://Scenes/maps/TilefallLevel.tscn"
#var LOCAL_MAP = "res://Scenes/maps/bombardment_Level.tscn"
#var LOCAL_MAP = "res://Scenes/maps/Watersplash_level.tscn"
var LOCAL_MAP = "res://Scenes/FreeRide/Maps/AzadShahr.tscn"
var BACKEND_URL = "https://api.sizakgames.ir"
#var BACKEND_URL = "http://127.0.0.1:8000"

enum Controls {Player, Remote, Bot}
enum ServerState {Loading, Lobby, CountDown, InGame, Results}
enum MapType {Qualification, Elimination, Team}
enum GameMode {Race, FreeRide, Prophaunt}
enum LEADERBOARD_TYPE {CUP, CROWN}
enum NETWORK_CHEST_STATE {Close, Open}
enum CannonDartStages {Idle, Aim, Fire}
const ServerStateStrings = ["Loading", "Lobby", "CountDown", "InGame", "Results"]

const PHYSICS_LAYER_PLAYER = 1
const PHYSICS_LAYER_FLOOR = 2
const PHYSICS_LAYER_BALL = 4

const MINIGAME_EXIT = 0
const MINIGAME_P1_WIN = 1
const MINIGAME_P2_WIN = 2
const MINIGAME_DRAW = 3
const MINIGAME_DC = 4

const PRESENCE_MAIN_MENU = "MainMenu"
const PRESENCE_RACE_MODE = "Race"
const PRESENCE_FREERIDE_MODE = "Freeride"
const PRESENCE_PROPHAUNT_MODE = "Prophaunt"

var URL = "127.0.0.1"
var PORT = 9000
#var POSITION_LERP = 0.1
var POSITION_LERP = 0.3
var INIT_POS = Vector3(-1000, -1000, -1000)

var is_server = false
var server_sync_v2 = false

var SERVER_TICK = 60
var SERVER_ALL_PLAYERS_SYNC = 1.0 / 30
var SERVER_MY_CONTROLL_SYNC = 1.0 / 30
var SERVER_MY_PLAYER_SYNC = 1.0 / 30

var SERVER_LOBBY_SYNC = 1.0 / 30
var SERVER_COUNTDOWN_SYNC = 1.0 / 20
var SERVER_IN_GAME_TIMER_SYNC = 1.0 / 10
var LOBBY_TIME = 180# Fill in map select api
var LOBBY_PLAYER_COUNT_START = 1
var CLIENT_CHARACTER_MOVE_TIME = 0
var IN_GAME_FINISHED_TIME = 0
var SERVER_RESULT_TIME = 10
var SERVER_IN_GAME_TIME = -1# Fill in map select api
var SERVER_COUNTDOWN_TIME = 5 + 5 #second 5-> for loading map
var SERVER_PLAYER_JOINED_ADD_LOBBY_TIME = -1# Fill in map select api
var SERVER_FORCE_NEW_PLAYER = false# Update on update state
var SERVER_FORCE_RECONNECT_PLAYER = false# Update on update state
var SERVER_GUN_ALLOW = false# Update on update state
var SERVER_PROPHAUNT_LOBBY_TIME#Fill in map select
var EMOTE_SHOW_TIME = 5.8 #seconds
var PLAYER_FORCE_STATE_SYNC_TIME = 1.5
const CHARACTER_SLIDE_ANIMATION_SPEED = 1.18
const CHARACTER_ATTACK_ANIMATION_SPEED = 1.5
const CHARACTER_COOK_ANIMATION_SPEED = 1.0
const SERVER_NETWORK_NODE_SYNC = 1.0 / 30
const SERVER_NETWORK_NODE_DISTANCE = 100

const SERVER_NETWORK_LIST_DATA_INTERVAL = 1 / 1.0
const SERVER_NETWORK_GENERAL_INTERVAL = 1 / 2.0
const SERVER_NETWORK_INSIGHT_INTERVAL = 1 / 30.0
const SERVER_NETWORK_INSIGHT_DISTANCE = 100 * 100  #Pow2 (client max visible player distance is 90 #RemoteCharacters.gd)


const CHANNEL_LOBBY = 1
const CHANNEL_PING = 2
const CHANNEL_PLAYER_SYNC = 3
const CHANNEL_PLAYER_INPUT = 4
const CHANNEL_PLAYER_JUMP = 5
const CHANNEL_NEW_PLAYER = 6
const CHANNEL_LOBBY_UNRELIABLE = 7
const CHANNEL_RESULT = 8
const CHANNEL_INDIE_PLAYER_SYNC = 9
const CHANNEL_TIMER_SYNC = 10
const CHANNEL_GAME_START = 11
const CHANNEL_UNIVERSAL_SYNC = 12
const CHANNEL_EMOTE = 13
const CHANNEL_CHANGE_CHARACTER = 14
const CHANNEL_FORCE_PLAYER_SYNC = 15
const CHANNEL_ACTIONABLES = 16
const CHANNEL_MINIGAMES = 17
const CHANNEL_PRIVATE_CHAT = 18
const CHANNEL_COSMETIC = 19
const CHANNEL_RESTAURANT = 20
const CHANNEL_SOUND = 21
const CHANNEL_JOB = 22
const CHANNEL_POLICE = 23
const CHANNEL_GUN = 25
const CHANNEL_VOICE_CHAT = 26
const CHANNEL_SPAWN_MANAGER = 27
const CHANNEL_PLAYER_GENERAL = 28
const CHANNEL_PLAYER_INSIGHT = 29
const CHANNEL_EVENT = 30


const SHOP_TELEPORT_PRICE = 10
const ATM_USE_MIN_CUP = 100

# Prophaunt Game Mode Constants
var PROPHAUNT_ROUND_TIME = 180 # 3 minutes per round
const PROPHAUNT_ROUNDS_PER_MAP = 3 # 3 rounds before map change
const PROPHAUNT_PROP_SOUND_COOLDOWN = 20 # 20 seconds before prop makes sound
const PROPHAUNT_HEX_DURATION = 5 # 5 seconds hex stun duration
const PROPHAUNT_HEX_COOLDOWN = 30 # 30 seconds hex cooldown
const PROPHAUNT_PROP_DEFAULT_HP = 100 # Default prop health
const PROPHAUNT_GRENADE_DAMAGE = 75 # Grenade damage
const PROPHAUNT_GRENADE_RADIUS = 5 # Grenade damage radius
const PROPHAUNT_GUN_DAMAGE = 25 # Gun damage to props
const PROPHAUNT_SELF_DAMAGE = 10 # Damage to haunter for shooting wrong object
const PROPHAUNT_RIFLE_COOLDOWN = 0.1
const PROPHAUNT_SYNC_PROP_SIZE = 27
const PROPHAUNT_SYNC_HAUNTER_SIZE = 26
const PROPHAUNT_SYNC_START_SIZE = 61

enum ProphauntTeam {PROPS, HAUNTERS}
enum ProphauntPlayerState {ALIVE, DEAD, HEXED}


var PLAYER_DATA_PACKET_SIZE = 29
var is_headless = false
var game_mode = GameMode.Race #Default #For Server


var server: Server
var client: Client

var DAILY_REWARD_INTERVAL = 24 * 3600
var SERVER_IP = "127.0.0.1"
var SERVER_PORT = 9000
var REGISTER_PORT = -1
var BACKEND_ID = 0 #Game Server Backend ID
var BACKEND_TOKEN = ""

var RATE_CUPS = [30, 60, 100, 150, 200, 400, 500, 600]

const CRIME_FAULT = "دستگیری اشتباه"
const CRIME_SWEAR = "حرف بد"
const CRIME_DISTURBANCE = "مزاحمت"
const CRIME_JOB = "کم کاری"
const CRIME_THIEF = "دزدی"
const CRIME_SOESTEFADE = "سواستفاده"
const Crimes = [CRIME_SWEAR, CRIME_DISTURBANCE, CRIME_JOB, CRIME_THIEF, CRIME_SOESTEFADE]
const ReportCrimes = [CRIME_FAULT, CRIME_SWEAR, CRIME_DISTURBANCE, CRIME_JOB, CRIME_THIEF, CRIME_SOESTEFADE]
const POLICE_CALL_PRICE = 5
const POLICE_CALL_PRICE_MOBILE = 30


func handle_args():
	var args = OS.get_cmdline_user_args()
	for arg in args:
		var key = arg.split("=")[0]
		var value = arg.split("=")[1]
		
		if key == "server":
			if int(value):
				is_server = true
				LOCAL_MODE = false
		if key == "url":
			Constants.URL = value
		if key == "mode":
			if value == "FreeRide":
				game_mode = GameMode.FreeRide
			elif value == "PropHaunt":
				game_mode = GameMode.Prophaunt
			else:
				game_mode = GameMode.Race
		if key == "headless":
			if int(value):
				is_headless = true
			else:
				is_headless = false
		if key == "syncv2":
			if int(value):
				server_sync_v2 = true
			else:
				server_sync_v2 = false
		if key == "ip":
			SERVER_IP = value
		if key == "port":
			SERVER_PORT = int(value)
		if key == "register_port":
			REGISTER_PORT = int(value)
		if key == "token":
			BACKEND_TOKEN = value
		if key == "backend":
			BACKEND_URL = value
	if LOCAL_MODE:
		is_server = true


func is_client():
	if LOCAL_MODE:
		return true
	return not is_server


func convert_player_data_to_packed_byte_array(player_data: Dictionary, _tick=-1):
	var buf = PackedByteArray()
	buf.resize(PLAYER_DATA_PACKET_SIZE)
	
	
	buf.encode_u8(0, player_data["d"]["title"])
	buf.encode_u8(1, player_data["d"]["right_hand"])
	buf.encode_u8(2, player_data["d"]["vehicle"])
	buf.encode_u8(3, player_data["d"]["reserved"])
	
	
	
	buf.encode_u32(4, player_data["id"]) #4bytes
	
	buf.encode_half(8, player_data["d"]["p"].x) #2bytes
	buf.encode_half(10, player_data["d"]["p"].y) #2bytes
	buf.encode_half(12, player_data["d"]["p"].z) #2bytes
	
	buf.encode_half(14, player_data["d"]["R"].x) #2bytes
	buf.encode_half(16, player_data["d"]["R"].y) #2bytes
	buf.encode_half(18, player_data["d"]["R"].z) #2bytes
	
	buf.encode_half(20, player_data["d"]["v"].x) #2bytes
	buf.encode_half(22, player_data["d"]["v"].y) #2bytes
	buf.encode_half(24, player_data["d"]["v"].z) #2bytes
	
	buf.encode_u8(26, player_data["d"]["a"]) #1byte
	buf.encode_u8(27, player_data["d"]["s"]) #1byte
	buf.encode_u8(28, player_data["d"]["r"]) #1byte
	
	var size = PLAYER_DATA_PACKET_SIZE
	if player_data.has("backend_id"):
		size = PLAYER_DATA_PACKET_SIZE + 4
		buf.resize(size)
		buf.encode_u32(PLAYER_DATA_PACKET_SIZE, player_data["backend_id"]) #4bytes
	
	if player_data.has("selection"):
		var index = size
		size += 55 + 2
		buf.resize(size)
		buf.encode_var(index, player_data["selection"]["name"])
		var char_path = player_data["selection"]["character"]
		buf.encode_u16(index + 55, Selector.find_id_from_character_path(char_path))
	
	return buf


func convert_packed_byte_array_to_player_data(buf: PackedByteArray):
	var player_data = {}
	player_data["d"] = {}
	#player_data["tick"] = buf.decode_u32(0)
	player_data["tick"] = 0 #Deprecated
	
	player_data["d"]["title"] = buf.decode_u8(0)
	player_data["d"]["right_hand"] = buf.decode_u8(1)
	player_data["d"]["vehicle"] = buf.decode_u8(2)
	player_data["d"]["reserved"] = buf.decode_u8(3)
	
	player_data["id"] = buf.decode_u32(4)
	player_data["d"]["p"] = Vector3(buf.decode_half(8), buf.decode_half(10), buf.decode_half(12))
	player_data["d"]["R"] = Vector3(buf.decode_half(14), buf.decode_half(16), buf.decode_half(18))
	player_data["d"]["v"] = Vector3(buf.decode_half(20), buf.decode_half(22), buf.decode_half(24))
	
	player_data["d"]["a"] = buf.decode_u8(26)
	player_data["d"]["s"] = buf.decode_u8(27)
	player_data["d"]["r"] = bool(buf.decode_u8(28))
	
	if buf.size() > PLAYER_DATA_PACKET_SIZE:
		#has selection and backend id
		player_data["selection"] = {"name": "", "character": "", "backend_id": 0}
		player_data["backend_id"] = buf.decode_u32(29)
		var _str = ""
		if buf.decode_var(33):
			_str = buf.decode_var(33)
		player_data["selection"]["name"] = _str
		var id = buf.decode_u16(33 + 55)
		player_data["selection"]["character"] = Selector.find_character_by_id_in_characters(id)["path"]
	
	return player_data


func convert_my_player_controller_to_packed_byte_array(player_data, _tick=-1):
	var buf = PackedByteArray()
	buf.resize(37)
	#if tick == -1:
	#	buf.encode_u32(0, TickManager.tick_counter) #4bytes
	#else:
	#	buf.encode_u32(0, tick) #4bytes
	
	buf.encode_u8(0, player_data["d"]["title"])
	buf.encode_u8(1, player_data["d"]["right_hand"])
	buf.encode_u8(2, player_data["d"]["vehicle"])
	buf.encode_u8(3, player_data["d"]["reserved"])


	buf.encode_half(4, player_data["d"]["c"]["i"].x) #2bytes
	buf.encode_half(6, player_data["d"]["c"]["i"].y) #2bytes
	
	buf.encode_half(8, player_data["d"]["c"]["l"].x) #2bytes
	buf.encode_half(10, player_data["d"]["c"]["l"].y) #2bytes
	buf.encode_half(12, player_data["d"]["c"]["l"].z) #2bytes
	buf.encode_u8(14, player_data["d"]["c"]["j"]) #1byte
	
	buf.encode_half(16, player_data["d"]["p"].x) #2bytes
	buf.encode_half(18, player_data["d"]["p"].y) #2bytes
	buf.encode_half(20, player_data["d"]["p"].z) #2bytes
	
	buf.encode_half(22, player_data["d"]["R"].x) #2bytes
	buf.encode_half(24, player_data["d"]["R"].y) #2bytes
	buf.encode_half(26, player_data["d"]["R"].z) #2bytes
	
	buf.encode_half(28, player_data["d"]["v"].x) #2bytes
	buf.encode_half(30, player_data["d"]["v"].y) #2bytes
	buf.encode_half(32, player_data["d"]["v"].z) #2bytes
	
	buf.encode_u8(34, player_data["d"]["a"]) #1byte
	buf.encode_u8(35, player_data["d"]["s"]) #1byte
	buf.encode_u8(36, player_data["d"]["r"]) #1byte
	
	return buf


func convert_packed_byte_array_to_my_controller(buf):
	var player_data = {}
	player_data["d"] = {"c": {}}
	player_data["input_tick"] = 0#Depricated
	
	player_data["d"]["title"] = buf.decode_u8(0)
	player_data["d"]["right_hand"] = buf.decode_u8(1)
	player_data["d"]["vehicle"] = buf.decode_u8(2)
	player_data["d"]["reserved"] = buf.decode_u8(3)
	
	player_data["d"]["c"]["i"] = Vector2(buf.decode_half(4), buf.decode_half(6))
	player_data["d"]["c"]["l"] = Vector3(buf.decode_half(8), buf.decode_half(10), buf.decode_half(12))
	player_data["d"]["c"]["j"] = bool(buf.decode_u8(14))
	
	player_data["d"]["p"] = Vector3(buf.decode_half(16), buf.decode_half(18), buf.decode_half(20))
	player_data["d"]["R"] = Vector3(buf.decode_half(22), buf.decode_half(24), buf.decode_half(26))
	player_data["d"]["v"] = Vector3(buf.decode_half(28), buf.decode_half(30), buf.decode_half(32))
	
	player_data["d"]["a"] = buf.decode_u8(34)
	player_data["d"]["s"] = buf.decode_u8(35)
	player_data["d"]["r"] = bool(buf.decode_u8(36))
	
	return player_data


static func angle_to_angle(from, to):
	return fposmod(to-from + PI, PI*2) - PI


func convert_angle_to_lerpable(_angle):
	var angle = _angle
	while(angle < 0):
		angle += 2 * PI
		
	while(angle > 2 * PI):
		angle -= 2 * PI
	return angle


func find_angle_gheta(angle):
	if angle < 0:
		angle += 2 * PI
	if angle >= 0 and angle <= PI / 2:
		return 1
	if angle >= PI / 2 and angle <= PI:
		return 2
	if angle >= PI and angle <= 3 * PI / 2:
		return 3
	if angle >= 3 * PI / 2 and angle <= 2 * PI:
		return 4
	return 1
	#return find_angle_gheta(convert_angle_to_lerpable(angle))


func distance(v1: Vector3, v2: Vector3):
	return (v1 - v2).length()


func var_sign(a):
	if a < 0:
		return -1
	if a > 0:
		return 1
	return 0


func is_mobile():
	if OS.get_name() == "Android":
		return true
		
	return false


func is_desktop():
	return not is_mobile()


func wait_frames(frames):
	for i in range(0, frames):
		await get_tree().process_frame


func wait_timer(time_sec):
	await get_tree().create_timer(time_sec).timeout


func pretify_seconds(seconds: int) -> String:
	@warning_ignore("integer_division")
	var hours = int(seconds / 3600)
	seconds -= (hours * 3600)
	@warning_ignore("integer_division")
	var minutes = int(seconds / 60)
	seconds -= minutes * 60
	
	if hours > 0:
		return "%02d:%02d:%02d" % [hours, minutes, seconds]
	return "%02d:%02d" % [minutes, seconds]


func tr_multiline(key):
	return tr(key).replace("\"", "").trim_prefix("\n").trim_suffix("\n")


func hide_keyboard():
	if is_mobile():
		DisplayServer.virtual_keyboard_hide()


func show_mouse():
	if Constants.is_desktop() and Constants.is_client():
		Input.set_mouse_mode(Input.MOUSE_MODE_VISIBLE)


func hide_mouse():
	if Constants.is_desktop() and Constants.is_client():
		Input.set_mouse_mode(Input.MOUSE_MODE_CAPTURED)


func has_daily_reward():
	var now = Time.get_unix_time_from_system()
	var last_time = DataSaver.get_item("daily_claim_time", 0)
	if now - last_time >= Constants.DAILY_REWARD_INTERVAL:
		return true
	return false


func next_daily_reward_time():
	if has_daily_reward():
		return 0

	var now = Time.get_unix_time_from_system()
	var last_time = DataSaver.get_item("daily_claim_time", 0)
	return Constants.DAILY_REWARD_INTERVAL - (now - last_time)


func kill_server():
	get_tree().quit(127)


#Client
func get_freeride_scene():
	if client == null:
		return
	return client.game_scene


func is_freeride_quiting():
	if get_freeride_scene() == null:
		return true

	return get_freeride_scene().quiting


func exit_me_from_sit(position):
	if Constants.client == null:
		return
	Selector.on_exit_sit()
	Constants.client.im_server_authoritive = false
	Constants.client.my_player_scene.global_position = position
	Constants.client.my_player_scene.set_state_to_idle(true)
	Constants.client.game_scene.player_inventory_container.visible = true


func set_shader_cache(state):
	if is_desktop():
		return
	ProjectSettings.set_setting("rendering/shader_compiler/shader_cache/enabled", state)
	ProjectSettings.save()


func pretify_time(_time):
	var time = _time
	var now = Time.get_unix_time_from_system()
	var diff = int(now - time)
	var type = tr(get_string_type_from_delta_time(diff))
	var value = get_value_from_delta_time(diff)
	return value + " " + type


func get_string_type_from_delta_time(sec):
	if sec <= 60:
		return "TIME_SEC"
	if sec <= 3600:
		return "TIME_MIN"
	if sec <= 24 * 3600:
		return "TIME_HOUR"
	return "TIME_DAY"


func get_value_from_delta_time(sec):
	if sec <= 60:
		return ""
	if sec <= 3600:
		return str(sec / 60)
	if sec <= 24 * 3600:
		return str(sec / 3600)
	return str(sec / 24 / 3600)


func show_no_access_toast():
	if is_desktop():
		print("no access")
		return
	
	NativeFunctions.toast(tr("NO_ACCESS"))


@rpc("reliable", "authority", "call_remote", Constants.CHANNEL_POLICE)
func show_toast(text):
	if is_desktop():
		print(text)
		return
	
	NativeFunctions.toast(text)


@rpc("reliable", "authority", "call_remote", Constants.CHANNEL_POLICE)
func show_toast_tr(text):
	show_toast(tr(text))


func im_dead():
	return DataSaver.get_item("im_dead", false)


func im_freezed():
	return DataSaver.get_item("im_freezed", false)


func is_multiplayer_connected():
	return ClientRPC.peer.get_connection_status() == ClientRPC.peer.CONNECTION_CONNECTED


func gps_time():
	var current_tool_data = DataSaver.get_item("tool", {})
	if not current_tool_data.has("gps"):
		return 0
	
	return clamp(current_tool_data["gps"] - Time.get_unix_time_from_system(), 0, 1000 * 1000)


func trim_cosmetics_data(cosmetics):
	var ret = cosmetics.duplicate(true)
	var to_remove_keys = []
	for key in ret.keys():
		if ret[key] == null:
			to_remove_keys.append(key)
	
	for key in to_remove_keys:
		ret.erase(key)
	
	if ret.has("weapon"):
		ret["w"] = ret["weapon"]
		ret.erase("weapon")
	if ret.has("food"):
		ret["f"] = ret["food"]
		ret.erase("food")
	if ret.has("mobile"):
		if ret["mobile"] != null:
			var id = ret["mobile"]["id"]
			var color = ret["mobile"]["color"]
			var packed = PackedByteArray()
			packed.resize(2)
			packed.encode_u8(0, id)
			packed.encode_u8(1, color)
			ret["m"] = packed
		ret.erase("mobile")
	if ret.has("title"):
		if ret["title"].has("title"):
			var job_id = JobManager.find_job_id_from_title(ret["title"]["title"])
			if job_id != null:
				ret["t"] = job_id
		ret.erase("title")
	
	return ret


func decode_cosmetics_data(cosmetics):
	var ret = {}
	if cosmetics.has("w"):
		ret["weapon"] = cosmetics["w"]
	if cosmetics.has("f"):
		ret["food"] = cosmetics["f"]
	if cosmetics.has("m"):
		var packed = cosmetics["m"] as PackedByteArray
		var id = packed.decode_u8(0)
		var color = packed.decode_u8(1)
		ret["mobile"] = {
			"id": id,
			"color": color,
		}
	if cosmetics.has("t"):
		var job = JobManager.jobs_data[cosmetics["t"]]
		ret["title"] = {
			"title": job["title"],
			"color": job["color"],
		}
	
	return ret


func is_cosmetic_data_empty(cosmetics):
	if cosmetics.has("title"):
		if cosmetics["title"] != null:
			if cosmetics["title"]["title"] != "":
				return false

	for key in cosmetics.keys():
		if key == "title":
			continue

		if cosmetics[key] != null:
			return false
	
	return true


func update_show_adder_just_values():
	if is_instance_valid(client):
		if client.game_scene:
			if client.game_scene.has_method("show_add_coins"):
				client.game_scene.coin_adder_ui.update_just_values()


func exit_mobile_state():
	if is_instance_valid(client):
		if client.my_player_scene:
			if client.my_player_scene.state == Character.State.STATE_MOBILE:
				InventoryManager.swap_mobile_and_hand()
			client.my_player_scene.mobileState.end_state()


func copy_to_clipboard(text):
	DisplayServer.clipboard_set(text)
	show_toast(tr("COPIED"))


func ClanMemberSort(a, b):
	if a["cup"] > b["cup"]:
		return true
	return false


func FriendOnlineSort(a, b):
	if a["is_online"] and not b["is_online"]:
		return true
	if not a["is_online"] and b["is_online"]:
		return false

	return false


func can_i_sit():
	if Selector.am_i_sit:
		return false

	var player = client.my_player_scene as Character
	if im_dead():
		return false
	if player.state in player.CANT_SIT_STATES:
		return false
	
	return true


func my_cosmetics_to_right_hand():
	return


func player_sync_data_to_cosmetics(data):
	var cosmetics = {
		"title": null,#Empty
		"hand": data["d"]["right_hand"],
		"reserved": data["d"]["reserved"],
	}
	if data["d"]["title"] != 255:
		cosmetics["title"] = JobManager.jobs_data[data["d"]["title"]].duplicate()
	
	return cosmetics


func get_my_inventory_container() -> PlayerInventoryContainer:
	return (Constants.client.game_scene as FreeRide).player_inventory_container


const PACKET_SIZE_GENERAL = 70
const PACKET_SIZE_INSIGHT = 30
const PACKET_SIZE_LIST_DATA = 15


func encode_player_list_data(player_data: Dictionary, buf: PackedByteArray, start: int):
	buf.resize(buf.size() + PACKET_SIZE_LIST_DATA)
	
	buf.encode_u32(start, player_data["id"]) #4bytes #Key
	buf.encode_u32(start + 4, player_data["backend_id"]) #4bytes
	buf.encode_u8(start + 8, player_data["d"]["title"])#1byte
	var char_path = player_data["selection"]["character"]
	buf.encode_u16(start + 9, Selector.find_id_from_character_path(char_path))#2Bytes
	buf.encode_half(start + 11, player_data["d"]["p"].x) #2bytes
	buf.encode_half(start + 13, player_data["d"]["p"].z) #2bytes
	
	return buf


func decode_player_list_data(buf: PackedByteArray, start: int):
	var player_data = {}
	player_data["d"] = {}
	
	player_data["id"] = buf.decode_u32(start + 0)
	player_data["backend_id"] = buf.decode_u32(start + 4)
	player_data["d"]["title"] = buf.decode_u8(start + 8)
	
	player_data["selection"] = {"name": "", "character": "", "backend_id": 0}
	var id = buf.decode_u16(start + 9)
	player_data["selection"]["character"] = Selector.find_character_by_id_in_characters(id)["path"]
	player_data["d"]["p"] = Vector3()
	player_data["d"]["p"].x = buf.decode_half(start + 11)
	player_data["d"]["p"].z = buf.decode_half(start + 13)
	
	return [player_data["id"], player_data]


func encode_player_data_general(player_data: Dictionary, buf: PackedByteArray, start: int):
	buf.resize(buf.size() + PACKET_SIZE_GENERAL)
	
	buf.encode_u32(start, player_data["id"]) #4bytes #Key
	buf.encode_u32(start + 4, player_data["backend_id"]) #4bytes
	buf.encode_u8(start + 8, player_data["d"]["title"])
	buf.encode_var(start + 9, player_data["selection"]["name"])#55bytes
	var char_path = player_data["selection"]["character"]
	buf.encode_u16(start + 64, Selector.find_id_from_character_path(char_path))#2Bytes
	buf.encode_half(start + 66, player_data["d"]["p"].x) #2bytes
	buf.encode_half(start + 68, player_data["d"]["p"].z) #2bytes
	
	return buf


func encode_player_data_insight(player_data: Dictionary, buf: PackedByteArray, start: int):
	buf.resize(buf.size() + PACKET_SIZE_INSIGHT)
	
	buf.encode_u8(start + 0, player_data["d"]["title"])
	buf.encode_u8(start + 1, player_data["d"]["right_hand"])
	buf.encode_u8(start + 2, player_data["d"]["vehicle"])
	buf.encode_u8(start + 3, player_data["d"]["vehicle_sit"])
	buf.encode_u8(start + 4, player_data["d"]["reserved"])
	
	buf.encode_u32(start + 5, player_data["id"]) #4bytes #KEY
	
	buf.encode_half(start + 9, player_data["d"]["p"].x) #2bytes
	buf.encode_half(start + 11, player_data["d"]["p"].y) #2bytes
	buf.encode_half(start + 13, player_data["d"]["p"].z) #2bytes
	
	buf.encode_half(start + 15, player_data["d"]["R"].x) #2bytes
	buf.encode_half(start + 17, player_data["d"]["R"].y) #2bytes
	buf.encode_half(start + 19, player_data["d"]["R"].z) #2bytes
	
	buf.encode_half(start + 21, player_data["d"]["v"].x) #2bytes
	buf.encode_half(start + 23, player_data["d"]["v"].y) #2bytes
	buf.encode_half(start + 25, player_data["d"]["v"].z) #2bytes
	
	buf.encode_u8(start + 27, player_data["d"]["a"]) #1byte
	buf.encode_u8(start + 28, player_data["d"]["s"]) #1byte
	buf.encode_u8(start + 29, player_data["d"]["r"]) #1byte
	
	return buf


func decode_player_data_general(buf: PackedByteArray, start: int):
	var player_data = {}
	player_data["d"] = {}
	
	player_data["id"] = buf.decode_u32(start + 0)
	player_data["backend_id"] = buf.decode_u32(start + 4)
	player_data["d"]["title"] = buf.decode_u8(start + 8)
	
	player_data["selection"] = {"name": "", "character": "", "backend_id": 0}
	var _str = ""
	if buf.decode_var(start +  9):
		_str = buf.decode_var(start + 9)
	player_data["selection"]["name"] = _str
	var id = buf.decode_u16(start + 64)
	player_data["selection"]["character"] = Selector.find_character_by_id_in_characters(id)["path"]
	player_data["d"]["p"] = Vector3()
	player_data["d"]["p"].x = buf.decode_half(start + 66)
	player_data["d"]["p"].z = buf.decode_half(start + 68)
	
	return [player_data["id"], player_data]


func decode_player_data_insight(buf: PackedByteArray, start: int):
	var player_data = {}
	player_data["d"] = {}
	
	player_data["d"]["title"] = buf.decode_u8(start + 0)
	player_data["d"]["right_hand"] = buf.decode_u8(start + 1)
	player_data["d"]["vehicle"] = buf.decode_u8(start + 2)
	player_data["d"]["vehicle_sit"] = buf.decode_u8(start + 3)
	player_data["d"]["reserved"] = buf.decode_u8(start + 4)
	
	player_data["id"] = buf.decode_u32(start + 5)
	player_data["d"]["p"] = Vector3(buf.decode_half(start +  9), buf.decode_half(start + 11), buf.decode_half(start + 13))
	player_data["d"]["R"] = Vector3(buf.decode_half(start + 15), buf.decode_half(start + 17), buf.decode_half(start + 19))
	player_data["d"]["v"] = Vector3(buf.decode_half(start + 21), buf.decode_half(start + 23), buf.decode_half(start + 25))
	
	player_data["d"]["a"] = buf.decode_u8(start + 27)
	player_data["d"]["s"] = buf.decode_u8(start + 28)
	player_data["d"]["r"] = bool(buf.decode_u8(start + 29))
	
	return [player_data["id"], player_data]


func pretty_print_vector(v:Vector3):
	print("(%.2f, %.2f, %.2f)" % [v.x, v.y, v.z])


func print_orphans(title):
	print("*****************")
	print(title)
	print("*****************")
	print_orphan_nodes()
	print("/////////////////")


func vector_is_nan(v:Vector3) -> bool:
	if is_nan(v.x):
		return true
	if is_nan(v.y):
		return true
	if is_nan(v.z):
		return true
	
	return false
