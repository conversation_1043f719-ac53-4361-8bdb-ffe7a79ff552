class_name RifleReloadState
extends Node

@onready var character: Character = $"../.."

var counter = 0
var total_time = 2.0

func _ready() -> void:
	pass

func run(delta):
	if character.controls != Constants.Controls.Player or Constants.is_server:
		return
	
	counter += delta
	#print(counter, " ", total_time)
	if counter >= total_time:
		# Reload animation finished, return to rifle idle
		character.rifleIdleState.start_state()
		on_reload_finished()
		return
	
	var just_jumped = character.player_client_jump()
	var input_dir = character.player_client_input(delta)
	
	# Allow movement during reload to transition to reload_run
	if input_dir != Vector2(0, 0):
		character.rifleReloadRunState.start_state()
		character.rifleReloadRunState.counter = counter
		return
	
	# Allow jumping during reload
	if just_jumped:
		character.state = character.State.RIFLE_JUMP
		return
	
	character.clientNetworkHistory.snapshot_input(Vector2(), character.velocity, false)


func start_state():
	if character.state == character.State.RIFLE_RELOAD:
		return
	
	character.state = character.State.RIFLE_RELOAD
	counter = 0
	
	# Get reload animation length
	#var animation_name = character.animation_prefix + character.RIFLE_RELOAD_ANIMATION
	#if character.animation_player.has_animation(animation_name):
		#total_time = character.animation_player.get_animation(animation_name).length
	#else:
		#total_time = 2.0  # Default reload time
	
	# Animation will be handled in handle_animation function


func end_state():
	counter = 0


func on_reload_finished():
	pass
	# This can be connected to reload logic (e.g., refill ammo)
	#print("Reload finished")
	# You can emit a signal here or call reload logic
