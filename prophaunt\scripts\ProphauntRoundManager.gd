extends Node
class_name ProphauntRoundManager

# Prophaunt round management system
# Handles round timing, win conditions, and round transitions

# Round data
var current_round: int = 1
var rounds_on_current_map: int = 0
var total_rounds_played: int = 0

# Timer data
var round_duration: float = Constants.PROPHAUNT_ROUND_TIME
var round_timer: float = 0.0
var round_start_time: float = 0.0
var round_end_time: float = 0.0

# State tracking
var round_active: bool = false
var is_round_ended: bool = false
var winner_team: Constants.ProphauntTeam = Constants.ProphauntTeam.HAUNTERS

# Player tracking
var total_props: Array = []
var haunters: Array = []

# Round statistics
var round_stats: Dictionary = {}

signal round_started(round_number: int)
signal round_ended(winner: Constants.ProphauntTeam, stats: Dictionary)
signal timer_updated(time_remaining: float)
signal props_eliminated(eliminated_count: int, total_count: int)
signal round_warning(warning_type: String, time_remaining: float)

func _ready():
	reset_round_data()


func start_round(round_number: int, props: Array, haunters_list: Array):
	"""Start a new round"""
	current_round = round_number
	total_props = props.duplicate()
	haunters = haunters_list.duplicate()
	
	# Initialize timer
	round_timer = round_duration
	round_start_time = Time.get_ticks_msec() / 1000.0
	round_active = true
	is_round_ended = false
	
	# Initialize round stats
	initialize_round_stats()
	
	round_started.emit(current_round)
	print("Round ", current_round, " started - Props: ", props_alive().size(), " Haunters: ", haunters.size())


func update_round(delta: float):
	"""Update round logic"""
	if not round_active or is_round_ended:
		return
	
	# Update timer
	round_timer -= delta
	timer_updated.emit(round_timer)
	
	# Check for time warnings
	check_time_warnings()
	
	# Check win conditions
	check_win_conditions()
	
	# Update round stats
	update_round_stats(delta)


func check_win_conditions():
	"""Check if any team has won"""
	if is_round_ended:
		return
	
	# Check if all props are dead
	if props_alive().size() == 0:
		end_round(Constants.ProphauntTeam.HAUNTERS, "all_props_eliminated")
		return
	
	# Check if time is up
	if round_timer <= 0:
		end_round(Constants.ProphauntTeam.PROPS, "time_expired")
		return


func check_time_warnings():
	"""Check for time-based warnings"""
	if round_timer <= 30 and round_timer > 29:
		round_warning.emit("30_seconds_remaining", round_timer)
	elif round_timer <= 10 and round_timer > 9:
		round_warning.emit("10_seconds_remaining", round_timer)
	elif round_timer <= 5 and round_timer > 4:
		round_warning.emit("5_seconds_remaining", round_timer)


func end_round(winning_team: Constants.ProphauntTeam, reason: String):
	"""End the current round"""
	if is_round_ended:
		return
	
	is_round_ended = true
	round_active = false
	winner_team = winning_team
	round_end_time = Time.get_ticks_msec() / 1000.0
	
	# Finalize round stats
	finalize_round_stats(reason)
	
	# Increment counters
	rounds_on_current_map += 1
	total_rounds_played += 1
	
	round_ended.emit(winning_team, round_stats)
	
	var team_name = "Haunters" if winning_team == Constants.ProphauntTeam.HAUNTERS else "Props"
	print("Round ", current_round, " ended - Winner: ", team_name, " (", reason, ")")


func eliminate_prop(prop_id: int):
	"""Eliminate a prop from the round"""
	if prop_id in props_alive():
		var player:Character = Constants.server.players[prop_id]
		player.prophaunt_player.health_system.die()
		props_eliminated.emit(props_alive().size() - 1, total_props.size())
		
		# Update stats
		if round_stats.has("eliminations"):
			round_stats["eliminations"] += 1
		
		print("Prop eliminated - Remaining: ", props_alive().size(), "/", total_props.size())


func get_time_remaining() -> float:
	"""Get remaining time in seconds"""
	return max(0, round_timer)


func get_time_elapsed() -> float:
	"""Get elapsed time in seconds"""
	return round_duration - round_timer


func get_round_progress() -> float:
	"""Get round progress as percentage (0.0 to 1.0)"""
	return (round_duration - round_timer) / round_duration


func get_props_survival_rate() -> float:
	"""Get the percentage of props still alive"""
	if total_props.size() == 0:
		return 0.0
	return float(props_alive().size()) / float(total_props.size())


func should_change_map() -> bool:
	"""Check if map should be changed"""
	return rounds_on_current_map >= Constants.PROPHAUNT_ROUNDS_PER_MAP


func reset_for_new_map():
	"""Reset counters for a new map"""
	rounds_on_current_map = 0


func reset_round_data():
	"""Reset all round data"""
	current_round = 1
	rounds_on_current_map = 0
	round_timer = round_duration
	round_active = false
	is_round_ended = false
	total_props.clear()
	haunters.clear()
	round_stats.clear()


func initialize_round_stats():
	"""Initialize round statistics"""
	round_stats = {
		"round_number": current_round,
		"start_time": round_start_time,
		"duration": round_duration,
		"props_count": total_props.size(),
		"haunters_count": haunters.size(),
		"eliminations": 0,
		"damage_dealt": 0,
		"shots_fired": 0,
		"grenades_thrown": 0,
		"hex_casts": 0,
		"disguise_changes": 0,
		"winner": -1,
		"win_reason": "",
		"props_survived": 0,
		"survival_rate": 0.0
	}


func update_round_stats(_delta: float):
	"""Update round statistics during gameplay"""
	# This would be called to update various stats
	# Stats are updated by other systems calling record_* functions
	pass


func finalize_round_stats(win_reason: String):
	"""Finalize round statistics"""
	round_stats["end_time"] = round_end_time
	round_stats["actual_duration"] = round_end_time - round_start_time
	round_stats["winner"] = winner_team
	round_stats["win_reason"] = win_reason
	round_stats["props_survived"] = props_alive().size()
	round_stats["survival_rate"] = get_props_survival_rate()


# Stat recording functions
func record_shot_fired():
	"""Record a shot being fired"""
	if round_stats.has("shots_fired"):
		round_stats["shots_fired"] += 1


func record_grenade_thrown():
	"""Record a grenade being thrown"""
	if round_stats.has("grenades_thrown"):
		round_stats["grenades_thrown"] += 1


func record_hex_cast():
	"""Record a hex being cast"""
	if round_stats.has("hex_casts"):
		round_stats["hex_casts"] += 1


func record_disguise_change():
	"""Record a disguise change"""
	if round_stats.has("disguise_changes"):
		round_stats["disguise_changes"] += 1


func record_damage_dealt(damage: int):
	"""Record damage being dealt"""
	if round_stats.has("damage_dealt"):
		round_stats["damage_dealt"] += damage


# Getters for UI and other systems
func get_current_round() -> int:
	return current_round


func get_rounds_on_map() -> int:
	return rounds_on_current_map


func get_total_rounds() -> int:
	return total_rounds_played


func get_props_alive_count() -> int:
	return props_alive().size()


func get_total_props_count() -> int:
	return total_props.size()


func get_haunters_count() -> int:
	return haunters.size()


func is_round_active() -> bool:
	return round_active and not round_ended


func get_winner() -> Constants.ProphauntTeam:
	return winner_team


func get_round_stats() -> Dictionary:
	return round_stats.duplicate()


# Time formatting utilities
func format_time(seconds: float) -> String:
	"""Format time as MM:SS"""
	@warning_ignore("integer_division")
	var minutes = int(seconds) / 60
	var secs = int(seconds) % 60
	return "%02d:%02d" % [minutes, secs]


func get_formatted_time_remaining() -> String:
	"""Get formatted time remaining"""
	return format_time(get_time_remaining())


func get_formatted_time_elapsed() -> String:
	"""Get formatted time elapsed"""
	return format_time(get_time_elapsed())


# Warning thresholds
func is_time_critical() -> bool:
	"""Check if time is in critical range (last 30 seconds)"""
	return round_timer <= 30


func is_time_urgent() -> bool:
	"""Check if time is urgent (last 10 seconds)"""
	return round_timer <= 10


func get_time_warning_level() -> String:
	"""Get current time warning level"""
	if round_timer <= 5:
		return "critical"
	elif round_timer <= 10:
		return "urgent"
	elif round_timer <= 30:
		return "warning"
	else:
		return "normal"


func props_alive():
	var ret = []
	for p in total_props:
		var player: Character = Constants.server.players[p]
		if player.prophaunt_player.is_alive():
			ret.append(p)
	
	return ret
