[gd_scene load_steps=4 format=3 uid="uid://ypd1m8pbvnbd"]

[ext_resource type="PackedScene" uid="uid://cy82bsxfj1ddv" path="res://prophaunt/scenes/PropObject.tscn" id="1_r5cqx"]
[ext_resource type="PackedScene" uid="uid://dclgblho5e5ar" path="res://prophaunt/maps/Source/ElectricalItem/dryer.tscn" id="2_0uu4k"]

[sub_resource type="BoxShape3D" id="BoxShape3D_bx0gf"]
size = Vector3(1.57926, 1.89085, 1.5365)

[node name="DryerProp" instance=ExtResource("1_r5cqx")]

[node name="Dryer" parent="Meshes" index="0" instance=ExtResource("2_0uu4k")]

[node name="CollisionShape3D" type="CollisionShape3D" parent="StaticBody3D" index="0"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0.00780094, 0.955784, -0.315422)
shape = SubResource("BoxShape3D_bx0gf")
