[gd_scene load_steps=4 format=3 uid="uid://djds2v2b7y3t2"]

[ext_resource type="PackedScene" uid="uid://cy82bsxfj1ddv" path="res://prophaunt/scenes/PropObject.tscn" id="1_r7rl6"]
[ext_resource type="PackedScene" uid="uid://dienblirq4e4v" path="res://prophaunt/maps/Source/ArenaProp/trophy.tscn" id="2_3g332"]

[sub_resource type="CapsuleShape3D" id="CapsuleShape3D_5q4rx"]
radius = 0.793472
height = 2.29074

[node name="TrophyProp" instance=ExtResource("1_r7rl6")]

[node name="Trophy" parent="Meshes" index="0" instance=ExtResource("2_3g332")]

[node name="CollisionShape3D" type="CollisionShape3D" parent="StaticBody3D" index="0"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 1.13283, -0.010301)
shape = SubResource("CapsuleShape3D_5q4rx")
