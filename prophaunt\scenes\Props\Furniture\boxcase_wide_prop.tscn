[gd_scene load_steps=4 format=3 uid="uid://diw7x314mo1ob"]

[ext_resource type="PackedScene" uid="uid://cy82bsxfj1ddv" path="res://prophaunt/scenes/PropObject.tscn" id="1_becxu"]
[ext_resource type="PackedScene" uid="uid://blbb281pklaw3" path="res://prophaunt/maps/Source/Furniture/boxcase_wide.tscn" id="2_fh1t5"]

[sub_resource type="BoxShape3D" id="BoxShape3D_n2ccm"]
size = Vector3(2.75008, 3.04532, 0.759491)

[node name="BoxcaseWideProp" instance=ExtResource("1_becxu")]

[node name="BoxcaseWide" parent="Meshes" index="0" instance=ExtResource("2_fh1t5")]

[node name="CollisionShape3D" type="CollisionShape3D" parent="StaticBody3D" index="0"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -0.00145161, 1.52552, 0.00761414)
shape = SubResource("BoxShape3D_n2ccm")
