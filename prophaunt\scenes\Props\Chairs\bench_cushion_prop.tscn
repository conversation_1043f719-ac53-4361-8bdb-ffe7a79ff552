[gd_scene load_steps=4 format=3 uid="uid://b0kfqcebrscnl"]

[ext_resource type="PackedScene" uid="uid://cy82bsxfj1ddv" path="res://prophaunt/scenes/PropObject.tscn" id="1_r1knh"]
[ext_resource type="PackedScene" uid="uid://xpguha24nbxo" path="res://prophaunt/maps/Source/Chairs/bench_cushion.tscn" id="2_s380u"]

[sub_resource type="BoxShape3D" id="BoxShape3D_4oi0e"]
size = Vector3(1.6248, 1.85498, 0.836761)

[node name="BenchCushionProp" instance=ExtResource("1_r1knh")]

[node name="BenchCushion" parent="Meshes" index="0" instance=ExtResource("2_s380u")]

[node name="CollisionShape3D" type="CollisionShape3D" parent="StaticBody3D" index="0"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0.000129759, 0.92334, 0.0027923)
shape = SubResource("BoxShape3D_4oi0e")
