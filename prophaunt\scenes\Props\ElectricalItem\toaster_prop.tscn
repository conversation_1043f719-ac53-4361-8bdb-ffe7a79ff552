[gd_scene load_steps=4 format=3 uid="uid://c4rlgs45vhtg6"]

[ext_resource type="PackedScene" uid="uid://cy82bsxfj1ddv" path="res://prophaunt/scenes/PropObject.tscn" id="1_q34tq"]
[ext_resource type="PackedScene" uid="uid://cm66xuy3vb7g" path="res://prophaunt/maps/Source/ElectricalItem/toaster.tscn" id="2_ypwcb"]

[sub_resource type="BoxShape3D" id="BoxShape3D_bx0gf"]
size = Vector3(0.750549, 0.517044, 0.406683)

[node name="ToasterProp" instance=ExtResource("1_q34tq")]

[node name="toaster" parent="Meshes" index="0" instance=ExtResource("2_ypwcb")]

[node name="CollisionShape3D" type="CollisionShape3D" parent="StaticBody3D" index="0"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -0.00796494, 0.268881, 0.00220019)
shape = SubResource("BoxShape3D_bx0gf")
