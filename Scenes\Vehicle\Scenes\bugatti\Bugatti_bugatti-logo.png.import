[remap]

importer="texture"
type="CompressedTexture2D"
uid="uid://b70uad74mmgys"
path.s3tc="res://.godot/imported/Bugatti_bugatti-logo.png-c3d9d708a572634c4cc835c664123d70.s3tc.ctex"
path.etc2="res://.godot/imported/Bugatti_bugatti-logo.png-c3d9d708a572634c4cc835c664123d70.etc2.ctex"
metadata={
"imported_formats": ["s3tc_bptc", "etc2_astc"],
"vram_texture": true
}
generator_parameters={
"md5": "d5948e9f1fae09d47829c081dc1f45db"
}

[deps]

source_file="res://Scenes/Vehicle/Scenes/bugatti/Bugatti_bugatti-logo.png"
dest_files=["res://.godot/imported/Bugatti_bugatti-logo.png-c3d9d708a572634c4cc835c664123d70.s3tc.ctex", "res://.godot/imported/Bugatti_bugatti-logo.png-c3d9d708a572634c4cc835c664123d70.etc2.ctex"]

[params]

compress/mode=2
compress/high_quality=false
compress/lossy_quality=0.7
compress/hdr_compression=1
compress/normal_map=0
compress/channel_pack=0
mipmaps/generate=true
mipmaps/limit=-1
roughness/mode=0
roughness/src_normal=""
process/fix_alpha_border=true
process/premult_alpha=false
process/normal_map_invert_y=false
process/hdr_as_srgb=false
process/hdr_clamp_exposure=false
process/size_limit=0
detect_3d/compress_to=0
