[gd_scene load_steps=4 format=3 uid="uid://bucal7afsra61"]

[ext_resource type="PackedScene" uid="uid://cy82bsxfj1ddv" path="res://prophaunt/scenes/PropObject.tscn" id="1_7gsbj"]
[ext_resource type="PackedScene" uid="uid://dxjaiqhtvkrhf" path="res://prophaunt/maps/Source/ElectricalItem/washer.tscn" id="2_orb1v"]

[sub_resource type="BoxShape3D" id="BoxShape3D_bx0gf"]
size = Vector3(1.5641, 1.88157, 1.5714)

[node name="WasherProp" instance=ExtResource("1_7gsbj")]

[node name="Washer" parent="Meshes" index="0" instance=ExtResource("2_orb1v")]

[node name="CollisionShape3D" type="CollisionShape3D" parent="StaticBody3D" index="0"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -0.0302504, 0.951144, 0.145321)
shape = SubResource("BoxShape3D_bx0gf")
