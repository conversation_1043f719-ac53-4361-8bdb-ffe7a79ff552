[gd_scene load_steps=6 format=4 uid="uid://bfmninyo2r632"]

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_uikya"]
resource_name = "Wall.001"
cull_mode = 2
metallic = 0.2

[sub_resource type="ArrayMesh" id="ArrayMesh_5om2j"]
_surfaces = [{
"aabb": AABB(-2.00001, -2.38419e-07, -2.4, 4.00035, 4, 0.800001),
"format": 34896613377,
"index_count": 540,
"index_data": PackedByteArray("NAABADAANAASAAEAJQAFACQAJQAHAAUAQwAFAAcAQwBCAAUAKQAPABcAKQAmAA8ASQAGAEsASQAUAAYAEAAGABQAEAAEAAYAEgAOABUAEgAKAA4AOgANABYAOgA5AA0ARgAJABEARgBEAAkAMQABAAMAMQAwAAEAHwAAACEAHwATAAAAHgANAAsAHgAjAA0ANgANADkANgALAA0ATAAMAE0ATAAIAAwAHgATAB8AHgALABMAQgARAAUAQgBGABEAOAAWAAIAOAA6ABYAAQAVAAMAAQASABUACAAUAAwACAAQABQATQAUAEkATQAMABQAJQAXAAcAJQApABcANQASADQANQAKABIAIgAdABoAIgAgAB0AJwAcACgAJwAYABwAJwAbABgAJwAmABsAKAAZACQAKAAcABkAIAAbAB0AIAAjABsAIgAZACEAIgAaABkAAgAhAAAAAgAiACEAFgAjACAAFgANACMAAgAgACIAAgAWACAAGAAfABwAGAAeAB8AGAAjAB4AGAAbACMAHAAhABkAHAAfACEAEQAkAAUAEQAoACQACQAmACcACQAPACYACQAoABEACQAnACgAGgApACUAGgAdACkAHQAmACkAHQAbACYAGgAkABkAGgAlACQANgAvADsANgAsAC8AMQAuACsAMQAzAC4ANQAtADIANQAsAC0AOAAqACsAOAA3ACoAMwAtAC4AMwAyAC0AOwAqADcAOwAvACoAFQAyADMAFQAOADIACgAyAA4ACgA1ADIAAwAzADEAAwAVADMALAA0AC8ALAA1ADQAKwAwADEAKwAqADAALwAwACoALwA0ADAAEwA3AAAAEwA7ADcAAgA3ADgAAgAAADcACwA7ABMACwA2ADsAKwA6ADgAKwAuADoALAA5AC0ALAA2ADkALgA5ADoALgAtADkARQBBAEcARQA/AEEASgBAADwASgBIAEAARAA/AEUARAA+AD8ASAA+AEAASABMAD4ARwA9AEMARwBBAD0ASwA8AD0ASwBKADwAFwBDAAcAFwBHAEMACQBFAA8ACQBEAEUADwBHABcADwBFAEcAPABGAEIAPABAAEYAQABEAEYAQAA+AEQAPQBCAEMAPQA8AEIABgBKAEsABgAEAEoAEABMAEgAEAAIAEwABABIAEoABAAQAEgAPwBJAEEAPwBNAEkAPgBNAD8APgBMAE0AQQBLAD0AQQBJAEsAUQBbAFAAUQBcAFsAVwBcAFgAVwBbAFwAWgBUAFIAWgBZAFQAVQBXAFgAVQBWAFcAVwBQAFsAVwBWAFAAUwBSAFQAUwBOAFIAUgBPAFoAUgBOAE8ATwBTAF0ATwBOAFMAWQBTAFQAWQBdAFMAVQBQAFYAVQBRAFAATwBcAFEATwBdAFwAVQBPAFEAVQBaAE8AWQBcAF0AWQBYAFwAVQBZAFoAVQBYAFkA"),
"name": "Wall.001",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 94,
"vertex_data": PackedByteArray("+f///6tMAAD//wAAq0wAAPn///9TswAA//8AAFOzAAAFAAAAq0wAAAAA//+rTAAABQAAAFOzAAAAAP//U7MAAJcZAAD/PwAAmBnMzP8/AABe5gAA/z8AAGDmzMz/PwAAlxkAAP+/AABg5szM/78AAF7mAAD/vwAAmBnMzP+/AACSAgAA/z8AAI0Cb/3/PwAAa/0AAP8/AABm/W/9/z8AAJICAAD/vwAAa/0AAP+/AABm/W/9/78AAI0Cb/3/vwAA/H/MzKtMAAD8f///q0wAAPx///9TswAA/H/MzFOzAAD6f2/9q0wAAPp/b/1TswAAjILMzP8/AACLgm/9/z8AAIuCb/3/vwAAjIL//6tMAACMgv//U7MAAIyCzMz/vwAAbn3//6tMAABuff//U7MAAG59zMz/vwAAbn3MzP8/AABsfW/9/z8AAGx9b/3/vwAA/P//f6tMAAD8//9/U7MAAF/m/3+rTAAAX+b/f1OzAABp/f9/U7MAAGn9/3+rTAAA/P9wfatMAAD8/3B9U7MAAF/mcH3/vwAAaf1wff+/AABp/XB9/z8AAF/mcH3/PwAAX+aOgv8/AAD7/46Cq0wAAPv/joJTswAAX+aOgv+/AABo/Y6C/78AAGj9joL/PwAAAgD/f6tMAAACAP9/U7MAAJgZ/3+rTAAAmBn/f1OzAACQAv9/q0wAAJAC/39TswAAAgCOgqtMAAACAI6CU7MAAJgZjoL/PwAAmBmOgv+/AACPAo6C/z8AAI8CjoL/vwAAkAJwff8/AACQAnB9/78AAAIAcH2rTAAAAgBwfVOzAACYGXB9/z8AAJgZcH3/vwAAlxkAAAAAAACYGczMAAAAAF7mAAAAAAAAYObMzAAAAACXGQAA//8AAGUmAAAAAAAAZSYAAP//AABg5szM/v8AAF7mAAD//wAAk9kAAP//AACT2f+//v8AAGUm/7/+/wAAmBnMzP7/AACT2QAAAAAAAJPZ/78AAAAAZSb/vwAAAAA=")
}]
blend_shape_mode = 0

[sub_resource type="ArrayMesh" id="ArrayMesh_38nj3"]
resource_name = "WallDoorwayWideUV_WallDoorwayWideUV_001"
_surfaces = [{
"aabb": AABB(-2.00001, -2.38419e-07, -2.4, 4.00035, 4, 0.800001),
"attribute_data": PackedByteArray("iHuBvoh7gb6Ie4G+iHuBvld8Uf1XfFH9V3xR/Wl3gb5pd4G+aXeBvml3gb77d2f9+3dn/ft3Z/0FeAT8BXgE/AV4BPxfd76+X3e+vl93vr5fd76+Mnwf/DJ8H/wyfB/8fXu9vn17vb59e72+fXu9vihfFUHhBZKN4QWSjapW5zUqIMONKiDDjTlp23e9VCaOvVQmjjY8YSlzOvWNczr1jZ2g8EDnBUmK5wVJirrnhlR5OqyKeTqsihCO8EDDVN2Kw1TdiqWhI2kwIHqKMCB6ijZpFUHsAoyNLHed/Cx3nfxtQdcriXa+vol2vr4oX9t3slcsjj599P0+ffT9REafPl58gL5efIC+WZfwQPICQ4oLfan8C32p/FiX8EC4V+OKHHf2/Rx39v0A8fFAlHaBvpR2gb4QjmpyUny9vlJ8vb5PLYmNT22vqU9tr6nGdlGgznwZoMZ2UaDOfBmg0nYJoL98YKC/fGCg0nYJoFQt5ooyRvT6Mkb0+iR2QqCkc72pdH0PoKRzvallfVegiEwD+zB2+p+ITAP7nRBhKaMt3Y1QbTepnRCfPsJ96aDCfemgpXNFqYi/8UDcdc6g3HXOoIdMe/vXfM6g13zOoNd8zqDEdrmgxHa5oMR2uaCJv4ZUqS2TijFGbPu4dgChuHYAobh2AKHIfBWhyHwVoch8FaGlofBAAS2SijNGfPqrVoBh+yzbjU5tJ6puQYBh0HUVodB1FaGjczWqEI7xQLN9MKGzfTChiUyL+uF879zhfO/c4Xzv3OF879zPdgbdz3YG3c92Bt3PdgbdqCCpxaggqcVPRLSNT0S0jaogucSqILnEVEQRi1REEYuqHbLEqh2yxCV2CN0ldgjdqB2ixagdosWLfevci33r3Oh8pd3ofKXd6Hyl3c52wN3OdsDdznbA3RCOcA6rIEHEqUS/ileXcA6rHTrE3HXj3dx1490pXxVBph0axtp9wt3afcLdOWkVQacgIcajRAiONTz3CKkgMcX7QweO+0MHjuN8N9zjfDfc43w33ON8N9zHdlHcx3ZR3Md2UdzHdlHcuedqcqkgMcUBRL2KAUS9igDxanKpHSrF3XUz3N11M9xFRvcIqR0qxc19EtzNfRLcy3ZL3ct2S93Ldkvdy3ZL3dV8NN3VfDTd1Xw03dV8NN2jHYPHox2Dx08WXY1PFl2NpSCZxqUgmcZUFruKVBa7iqMgicejIInHIHZN3SB2Td2lHZLGpR2SxoB9MN2AfTDdw3aV3MN2ldzDdpXcw3aV3Nd8etzXfHrc13x63Nd8etwVd+c1pB0Kx6MWsY2jFrGNiL8jaacgIcapFmiKqRZoihV31yukIBHH2XV23Nl1dtyIv2lyph0axsF9VdzBfVXcN2lPCqIgAsjQdSze0HUs3liXcA6kHQrH1X0M3tV9DN7Ldgjey3YI3st2CN7Ldgje2nzt3dp87d3afO3d2nzt3ShfTwqiHfvH+hWwjfoVsI2eoHAOpCARxwAWZ4oAFmeKkCAy1JIZE9peAsvK8xwJ1Tw7cNRRR3a7Ch0Iy0sCIdPwa/zKlCCH0kcCy9T3HF7TpSw5u0A7xdJPAnbRPSAj2k8CdtGUIIfSjhm+2+w728ZLAiHTOiDO2/s7MMBXNQyyODsa1rQsjrRPOxrMjCDd1ZtyC8ujINzLkCAy1J9yYMl9IIjcBjdlsJE5bNQ0AiHdfjnC3E0bWtNcTpuwixlX3f1UasD7HLPRA1BJsmBHy7T7HLPRRwLL1PRrUcmMIN3V7gN61kkbBdVDAnbWjTkW1kUbsNaVOcHSmhms1u1UFcc="),
"format": 34896613399,
"index_count": 540,
"index_data": PackedByteArray("tgAFAKcAtgA+AAUAfgASAHsAfgAZABIA8QAUABsA8QDtABQAigAxAE0AigCAADEACQEWAA8BCQFFABYANgAVAEQANgAOABUAPAAvAEcAPAAjAC8AygArAEoAygDGACsA+gAfADgA+gDyAB8AqwAGAA0AqwCoAAYAagAAAHEAagBAAAAAZwAsACYAZwB4ACwAvAAtAMgAvAAnAC0AFAEqABgBFAEeACoAZgA/AGkAZgAlAD8A6wA6ABMA6wD9ADoAwwBMAAkAwwDNAEwABABIAAsABAA9AEgAHQBDACkAHQA1AEMAFgFCAAYBFgEoAEIAfQBOABgAfQCLAE4AtwA7ALMAtwAiADsAdgBkAFoAdgBvAGQAhQBfAIkAhQBRAF8AhABbAFAAhACBAFsAiABVAHwAiABeAFUAcABdAGUAcAB5AF0AdQBUAHIAdQBXAFQACAByAAEACAB1AHIASgB3AG0ASgArAHcABwBuAHQABwBLAG4AUgBsAGEAUgBoAGwAUAB4AGcAUABbAHgAYABzAFYAYABrAHMAOQB6ABEAOQCHAHoAIACBAIQAIAAyAIEAHwCGADgAHwCDAIYAWQCMAH8AWQBiAIwAYwCCAI0AYwBcAIIAWAB7AFMAWAB+AHsAuwCjAM8AuwCXAKMAqQCgAJIAqQCxAKAAuQCcAK4AuQCYAJwAxACQAJQAxADAAJAAsACaAJ4AsACtAJoA0ACOAL4A0ACkAI4ARgCsAK8ARgAuAKwAJACuADAAJAC5AK4ADACyAKoADABJALIAlgC0AKIAlgC4ALQAlQCoAKsAlQCRAKgApQCmAI8ApQC1AKYAQQC/AAIAQQDRAL8ACgDBAMUACgADAMEAJQDOAD8AJQC6AM4AkwDMAMIAkwChAMwAmQDJAJ0AmQC9AMkAnwDHAMsAnwCbAMcA9wDnAP8A9wDfAOcACgHlANMACgEEAeUA9QDhAPkA9QDdAOEAAwHaAOIAAwETAdoAAAHWAO4AAAHoANYAEAHVANkAEAEMAdUATwDvABoATwABAe8AIQD4ADMAIQD0APgAMQD+AE0AMQD2AP4A0gD8AOoA0gDkAPwA4wDzAPsA4wDbAPMA2ADsAPAA2ADUAOwAFwANAREBFwAQAA0BNAASAQIBNAAcABIBDwAFAQsBDwA3AAUB3gAHAeYA3gAXAQcB3AAZAeAA3AAVARkB6QAOAdcA6QAIAQ4BJQFHASEBJQFLAUcBOgFNAT4BOgFJAU0BQwEuASgBQwE/AS4BMgE4ATwBMgE1ATgBOQEiAUgBOQE2ASIBLAEpAS8BLAEbASkBKgEgAUYBKgEcASABHgErAU8BHgEaASsBQgEtATABQgFRAS0BNAEjATcBNAEnASMBHQFKASQBHQFOAUoBMwEfASYBMwFFAR8BQQFMAVABQQE9AUwBMQFAAUQBMQE7AUAB"),
"material": SubResource("StandardMaterial3D_uikya"),
"name": "Wall.001",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 338,
"vertex_data": PackedByteArray("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")
}]
blend_shape_mode = 0
shadow_mesh = SubResource("ArrayMesh_5om2j")

[sub_resource type="BoxShape3D" id="BoxShape3D_5yy6l"]
size = Vector3(0.6, 4, 0.8)

[sub_resource type="BoxShape3D" id="BoxShape3D_antvl"]
size = Vector3(1.00366, 3.99072, 0.8)

[node name="WallDoorwayWide" type="MeshInstance3D" groups=["VisibleGroup0"]]
mesh = SubResource("ArrayMesh_38nj3")
skeleton = NodePath("")

[node name="StaticBody3D" type="StaticBody3D" parent="."]
collision_layer = 2

[node name="CollisionShape3D" type="CollisionShape3D" parent="StaticBody3D"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -1.70026, 2, -2)
shape = SubResource("BoxShape3D_5yy6l")

[node name="CollisionShape3D2" type="CollisionShape3D" parent="StaticBody3D"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 1.7026, 2, -2.00078)
shape = SubResource("BoxShape3D_5yy6l")

[node name="CollisionShape3D3" type="CollisionShape3D" parent="StaticBody3D"]
transform = Transform3D(-4.37114e-08, -1, 0, 1, -4.37114e-08, 0, 0, 0, 1, -0.00122064, 3.50017, -1.99736)
shape = SubResource("BoxShape3D_antvl")
