extends CharacterBody3D
class_name Character 

signal attack(attack_position)
signal cook_finished
signal heal_finished
signal jail_me
signal rifle_shoot(start_pos, end_pos)
signal grenade_shoot(start_pos, direction)

enum State {STATE_IDLE, STATE_WALK, STATE_RUN, STATE_JUMP, #3
			STATE_SLIDE, SHOW, STATE_FREE_RIDE,  #6
			STATE_ANIMATION, STATE_AUTHORITIVE,
			STATE_ATTACK, STATE_COOK, STATE_HEAL,
			STATE_ACTION_ANIMATION, STATE_DEAD, STATE_FREEZE,
			STATE_MOBILE, STATE_FOLLOW_PATH, STATE_FOLLOW_RAMP, STATE_FREEZE_DEATH, #18
			RIFLE_IDLE, RIFLE_RUN, RIFLE_RELOAD, RIFLE_RELOAD_RUN, RIFLE_JUMP, RIFLE_SHOOT, RIFLE_SHOOT_RUN}
var RIFLE_STATES = [State.RIFLE_IDLE, State.RIFLE_RUN, State.RIFLE_RELOAD,
 State.RIFLE_RELOAD_RUN, State.RIFLE_JUMP, State.RIFLE_SHOOT, State.RIFLE_SHOOT_RUN]
var RIFLE_SHOOT_STATES = [State.RIFLE_SHOOT, State.RIFLE_SHOOT_RUN]
var RIFLE_RELOAD_STATES = [State.RIFLE_RELOAD, State.RIFLE_RELOAD_RUN]
var state = State.STATE_IDLE
var last_state = State.STATE_IDLE
var controls = Constants.Controls.Remote
var SET_IDLE_STATES = [State.STATE_SLIDE, State.STATE_ATTACK, State.STATE_COOK,
 State.STATE_FREE_RIDE, State.STATE_ANIMATION, State.STATE_HEAL,
 State.STATE_ACTION_ANIMATION, State.STATE_DEAD, State.STATE_FREEZE,
 State.STATE_MOBILE, State.STATE_FOLLOW_PATH, State.STATE_FOLLOW_RAMP, State.STATE_FREEZE_DEATH,
 State.RIFLE_IDLE, State.RIFLE_RUN, State.RIFLE_RELOAD, State.RIFLE_RELOAD_RUN, State.RIFLE_JUMP, State.RIFLE_SHOOT, State.RIFLE_SHOOT_RUN]

var SPEED = 4.0
var AIR_SPEED = 3.0
var SLIDE_SPEED = 7.0
var JUMP_VELOCITY = 4
var velocity_cap = Vector3(30, 30, 30)
var slide_enabled = true

var statManager: CharacterStatManager

var ON_FLOOR_TIME = 0.2
var camera_controller
var controller: TouchController
var ragdoll = false
var ragdoll_counter = 0
var ragdoll_disable_counter = 0
var RAGDOLL_DISABLE_TIMER = 2.0
var RAGDOLL_TIME = 1.5 #Secs
var FREE_RIDE_TIME = 1.5 #Secs
var free_ride_counter = 0

@export var debug = false

@onready var collision_shape = $CollisionShape3D
@onready var name_label = $NameLabel
@onready var title_label = $TitleLabel


var gravity = ProjectSettings.get_setting("physics/3d/default_gravity")
@onready var animation_player:AnimationPlayer = $AnimationPlayer
@onready var particles = $MoveParticle
@onready var emote = $Emote
@onready var emote_animated_sprite = $Emote/AnimatedSprite
@onready var chat_message = $ChatMessage
@onready var chat_label = $ChatMessage/SubViewport/Panel/Label
@onready var chat_viewport = $ChatMessage/SubViewport
@onready var player_area = $PlayerArea
@onready var attack_place_holder = $AttackPlaceHolder
@onready var throw_place_holder = $ThrowPlaceHolder
@onready var throw_place_holder2 = $ThrowPlaceHolder2
@onready var front_camera_pos = $FrontCameraPlaceHolder
@onready var selfie_camera_pos = $SelfieCameraPlaceHolder


#State nodes
@onready var actionState: ActionState = $States/AnimationAction
@onready var deadState: DeadState = $States/DeadState
@onready var freezeState: FreezeState = $States/FreezeState
@onready var mobileState: MobileState = $States/MobileState
@onready var followPathState: FollowPathState = $States/FollowPathState
@onready var followRampState: FollowRampState = $States/FollowRampState
@onready var freezeDeathState: FreezeDeathState = $States/FreezeDeathState

# Rifle state nodes
@onready var rifleIdleState: RifleIdleState = $States/RifleIdleState
@onready var rifleRunState: RifleRunState = $States/RifleRunState
@onready var rifleReloadState: RifleReloadState = $States/RifleReloadState
@onready var rifleReloadRunState: RifleReloadRunState = $States/RifleReloadRunState
@onready var rifleJumpState: RifleJumpState = $States/RifleJumpState
@onready var rifleShootState: RifleShootState = $States/RifleShootState
@onready var rifleShootRunState: RifleShootRunState = $States/RifleShootRunState


var last_look_at_position = null
var last_direction = Vector3(0, 0, 1)
var direction = Vector3(0, 0, 0)

var IDLE_ANIMATION = "Animation_Animal_Final/Idle"
var WALK_ANIMATION = "Drunk-RunForward"
var RUN_ANIMATION = "Animation_Animal_Final/Simple Run"
var JUMP_ANIMATION = "Animation_Animal_Final/Jump"
var SLIDE_ANIMATION = "Animation_Animal_Final/Slide"
var FALL_FRONT_COLLIDE = "Animation_Animal_Final/Hit"
var FALL_BEHIND_COLLIDE = "Animation_Animal_Final/Hit-Back"
var SIT_ANIMATION = "Animation_Animal_Final/Siting_Talking"
var LAY_ANIMATION = "Animation_Animal_Final/SleapingSpeak"
var EAT_ANIMATION = "Animation_Animal_Final/Siting_eat"

# Rifle animations
var RIFLE_IDLE_ANIMATION = "rifle_idle"
var RIFLE_RUN_ANIMATION = "rifle_run"
var RIFLE_RELOAD_ANIMATION = "rifle_reload"
var RIFLE_RELOAD_RUN_ANIMATION = "rifle_reload_run"
var RIFLE_JUMP_ANIMATION = "rifle_jump"
var RIFLE_SHOOT_ANIMATION = "rifle_firing"
var RIFLE_SHOOT_RUN_ANIMATION = "rifle_run_firing"
var animation_state_animations = [
	"Animation_Animal_Final/Arms_HipHop_Dance",
	"Animation_Animal_Final/Breakdance_1990",
	"Animation_Animal_Final/Breakdance_1990_2",
	"Animation_Animal_Final/Breakdance_Ending",
	"Animation_Animal_Final/Breakdance_Freeze_Var2",
	"Animation_Animal_Final/Breakdance_Uprock_Var2",
	"Animation_Animal_Final/Dancing",
	"Animation_Animal_Final/Dancing_Twerk",
	"Animation_Animal_Final/HipHop_Dancing",
	"Animation_Animal_Final/Northern_Soul_Spin",
	"Animation_Animal_Final/Rumba_Dancing",
	"Animation_Animal_Final/Salsa_Dancing",
	"Animation_Animal_Final/Shuffling",
	"Animation_Animal_Final/Silly_Dancing",
	"Animation_Animal_Final/Swing_Dance",
	"Animation_Animal_Final/Tut_HipHop_Dance",
	"Animation_Animal_Final/Twist_Dance",
	"Animation_Animal_Final/Victory",
	"Animation_Animal_Final/Wave_HipHop_Dance",
	"Animation_Animal_Final/WavingHand",
	"Animation_Animal_Final/Brutal_Assassination",
	"Animation_Animal_Final/Bartending",
	"Animation_Animal_Final/Cheering2",
	"Animation_Animal_Final/Cheering",
	"Animation_Animal_Final/Entry",
	"Animation_Animal_Final/Kneeling",
	"Animation_Animal_Final/PainGesture",
	"Animation_Animal_Final/Praying",
	"Animation_Animal_Final/Situps",
	"Animation_Animal_Final/SleepingIdel",
	"Animation_Animal_Final/ThankFull",
	"Animation_Animal_Final/WipingSweat",
	"Animation_Animal_Final/GuitarPlayingClassic",
	"Animation_Animal_Final/GuitarPlayingRock",
	"Animation_Animal_Final/AdministeringCpr",
	"Animation_Animal_Final/ArmWaveDancing",
	"Animation_Animal_Final/FallFlat",
	"Animation_Animal_Final/BrooklynUprock",
	"Animation_Animal_Final/Salute",
]
var animation_prefix = "Animation_Animal_Final/"
var attack_animations = ["Animation_Animal_Final/MeleeAttack360"]
var sit_animations = ["Animation_Animal_Final/SitStandingVictory"]
var cook_animations = ["Animation_Animal_Final/PulingLever"]
var heal_animations = ["Animation_Animal_Final/AdministeringCpr"]
var death_animation = "Death"
var pistol_shoot_animation = "PistolShot"
var baton_animation = "Batom"
var punch_animation = "Punch"
var mobile_animation = "Mobilephone"
var throw_animation = "Throw"
var grocery_eat_animation = "EatingAnimation"#Eat in hand grocery item

var not_loop_list = []# Fill in set_two_part_animations

var animation_list

#For Server
var player_id = null
var server_player_data = null
var cosmetic_data = {}#Use just in client

var server: Server = null
var client: Client = null

var jump_sound_played = false
var jump_pressed = false #for sound handling
var ragdoll_sound_played = false
var last_woosh_tick = 0
var WOOSH_TIME = 500 #mili seconds

var clientNetworkHistory = null
var serverNetworkHistory = null

var warmth_ratio = 1.0
var is_in_cold_area = false
var tube = null#Used in followRampState
var iceCube = null#Used in FreezeState

var is_in_lava_area = false

var remote_vehicle#MyVehicle or ClientSyncVehicle
var remote_sit:VehicleSit
var prophaunt_player: ProphauntPlayer = null


const NO_MOBILE_STATES = [Character.State.SHOW, Character.State.STATE_FREE_RIDE, State.STATE_MOBILE,
	 Character.State.STATE_ATTACK, Character.State.STATE_COOK, Character.State.STATE_HEAL,
	Character.State.STATE_ACTION_ANIMATION, Character.State.STATE_DEAD, Character.State.STATE_FREEZE,
	State.STATE_FOLLOW_PATH, State.STATE_FOLLOW_RAMP, State.STATE_FREEZE_DEATH,
	State.RIFLE_IDLE, State.RIFLE_RUN, State.RIFLE_RELOAD, State.RIFLE_RELOAD_RUN, State.RIFLE_JUMP, State.RIFLE_SHOOT, State.RIFLE_SHOOT_RUN]

const CANT_SIT_STATES = [Character.State.SHOW, Character.State.STATE_FREE_RIDE, State.STATE_MOBILE,
	 Character.State.STATE_ATTACK, Character.State.STATE_COOK, Character.State.STATE_HEAL,
	Character.State.STATE_ACTION_ANIMATION,
	 Character.State.STATE_DEAD, Character.State.STATE_FREEZE, State.STATE_FREEZE_DEATH,
	State.RIFLE_IDLE, State.RIFLE_RUN, State.RIFLE_RELOAD, State.RIFLE_RELOAD_RUN, State.RIFLE_JUMP, State.RIFLE_SHOOT, State.RIFLE_SHOOT_RUN]

const NO_SHOOT_STATES = [Character.State.SHOW, Character.State.STATE_FREE_RIDE, State.STATE_MOBILE,
	 	Character.State.STATE_ATTACK, Character.State.STATE_COOK,
		Character.State.STATE_HEAL, Character.State.STATE_ACTION_ANIMATION,
		Character.State.STATE_DEAD, Character.State.STATE_FREEZE, State.STATE_FOLLOW_RAMP,
		State.STATE_FOLLOW_PATH, State.STATE_FREEZE_DEATH, State.RIFLE_RELOAD, State.RIFLE_RELOAD_RUN]

var backup_collision_mask
var backup_collision_layer
func _ready():
	title_label.visible = false
	particles.emitting = false
	set_animations()
	if has_node("ServerNetworkHistory"):
		serverNetworkHistory = get_node("ServerNetworkHistory")
	if has_node("ClientNetworkHistory"):
		clientNetworkHistory = get_node("ClientNetworkHistory")
	backup_collision_layer = collision_layer
	backup_collision_mask = collision_mask
	emote.visible = false
	chat_message.visible = false
	attack_place_holder.visible = false
	throw_place_holder.visible = false
	throw_place_holder2.visible = false
	statManager = CharacterStatManager.new()
	statManager.init(self)
	add_child(statManager)


func handle_velocity_cap():
	if Constants.is_server:
		return
	var capped = false
	if is_client_and_player():
		if state in [State.STATE_FOLLOW_PATH, State.STATE_FOLLOW_RAMP]:
			return

	if abs(velocity.x) > velocity_cap.x:
		velocity.x = 0
		capped = true
	if abs(velocity.y) > velocity_cap.y:
		velocity.y = 0
		capped = true
	if abs(velocity.z) > velocity_cap.z:
		velocity.z = 0
		capped = true
	if capped:
		print("capped velocity")


func _physics_process(delta):
	step_forward(-1, delta)
	if Constants.is_client() and is_client_and_player():
		for i in get_slide_collision_count():
			var c = get_slide_collision(i)
			if c.get_collider() is Vehicle:
				c.get_collider().apply_central_impulse(-c.get_normal() * 1)
		
		if VehicleManager.ami_in_vehicle():
			update_remote_character_from_vehicle_sit(VehicleManager.my_vehicle_sit)
			animation_player.play(animation_prefix + VehicleManager.my_vehicle_sit.animation)
			(animation_player as AnimationPlayer).speed_scale = VehicleManager.my_vehicle_sit.animtion_speed()
			clientNetworkHistory.snapshot_input(Vector2(), Vector3(), false)
		else:
			(animation_player as AnimationPlayer).speed_scale = 1.0
		
	if is_client_and_remote():
		if is_instance_valid(remote_vehicle):
			if is_instance_valid(remote_sit):
				update_remote_character_from_vehicle_sit(remote_sit)
				pass


func step_forward(debug_tick, delta):
	if state == State.SHOW:
		return
	
	
	#if is_me():
		#print("1: ", state)
	handle_velocity_cap()
	if is_instance_valid(statManager):
		statManager.run(delta)
	handle_emote(delta)
	if state == State.STATE_AUTHORITIVE:
		handle_authoritive_state(delta)
		return
	
	if is_server_non_local():
		#if Constants.game_mode == Constants.GameMode.Race:
		if (server == null or server.state == Constants.ServerState.Lobby):
			return
	
	if Constants.LOCAL_MODE == false:
		if (server == null or server.state == Constants.ServerState.CountDown):
			return
	
	if controls == Constants.Controls.Player and state != State.STATE_IDLE:
		if not state in SET_IDLE_STATES:
			state = State.STATE_IDLE
			enable_gravity()
	
	handle_on_floor(delta)
	
	if not on_floor and state != State.STATE_FREE_RIDE:
		velocity.y -= gravity * delta
	
	
	if Constants.is_client() and not Constants.LOCAL_MODE:
		if server == null:
			return
		if server.state == Constants.ServerState.CountDown:
			return
	
	ragdoll_disable_counter += delta
	if ragdoll_disable_counter > 20000:
		ragdoll_disable_counter = 20000
	if state == State.STATE_FREE_RIDE:
		handle_free_ride_mode(delta, debug_tick == -1)
	elif ragdoll:
		handle_ragdoll_mode(delta, debug_tick == -1)
	elif state == State.STATE_ANIMATION:
		handle_animation_state(delta)
	elif state == State.STATE_ATTACK:
		handle_attack_state(delta)
	elif state == State.STATE_COOK:
		handle_cook_state(delta)
	elif state == State.STATE_HEAL:
		handle_heal_state(delta)
	elif state == State.STATE_ACTION_ANIMATION:
		actionState.run(delta)
	elif state == State.STATE_DEAD:
		deadState.run(delta)
	elif state == State.STATE_FREEZE:
		freezeState.run(delta)
	elif state == State.STATE_MOBILE:
		mobileState.run(delta)
	elif state == State.STATE_FOLLOW_PATH:
		followPathState.run(delta)
	elif state == State.STATE_FOLLOW_RAMP:
		followRampState.run(delta)
	elif state == State.STATE_FREEZE_DEATH:
		freezeDeathState.run(delta)
	elif state == State.RIFLE_IDLE:
		rifleIdleState.run(delta)
	elif state == State.RIFLE_RUN:
		rifleRunState.run(delta)
	elif state == State.RIFLE_RELOAD:
		rifleReloadState.run(delta)
	elif state == State.RIFLE_RELOAD_RUN:
		rifleReloadRunState.run(delta)
	elif state == State.RIFLE_JUMP:
		rifleJumpState.run(delta)
	elif state == State.RIFLE_SHOOT:
		rifleShootState.run(delta)
	elif state == State.RIFLE_SHOOT_RUN:
		rifleShootRunState.run(delta)
	else:
		handle_normal_mode(delta, debug_tick == -1)
	
	#if is_me():
		#print("2: ", state)
	
	if last_state != state:
		if last_state == State.STATE_MOBILE:
			mobileState.end_state()
		if last_state == State.STATE_FOLLOW_RAMP:
			followRampState.end_state()
		if last_state == State.STATE_FREEZE_DEATH:
			freezeDeathState.unfreeze()


	if controls == Constants.Controls.Player or (Constants.is_server and Constants.LOCAL_MODE == false):
		move_and_slide()
		check_y_in_client()
		handle_animation()
	else:
		if Constants.is_client():
			#Bot or Remote
			var graphics_level = DataSaver.get_item("graphics_setting", 1)
			if graphics_level > 1:# High and Max
				move_and_slide()
				check_y_in_client()
				handle_animation()
	#if is_me():
		#print("3: ", state)
	last_state = state


func set_camera(cam):
	camera_controller = cam


func set_controller(control):
	controller = control
	controller.holdster.connect(holdster)
	controller.aim_shoot.connect(start_shoot)
	controller.arrest.connect(arrest_action)
	controller.snowball_shoot.connect(start_snowball_shoot)
	controller.drug.connect(heal_drug)
	controller.item_action.connect(item_action_start)


func handle_animation():
	if ragdoll:
		return
	if VehicleManager.ami_in_vehicle():
		return
	
	if is_instance_valid(particles):
		particles.emitting = false
	if state == State.STATE_ANIMATION:
		pass
	if state == State.STATE_IDLE:
		animation_player.play(IDLE_ANIMATION)
	if state == State.STATE_WALK:
		animation_player.play(WALK_ANIMATION)
	if state == State.STATE_RUN:
		if is_instance_valid(particles):
			if DataSaver.get_item("particles", true):
				particles.emitting = true
		animation_player.play(RUN_ANIMATION)
	if state == State.STATE_JUMP:
		animation_player.play(JUMP_ANIMATION)
		if Constants.is_client() and controls == Constants.Controls.Player and jump_sound_played == false and jump_pressed:
			jump_sound_played = true
			jump_pressed = false
			play_jump_sound()
	else:
		jump_sound_played = false
	if state == State.STATE_SLIDE:
		if last_state != State.STATE_SLIDE and Constants.is_client() and controls == Constants.Controls.Player:
			if Time.get_ticks_msec() - last_woosh_tick > WOOSH_TIME:
				last_woosh_tick = Time.get_ticks_msec()
				SoundManager.play_woosh_sound()
		jump_pressed = false
		animation_player.play(SLIDE_ANIMATION, -1, Constants.CHARACTER_SLIDE_ANIMATION_SPEED)

	# Handle rifle state animations
	#print("animation: ", animation_player.current_animation, " ", state)
	if state == State.RIFLE_IDLE:
		animation_player.play(animation_prefix + RIFLE_IDLE_ANIMATION)
	if state == State.RIFLE_RUN:
		if is_instance_valid(particles):
			if DataSaver.get_item("particles", true):
				particles.emitting = true
		animation_player.play(animation_prefix + RIFLE_RUN_ANIMATION)
	if state == State.RIFLE_JUMP:
		animation_player.play(animation_prefix + RIFLE_JUMP_ANIMATION)
	if state == State.RIFLE_RELOAD:
		animation_player.play(animation_prefix + RIFLE_RELOAD_ANIMATION)
	if state == State.RIFLE_RELOAD_RUN:
		animation_player.play(animation_prefix + RIFLE_RELOAD_RUN_ANIMATION)
	if state == State.RIFLE_SHOOT:
		animation_player.play(animation_prefix + RIFLE_SHOOT_ANIMATION)
	if state == State.RIFLE_SHOOT_RUN:
		animation_player.play(animation_prefix + RIFLE_SHOOT_RUN_ANIMATION)


func set_animations():
	animation_list = [IDLE_ANIMATION, WALK_ANIMATION, RUN_ANIMATION,
	 JUMP_ANIMATION, SLIDE_ANIMATION, FALL_BEHIND_COLLIDE, FALL_FRONT_COLLIDE, SIT_ANIMATION, LAY_ANIMATION]
	for anim in animation_state_animations:
		animation_list.append(anim)
	for anim in attack_animations:
		animation_list.append(anim)
	animation_player.get_animation(RUN_ANIMATION).loop_mode = 1
	for anim in sit_animations:
		animation_list.append(anim)
	for anim in cook_animations:
		animation_list.append(anim)
	animation_list.append(EAT_ANIMATION)
	for anim in heal_animations:
		animation_list.append(anim)
	set_two_part_animations()


func set_two_part_animations():
	animation_list.append(animation_prefix + death_animation)
	animation_list.append(animation_prefix + pistol_shoot_animation)
	animation_list.append(animation_prefix + baton_animation)
	animation_list.append(animation_prefix + punch_animation)
	animation_list.append(animation_prefix + mobile_animation)
	animation_list.append(animation_prefix + throw_animation)
	animation_list.append(animation_prefix + grocery_eat_animation)
	animation_list.append(animation_prefix + "MotorDriver")
	animation_list.append(animation_prefix + "skate_riding")
	animation_list.append(animation_prefix + "siting_bike")
	animation_list.append(animation_prefix + "Scooter_riding")
	animation_list.append(animation_prefix + "Forghoon_ride")
	
	animation_list.append(animation_prefix + RIFLE_IDLE_ANIMATION)
	animation_list.append(animation_prefix + RIFLE_RUN_ANIMATION)
	animation_list.append(animation_prefix + RIFLE_RELOAD_ANIMATION)
	animation_list.append(animation_prefix + RIFLE_RELOAD_RUN_ANIMATION)
	animation_list.append(animation_prefix + RIFLE_SHOOT_ANIMATION)
	animation_list.append(animation_prefix + RIFLE_SHOOT_RUN_ANIMATION)
	animation_list.append(animation_prefix + RIFLE_JUMP_ANIMATION)
	
	not_loop_list = ["Brutal_Assassination", "Praying", "Kneeling",
	 animation_prefix + mobile_animation, animation_prefix + death_animation]


var has_attacked = false
var attack_state_counter = 0
var attack_state_total_time = 0
var attack_state_attack_time = 0
func handle_attack_state(delta):
	if controls != Constants.Controls.Player or Constants.is_server:
		return

	attack_state_counter += delta
	if attack_state_counter >= attack_state_attack_time and has_attacked == false:
		has_attacked = true
		attack.emit(attack_place_holder.global_position)
		SoundManager.play_attack_sound()
		
	if attack_state_counter >= attack_state_total_time:
		state = State.STATE_IDLE
		return

	var just_jumped = player_client_jump()
	var input_dir = player_client_input(delta)
	
	clientNetworkHistory.snapshot_input(Vector2(), Vector3(), false)
	
	if input_dir != Vector2(0, 0) or just_jumped:
		state = State.STATE_IDLE


var animation_state_counter = 0
var animation_state_time = 0
func handle_animation_state(delta):
	if controls != Constants.Controls.Player or Constants.is_server:
		return

	animation_state_counter += delta
	if animation_state_counter >= animation_state_time:
		#state = State.STATE_IDLE
		#return
		pass
	
	
	var just_jumped = player_client_jump()
	var input_dir = player_client_input(delta)
	
	clientNetworkHistory.snapshot_input(Vector2(), Vector3(), false)
	
	if input_dir != Vector2(0, 0) or just_jumped:
		state = State.STATE_IDLE


func handle_cook_state(delta):
	if controls != Constants.Controls.Player or Constants.is_server:
		return
	
	cook_state_counter += delta
	if cook_state_counter >= cook_state_total_time:
		cook_finished.emit()
		state = State.STATE_IDLE
		return
		#SoundManager.play_attack_sound()
	
	var just_jumped = player_client_jump()
	var input_dir = player_client_input(delta)
	
	clientNetworkHistory.snapshot_input(Vector2(), Vector3(), false)
	
	if input_dir != Vector2(0, 0) or just_jumped:
		state = State.STATE_IDLE


func handle_heal_state(delta):
	if controls != Constants.Controls.Player or Constants.is_server:
		return
	
	heal_state_counter += delta
	if heal_state_counter >= heal_state_total_time:
		heal_finished.emit()
		state = State.STATE_IDLE
		return
	
	var just_jumped = player_client_jump()
	var input_dir = player_client_input(delta)
	
	clientNetworkHistory.snapshot_input(Vector2(), Vector3(), false)
	
	if input_dir != Vector2(0, 0) or just_jumped:
		state = State.STATE_IDLE


func handle_normal_mode(delta, add_input):
	if VehicleManager.ami_in_vehicle():
		return
	
	
	ragdoll_sound_played = false
	
	if Constants.is_server and is_bot():
		find_bot_helper(delta)

	var just_jumped = player_client_jump()
	var input_dir = player_client_input(delta)
	
	if Constants.is_server and server_player_data != null:
		if controls != Constants.Controls.Bot:
			if server_player_data.has("c") and server_player_data["c"].has("i"):
				input_dir = server_player_data["c"]["i"]
		if server_player_data.has("c") and server_player_data["c"].has("j") and server_player_data["c"]["j"]:
			if on_floor and not state == State.STATE_JUMP and not is_in_trampoline:
				velocity.y = JUMP_VELOCITY
				server_player_data["c"]["j"] = false
			elif slide_enabled:
				state = State.STATE_SLIDE
				server_player_data["c"]["j"] = false

	direction = Vector3(0, 0, 0)
	if Constants.is_server and not Constants.LOCAL_MODE and server_player_data != null:
		if server_player_data.has("c") and server_player_data["c"].has("l") and not is_bot():#l->LookAt
			direction = server_player_data["c"]["l"]
			if direction.length() > 0:
				look_at(global_position + direction * 5)
	
	if camera_controller or is_bot():
		var look_at_pos = null
		if (Constants.is_server and not Constants.LOCAL_MODE) and server_player_data:
			if server_player_data.has("c") and server_player_data["c"].has("l") and not is_bot():#l->LookAt
				look_at_pos = server_player_data["c"]["l"]
			else:
				look_at_pos = Vector3(0, 0, 0)
			if controls == Constants.Controls.Bot:
				look_at_pos = Vector3(global_position.x + input_dir.x, global_position.y, global_position.z + + input_dir.y)
		else:
			if controls == Constants.Controls.Bot:
				look_at_pos = Vector3(global_position.x + input_dir.x, global_position.y, global_position.z + + input_dir.y)
			else:
				look_at_pos = camera_controller.get_node("LookAt").global_position
		var pointer = get_tree().get_first_node_in_group("pointer")
		if pointer == null:
			return
		var normal = input_dir.normalized()
		if controls == Constants.Controls.Bot:
			if input_dir == Vector2(0, 0):
				normal = Vector2(0, 0)
			else:
				normal = Vector2(1, 0)
		var straight = Vector3(look_at_pos.x - global_position.x, 0, look_at_pos.z - global_position.z).normalized()
		var length = 5
		var pointer_target = Vector3(-normal.x * length, 0, -normal.y * length)
		var angle : float = Vector3(0, 0, 1).angle_to(straight)
		if angle > 0.001 or angle < -0.001:
			var axis_vect = Vector3(0, 0, 1).cross(straight)
			if axis_vect != Vector3(0, 0, 0):
				pointer_target = pointer_target.rotated(axis_vect.normalized(), angle)
		pointer.global_position = pointer_target + global_position
		
		if (pointer.global_position - global_position).length() > 0.01:
			if last_look_at_position == null:
				last_look_at_position = pointer.global_position
			var lerp_direction = pointer.global_position
			look_at(lerp_direction)
			last_look_at_position = lerp_direction
			last_direction = (lerp_direction - global_position).normalized()
			
		direction = (pointer.global_position - global_position).normalized()
	
	#Set State for sending to server
	if Constants.is_client() and add_input:
		if client and not client.spector_mode and controls == Constants.Controls.Player:
			clientNetworkHistory.snapshot_input(input_dir, direction, just_jumped)
	
	
	if is_on_floor():
		if direction:
			if controls == Constants.Controls.Player or Constants.is_server:
				state = State.STATE_RUN
			if Constants.is_server or should_move_in_client():
				velocity.x = direction.x * SPEED
				velocity.z = direction.z * SPEED
		else:
			if controls == Constants.Controls.Player or Constants.is_server:
				state = State.STATE_IDLE
			if Constants.is_server or should_move_in_client():
				velocity.x = move_toward(velocity.x, 0, SPEED)
				velocity.z = move_toward(velocity.z, 0, SPEED)
	else:
		if state != State.STATE_SLIDE:
			if controls == Constants.Controls.Player or Constants.is_server:
				state = State.STATE_JUMP
			if Constants.is_server or should_move_in_client():
				velocity.x = direction.x * AIR_SPEED
				velocity.z = direction.z * AIR_SPEED
		elif slide_enabled:
			if controls == Constants.Controls.Player or Constants.is_server:
				state = State.STATE_SLIDE
			if Constants.is_server or should_move_in_client():
				velocity.x = direction.x * SLIDE_SPEED
				velocity.z = direction.z * SLIDE_SPEED
	
	return velocity


func handle_rifle_mode(delta, add_input):
	if VehicleManager.ami_in_vehicle():
		return

	ragdoll_sound_played = false

	if Constants.is_server and is_bot():
		find_bot_helper(delta)

	var just_jumped = player_client_jump()
	var input_dir = player_client_input(delta)

	if Constants.is_server and server_player_data != null:
		if controls != Constants.Controls.Bot:
			if server_player_data.has("c") and server_player_data["c"].has("i"):
				input_dir = server_player_data["c"]["i"]
		if server_player_data.has("c") and server_player_data["c"].has("j") and server_player_data["c"]["j"]:
			if on_floor and not state == State.RIFLE_JUMP and not is_in_trampoline:
				velocity.y = JUMP_VELOCITY
				server_player_data["c"]["j"] = false
			elif slide_enabled:
				state = State.STATE_SLIDE  # Exit rifle mode when sliding
				server_player_data["c"]["j"] = false

	direction = Vector3(0, 0, 0)
	if Constants.is_server and not Constants.LOCAL_MODE and server_player_data != null:
		if server_player_data.has("c") and server_player_data["c"].has("l") and not is_bot():#l->LookAt
			direction = server_player_data["c"]["l"]
			if direction.length() > 0:
				look_at(global_position + direction * 5)

	if camera_controller or is_bot():
		var look_at_pos = null
		if (Constants.is_server and not Constants.LOCAL_MODE) and server_player_data:
			if server_player_data.has("c") and server_player_data["c"].has("l") and not is_bot():#l->LookAt
				look_at_pos = server_player_data["c"]["l"]
			else:
				look_at_pos = Vector3(0, 0, 0)
			if controls == Constants.Controls.Bot:
				look_at_pos = Vector3(global_position.x + input_dir.x, global_position.y, global_position.z + + input_dir.y)
		else:
			if controls == Constants.Controls.Bot:
				look_at_pos = Vector3(global_position.x + input_dir.x, global_position.y, global_position.z + + input_dir.y)
			else:
				look_at_pos = camera_controller.get_node("LookAt").global_position
		var pointer = get_tree().get_first_node_in_group("pointer")
		if pointer == null:
			return
		var normal = input_dir.normalized()
		if controls == Constants.Controls.Bot:
			if input_dir == Vector2(0, 0):
				normal = Vector2(0, 0)
			else:
				normal = Vector2(1, 0)
		var straight = Vector3(look_at_pos.x - global_position.x, 0, look_at_pos.z - global_position.z).normalized()
		var length = 5
		var pointer_target = Vector3(-normal.x * length, 0, -normal.y * length)
		var angle : float = Vector3(0, 0, 1).angle_to(straight)
		if angle > 0.001 or angle < -0.001:
			var axis_vect = Vector3(0, 0, 1).cross(straight)
			if axis_vect != Vector3(0, 0, 0):
				pointer_target = pointer_target.rotated(axis_vect.normalized(), angle)
		pointer.global_position = pointer_target + global_position

		if (pointer.global_position - global_position).length() > 0.01:
			if last_look_at_position == null:
				last_look_at_position = pointer.global_position
			var lerp_direction = pointer.global_position
			look_at(lerp_direction)
			last_look_at_position = lerp_direction
			last_direction = (lerp_direction - global_position).normalized()

		direction = (pointer.global_position - global_position).normalized()

	#Set State for sending to server
	if Constants.is_client() and add_input:
		if client and not client.spector_mode and controls == Constants.Controls.Player:
			clientNetworkHistory.snapshot_input(input_dir, direction, just_jumped)

	# Handle rifle movement with slightly reduced speed
	var rifle_speed = SPEED * 0.8  # Rifle movement is 80% of normal speed
	var rifle_air_speed = AIR_SPEED * 0.8

	if is_on_floor() and not state in RIFLE_RELOAD_STATES and not state in RIFLE_SHOOT_STATES:
		if direction:
			if controls == Constants.Controls.Player or Constants.is_server:
				#if state in RIFLE_SHOOT_STATES:
					#rifleShootRunState.start_state()
				#else:
				state = State.RIFLE_RUN
			if Constants.is_server or should_move_in_client():
				velocity.x = direction.x * rifle_speed
				velocity.z = direction.z * rifle_speed
		else:
			if controls == Constants.Controls.Player or Constants.is_server:
				state = State.RIFLE_IDLE
			if Constants.is_server or should_move_in_client():
				velocity.x = move_toward(velocity.x, 0, rifle_speed)
				velocity.z = move_toward(velocity.z, 0, rifle_speed)
	else:
		if controls == Constants.Controls.Player or Constants.is_server:
			if not state in RIFLE_SHOOT_STATES and not state in RIFLE_RELOAD_STATES:
				state = State.RIFLE_JUMP
		if Constants.is_server or should_move_in_client():
			velocity.x = direction.x * rifle_air_speed
			velocity.z = direction.z * rifle_air_speed

	return velocity


func handle_free_ride_mode(delta, _add_input):
	free_ride_counter += delta
	
	if free_ride_counter >= FREE_RIDE_TIME:
		if Constants.is_server:
			state = State.STATE_IDLE
			if is_bot():
				recalculate_bot_step()
		if is_client_and_player():
			state = State.STATE_IDLE
	#Set State for sending to server
	if Constants.is_client():
		if client and not client.spector_mode and controls == Constants.Controls.Player:
			clientNetworkHistory.snapshot_input(Vector2(), Vector3(), false)


func handle_ragdoll_mode(delta, original):
	if is_client_and_player() and ragdoll_sound_played == false:
		SoundManager.play_crash_sound()
		ragdoll_sound_played = true
	if not (Constants.is_server or should_move_in_client()):
		velocity.x = move_toward(velocity.x, 0, 0.6)
		velocity.z = move_toward(velocity.z, 0, 0.6)
		return

	ragdoll_counter += delta
	
	velocity.x = move_toward(velocity.x, 0, 0.2)
	velocity.z = move_toward(velocity.z, 0, 0.2)
	
	if ragdoll_counter >= RAGDOLL_TIME:
		if Constants.is_server:
			ragdoll = false
			ragdoll_disable_counter = 0
			if is_bot():
				recalculate_bot_step()
		if is_client_and_player():
			ragdoll = false
			ragdoll_disable_counter = 0
	
	#Set State for sending to server
	if Constants.is_client() and original:
		if client and not client.spector_mode and controls == Constants.Controls.Player:
			clientNetworkHistory.snapshot_input(Vector2(), Vector3(), false)


func _on_player_area_ball_entered(body):
	if ragdoll_disable_counter < RAGDOLL_DISABLE_TIMER or ragdoll:
		return
	
	if body.get_groups().find("PushPlatform") != -1:
		return

	if body.get_groups().find("RotatePlatform") != -1:
		#It's Platform
		var rotate_item = body.get_parent()
		var body_velocity = rotate_item.get_linear_velocity(body)
		if Constants.is_server or is_client_and_player():
			reset_ragdoll_mode()
			velocity = body_velocity  * rotate_item.mass * 2
			handle_fall_on_collision_animation(body_velocity)
			ragdoll = true
		return
	
	
	#It's Ball
	if body.linear_velocity.length() * body.mass < 5:
		return 
	if ragdoll:
		return

	reset_ragdoll_mode()
	velocity = body.linear_velocity * body.impulse * body.mass
	handle_fall_on_collision_animation(body.linear_velocity)
	ragdoll = true


func start_ragdoll(character_v, collider_v):
	reset_ragdoll_mode()
	velocity = character_v
	handle_fall_on_collision_animation(collider_v)
	ragdoll = true


#Client for my_scene and Server for bots
func on_explosive_ball(linear_velocity, impulse):
	if ragdoll:
		return

	reset_ragdoll_mode()
	velocity = linear_velocity * impulse
	handle_fall_on_collision_animation(linear_velocity)
	ragdoll = true


#Client for my_scene and Server for bots
func on_water_splash(linear_velocity, impulse):
	#if ragdoll:
	#	return

	reset_ragdoll_mode()
	velocity = linear_velocity * impulse
	handle_fall_on_collision_animation(linear_velocity)
	ragdoll = true


#For server
#Don't need to get snapshot!
var is_in_trampoline = false
var trampoline_v = 0

func on_enter_trampoline(v):
	if Constants.LOCAL_MODE or Constants.is_server:
		velocity.y = v
		is_in_trampoline = true
		trampoline_v = v
		if Constants.LOCAL_MODE:
			if controls == Constants.Controls.Player:
				SoundManager.play_boing_sound()
	else:
		velocity.y = v
		if controls == Constants.Controls.Player:
			SoundManager.play_boing_sound()


func on_exit_trampoline(_v):
	#For server
	is_in_trampoline = false


#For server
#Don't need to get snapshot!
var is_in_spring_trampoline = false
var spring_poline_v = 0
var spring_poline_time = 0

func on_enter_spring_trampoline(vel, _time):
	if Constants.LOCAL_MODE or Constants.is_server:
		is_in_spring_trampoline = true
		spring_poline_v = vel
		spring_poline_time = _time
		start_free_ride(vel, _time)
		if Constants.LOCAL_MODE:
			if controls == Constants.Controls.Player:
				SoundManager.play_boing_sound()
	else:
		if controls == Constants.Controls.Player:
			if Constants.im_dead():
				return
			start_free_ride(vel, _time)
			SoundManager.play_boing_sound()


func on_exit_spring_trampoline():
	is_in_spring_trampoline = false


func start_free_ride(vel, _time):
	state = State.STATE_FREE_RIDE
	velocity = vel
	free_ride_counter = 0
	FREE_RIDE_TIME = _time


func reset_ragdoll_mode():
	ragdoll_counter = 0
	ragdoll_disable_counter = 0


#Server
func handle_fall_on_collision_animation(body_velocity):
	var collision_angle = rad_to_deg(body_velocity.angle_to(last_direction))
	if collision_angle >= 0 and collision_angle <= 90:
		if ragdoll == true:
			if animation_player.current_animation == FALL_FRONT_COLLIDE:
				animation_player.play(FALL_BEHIND_COLLIDE, 0, 1.5)
		else:
			animation_player.play(FALL_BEHIND_COLLIDE, 0, 1.5)
	else:
		if ragdoll == true:
			if animation_player.current_animation == FALL_BEHIND_COLLIDE:
				animation_player.play(FALL_FRONT_COLLIDE, 0, 1.8)
		else:
			animation_player.play(FALL_FRONT_COLLIDE, 0, 1.8)


func make_player():
	controls = Constants.Controls.Player
	add_to_group("Player")
	init()


func make_remote():
	controls = Constants.Controls.Remote
	remote_hand_slot.no_ui = true
	add_to_group("Remote")
	player_area.queue_free()
	particles.queue_free()
	attack_place_holder.queue_free()
	statManager.queue_free()
	init()


func make_bot():
	controls = Constants.Controls.Bot
	add_to_group("Remote")
	add_to_group("Bot")
	init()


func init():
	if Constants.is_client():
		global_position = Constants.INIT_POS


func get_current_animation():
	if animation_player:
		return find_animation_id(animation_player.current_animation, true)
	return 0


func find_animation_by_id(id):
	if id < 0 or id >= len(animation_list):
		return animation_list[0]
	return animation_list[id]


var last_animation_played_id = 0
func find_animation_id(animation, update_last=false):
	var i = 0
	for anim in animation_list:
		if anim == animation:
			if update_last:
				last_animation_played_id = i
			return i
		i += 1
	return last_animation_played_id


func set_state_to_animation(animation_id=0):
	if is_client_and_player():
		if state in NO_MOBILE_STATES:
			return

	var to_play = find_animation_by_id(animation_id)
	animation_player.stop(false)
	state = State.STATE_ANIMATION
	if animation_should_loop(to_play):
		animation_player.get_animation(to_play).loop_mode = 1#Looping
	else:
		animation_player.get_animation(to_play).loop_mode = 0
	animation_player.play(to_play)
	animation_state_time = animation_player.get_animation(to_play).length
	animation_state_counter = 0


func animation_should_loop(animation_str):
	for l in not_loop_list:
		if animation_str.find(l) != -1:
			return false
	
	return true


func animation_speed(animation_str):
	if animation_str == animation_prefix + throw_animation:
		return 1.5
	return 1


func set_state_to_attack():
	if state == State.STATE_ATTACK or state == State.STATE_FREE_RIDE:
		return
	state = State.STATE_ATTACK
	var to_play = attack_animations[0]
	var animation_speed_scale = Constants.CHARACTER_ATTACK_ANIMATION_SPEED
	animation_player.get_animation(to_play).loop_mode = 0
	animation_player.stop(false)
	animation_player.play(to_play, -1, animation_speed_scale)
	velocity = Vector3(0, velocity.y, 0)

	has_attacked = false
	attack_state_counter = 0
	attack_state_total_time = animation_player.get_animation(to_play).length / animation_speed_scale
	attack_state_attack_time = (animation_player.get_animation(to_play).length - 1.75) / animation_speed_scale


var cook_state_counter = 0
var cook_state_total_time = 0
func set_state_to_cook():
	if state == State.STATE_COOK:
		return

	state = State.STATE_COOK
	var to_play = cook_animations[0]
	var animation_speed_scale = Constants.CHARACTER_COOK_ANIMATION_SPEED
	animation_player.get_animation(to_play).loop_mode = 0
	animation_player.stop(false)
	animation_player.play(to_play, -1, animation_speed_scale)
	velocity = Vector3(0, 0, 0)


	cook_state_counter = 0
	cook_state_total_time = animation_player.get_animation(to_play).length / animation_speed_scale


var heal_state_counter = 0
var heal_state_total_time = 0
func set_state_to_heal():
	if state == State.STATE_HEAL:
		return

	state = State.STATE_HEAL
	var to_play = heal_animations[randi() % heal_animations.size()]
	var animation_speed_scale = 1.0
	animation_player.get_animation(to_play).loop_mode = 0
	animation_player.stop(false)
	animation_player.play(to_play, -1, animation_speed_scale)
	velocity = Vector3(0, 0, 0)


	heal_state_counter = 0
	heal_state_total_time = animation_player.get_animation(to_play).length / animation_speed_scale

var LOCAL_CHECKPOINT = Vector3(0, 0, 0)
func checkpoint(_position):
	LOCAL_CHECKPOINT = _position
	print("checkpoint: ", LOCAL_CHECKPOINT)
	if Constants.is_client():
		return
	if is_instance_valid(server):
		server.checkpoint(player_id, _position)


#For server
#Don't need to get snapshot!
var is_in_lose_area = false


func lose_area():
	is_in_lose_area = true
	if Constants.LOCAL_MODE == true:
		global_position = LOCAL_CHECKPOINT
		reset_character()
		if is_bot():
			recalculate_bot_step()
		return

	if is_client_and_player():
		global_position = LOCAL_CHECKPOINT
		reset_character()
		return
		
	if is_bot():
		server.lose_area(player_id)
		reset_character()
		recalculate_bot_step()
		return


func lose_area_exit():
	is_in_lose_area = false


func check_y_in_client():
	if is_client_and_player():
		if global_position.y < -50:
			lose_area()


func reset_character(look_at_vect=null):
	if state in RIFLE_STATES:
		state = State.RIFLE_IDLE
	else:
		state = State.STATE_IDLE
	free_ride_counter = FREE_RIDE_TIME
	ragdoll = false
	ragdoll_disable_counter = 0
	velocity = Vector3()
	if look_at_vect == null:
		look_at(Vector3(global_position.x, global_position.y, global_position.z + 1))
	else:
		look_at(look_at_vect)


func set_character_name(_name):
	if _name == null:
		_name = ""
	_name = SwearManager.clean_sentence(_name)[0]
	if len(_name) == 0:
		name_label.text = _name
		return
	name_label.text = " " + _name + " " # Don't remove spaces. names will be translate


func get_character_name():
	return name_label.text


var on_floor = false
var on_floor_counter = 0
func handle_on_floor(delta):
	if is_on_floor():
		on_floor_counter = 0
		if on_floor == false:
			on_floor = true
	
	if not is_on_floor():
		on_floor_counter += delta
		if on_floor_counter > ON_FLOOR_TIME:
			on_floor = false


func set_show(rand_timer=true):
	set_character_name("")
	state = State.SHOW
	gravity = 0
	particles.emitting = false
	statManager.queue_free()
	if is_instance_valid(remote_hand_slot):
		remote_hand_slot.queue_free()
	if rand_timer:
		await get_tree().create_timer(randf() * 3).timeout
	animation_player.play(IDLE_ANIMATION)


func should_move_in_client():
	if Constants.is_server:
		return true
	if client == null:
		return false
	if controls == Constants.Controls.Remote:
		return false #Velocity comes from server and don't calculate it in client
	if controls == Constants.Controls.Player:
		return true
	#return (client.time_from_last_sync >= Constants.CLIENT_CHARACTER_MOVE_TIME)


func remote_print(string):
	if Constants.is_client():
		if controls == Constants.Controls.Remote:
			print(string)


func play_jump_sound():
	if controls == Constants.Controls.Remote:
		return
	#SoundManager.play_jump_sound() # Don't Play Jump Sound


#Client
func player_client_jump():
	if Constants.is_server and not Constants.LOCAL_MODE:
		return false
	if not client:
		return false
	if client.spector_mode:
		return false

	var return_jump = false
	# Handle Jump.
	if controls == Constants.Controls.Player:
		var jump_input = (controller and controller.jump_pressed) or Input.is_action_just_pressed("jump")
		
		if jump_input:
			if on_floor:
				velocity.y = JUMP_VELOCITY
				if state in RIFLE_STATES:
					state = State.RIFLE_JUMP
				else:
					state = State.STATE_JUMP
				jump_pressed = true
			elif ((not on_floor) or state == State.STATE_JUMP) and slide_enabled:
				if state != State.STATE_SLIDE:
					state = State.STATE_SLIDE
					return_jump = true
					jump_pressed = false
			else:
				if Constants.LOCAL_MODE and not is_in_trampoline:
					velocity.y = JUMP_VELOCITY
				return_jump = true
				jump_pressed = true
		
			if controller:
				controller.jump_pressed = false
	
	return return_jump


#Client
func player_client_input(delta):
	var input_dir = Input.get_vector("ui_left", "ui_right", "ui_up", "ui_down")
	
	if controls == Constants.Controls.Player:
		if OS.get_name() == "Android":
			if controller:
				if client and not client.spector_mode:
					input_dir = controller.get_controller_state()
					print(input_dir)
	
	if client and client.spector_mode:
		input_dir = Vector2(0, 0)
	
	if controls == Constants.Controls.Bot:
		input_dir = handle_bot_movement()
		if input_dir == last_bot_input_dir:
			repetive_bot_input_counter += delta
			if repetive_bot_input_counter >= repetive_bot_input_time:
				repetive_bot_input_counter = 0
				lose_area()
		else:
			repetive_bot_input_counter = 0
		last_bot_input_dir = input_dir
	
	return input_dir


#Server
func handle_bot_movement():
	if server.state != Constants.ServerState.InGame and not Constants.LOCAL_MODE:
		return Vector2(0, 0)
	if bot_helper_node == null:
		return Vector2(0, 0)
	
	var distance_vector = bot_helper_node.global_position - global_position
	if bot_helper_node.get_groups().find("jump_helper") != -1:
		#set_character_name("jump: " + str(is_near_helper_node()))
		#it's jump
		if distance_vector.length() <= bot_helper_node.jump_distance or not is_on_floor():
			distance_vector = Vector2(distance_vector.z, -distance_vector.x).normalized() * 1.5
			distance_vector.x = clamp(distance_vector.x, -1, 1)
			distance_vector.y = clamp(distance_vector.y, -1, 1)
			bot_jump()
			if bot_jump_last_input == null:
				bot_jump_last_input = distance_vector
			else:
				bot_jump_last_input = lerp(bot_jump_last_input, distance_vector, 0.1)
			return bot_jump_last_input
		else:
			recalculate_bot_step(bot_helper_node.step)
			return Vector2(0, 0)
	elif bot_helper_node.get_groups().find("move_helper") != -1:
		#set_character_name("move: " + str(is_near_helper_node()))
		bot_jump_last_input = null
		#move
		distance_vector = Vector2(distance_vector.z, -distance_vector.x).normalized() * 1.5
		distance_vector.x = clamp(distance_vector.x, -1, 1)
		distance_vector.y = clamp(distance_vector.y, -1, 1)
		return distance_vector
	
	return Vector2(0, 0)


#Server
func bot_jump():
	server_player_data["c"]["j"] = true


var bot_jump_last_input = null
var last_bot_input_dir = null
var repetive_bot_input_counter = 0
var repetive_bot_input_time = 7
var bot_helper_node = null
var bot_find_helper_time = 3
var bot_find_helper_counter = 0
var bot_helper_step = 1
var bot_near_distance = 1
#var bot_near_distance_jump = 2 #Use helper node finish_distance
func find_bot_helper(delta):
	if server.state != Constants.ServerState.InGame and not Constants.LOCAL_MODE:
		return
	if is_near_helper_node():
		bot_helper_step = bot_helper_node.step + 1
	bot_find_helper_counter += delta
	if bot_helper_node == null or bot_find_helper_counter > bot_find_helper_time or bot_helper_node.step < bot_helper_step or is_near_helper_node():
		bot_find_helper_counter = 0
		if is_near_helper_node() and (bot_helper_node != null and bot_helper_node.recalculate_next):
			recalculate_bot_step(bot_helper_step)
		else:
			if bot_helper_node == null:
				bot_helper_node = find_next_helper_node()
			elif is_near_helper_node() or (bot_helper_node.get_groups().find("move_helper") != -1 and bot_find_helper_counter > bot_find_helper_time):#not is_on_floor():
				bot_helper_node = find_next_helper_node()


func is_near_helper_node():
	if bot_helper_node == null:
		return false
	var dist = bot_near_distance
	if bot_helper_node.get_groups().find("jump_helper") != -1:
		if not is_on_floor():
			return false
		dist = bot_helper_node.finish_distance
	return (global_position - bot_helper_node.global_position).length() <= dist


func is_bot():
	return controls == Constants.Controls.Bot


func find_next_helper_node():
	if bot_helper_node != null:
		if bot_helper_node.next_node != null:
			if is_near_helper_node():
				bot_helper_step = bot_helper_node.next_node.step
				return bot_helper_node.next_node
			return bot_helper_node

	var helpers = get_tree().get_nodes_in_group("bot_helper")
	var candidates = []
	for node in helpers:
		if node.step == bot_helper_step:
			candidates.append(node)
	
	if len(candidates) == 0:
		return null
	return candidates[randi() % len(candidates)]


func recalculate_bot_step(step=-1):
	var helpers = get_tree().get_nodes_in_group("bot_helper")
	var min_dist = 1000 * 1000 * 1000
	for node in helpers:
		var dist = global_position.distance_to(node.global_position)
		if step == -1:
			if dist < min_dist:
				min_dist = dist
				bot_helper_node = node
				bot_helper_step = node.step + 1
		else:
			if dist < min_dist and node.step == step:
				min_dist = dist
				bot_helper_node = node
				bot_helper_step = node.step + 1
	bot_find_helper_counter = 0

func bot_on_start_game():
	bot_find_helper_counter = 0
	bot_helper_step = 1
	recalculate_bot_step()


func get_current_tick(debug_tick):
	if debug_tick == -1:
		return TickManager.tick_counter
	
	return debug_tick


func is_server_or_local():
	return Constants.is_server or Constants.LOCAL_MODE == true


func is_server_non_local():
	return Constants.is_server and Constants.LOCAL_MODE == false


func is_client_and_player():
	return Constants.is_client() and controls == Constants.Controls.Player


func is_client_and_remote():
	return Constants.is_client() and controls == Constants.Controls.Remote


func is_me():
	return controls == Constants.Controls.Player


func get_snapshot():
	return {
		"tick": TickManager.tick_counter,
		"position": global_position,
		"rotation": global_rotation,
		"velocity": velocity,
		"state": state,
		"animation": get_current_animation(),
		"ragdoll": ragdoll,
		"input": player_client_input(0),
		"on_floor_counter": on_floor_counter,
		"on_floor": on_floor,
		"ragdoll_counter": ragdoll_counter,
		"ragdoll_disable_counter": ragdoll_disable_counter,
		"free_ride_counter": free_ride_counter,
	}


func disable():
	collision_layer = 0
	collision_mask = 0
	visible = false
	if is_instance_valid(particles):
		particles.emitting = false
	gravity = 0
	

func enable():
	collision_layer = backup_collision_layer
	collision_mask = backup_collision_mask
	visible = true
	state = State.STATE_IDLE
	if server_player_data:
		server_player_data["c"]["i"] = Vector2(0, 0)
		server_player_data["c"]["j"] = false
	animation_player.play(IDLE_ANIMATION)
	gravity = ProjectSettings.get_setting("physics/3d/default_gravity")


#Action Handling******************************************************8
var is_in_action_state = false
var actionable_scene = null
func on_enter_actionable_scene(scene):
	is_in_action_state = true
	actionable_scene = scene


func on_exit_actionable_scene(_scene):
	#if is_me() and _scene:
	#	print("Exit: ", _scene.name, " ", _scene.get_parent().name)
	is_in_action_state = false
	actionable_scene = null


#Emote Handling
var emote_counter = 0
var emote_timer = -1 #Fill from server
var last_is_chat = false
var remote_in_chat_emote = false
func show_emote(emote_id, time):
	if Selector.hide_my_emotes:
		return
	if emote_id == 53 and time > 10: #Start Loading Emote
		if controls == Constants.Controls.Player:
			if Constants.client.game_scene.client_state == Constants.client.game_scene.CLIENT_STATE.Game:
				return
	
	if emote_id == 43 and time > 10:#In Chat:
		if controls == Constants.Controls.Remote:
			remote_in_chat_emote = true
	if emote_id == 43 and time < 0.5:#In Chat Finished:
		remote_in_chat_emote = false
	if time < 0.5 and chat_message.visible:
		return
	emote_timer = time
	emote_counter = 0
	emote_animated_sprite.play(str(emote_id))
	chat_message.visible = false
	last_is_chat = false


func show_chat_message(message, time):
	chat_label.text = message
	emote_timer = time
	emote_counter = 0
	emote.visible = false
	last_is_chat = true
	chat_viewport.render_target_update_mode = 1


func handle_emote(delta):
	if Constants.is_server:
		return
	if not is_instance_valid(emote):
		return

	if emote_timer < 0 or emote_counter >= emote_timer:
		if chat_message.visible and remote_in_chat_emote:
			#Should hide chat but show emote
			emote_timer = 3600
			last_is_chat = false
			emote.visible = true
			chat_message.visible = false
		else:
			emote.visible = false
			chat_message.visible = false
		return

	emote_counter += delta
	if not last_is_chat:
		emote.visible = true
	else:
		chat_message.visible = true


func reset_emote():
	emote_counter = 0
	emote_timer = 0
	emote.visible = false
	chat_message.visible = false


func set_state_to_autoritive():
	state = State.STATE_AUTHORITIVE


func set_state_to_idle(check_death=false):
	if check_death:
		if DataSaver.get_item("im_dead", false):
			return
	state = State.STATE_IDLE


func handle_authoritive_state(delta):
	if Constants.is_server:
		velocity.y -= gravity * delta
		move_and_slide()
		return
	
	if Constants.is_client():
		if velocity.length() > 0.01:
			move_and_slide()
		return


func disable_gravity():
	gravity = 0


func enable_gravity():
	gravity = ProjectSettings.get_setting("physics/3d/default_gravity")


######################################Weapon Manager###################################
var weapon = null#New Weapon who added in game like battleHero
var weapon_item_id = 0#For battle Heroes only!
func get_right_hand_bone():
	var path = "Armature/Skeleton3D/BoneAttachment3D"
	if has_node(path):
		return get_node(path)
	return null


func get_left_hand_bone():
	var path = "Armature/Skeleton3D/BoneAttachment3D20"
	if has_node(path):
		return get_node(path)
	return null


func visible_native_character_item(vis):
	var right = get_right_hand_bone()
	if right:
		for w in right.get_children():
			w.visible = vis
	var left = get_left_hand_bone()
	if left:
		for w in left.get_children():
			w.visible = vis


func add_right_hand():
	var bone = BoneAttachment3D.new()
	var skeleton = get_node("Armature/Skeleton3D")
	skeleton.add_child(bone)
	bone.name = "BoneAttachment3D"
	bone.bone_name = "mixamorig_RightHand"
	return bone


func add_to_hand(mesh):
	visible_native_character_item(false)
	var right = get_right_hand_bone()
	if right:
		right.add_child(mesh)
	else:
		right = add_right_hand()
		right.add_child(mesh)
	hand_item_mesh = mesh


func add_weapon(_weapon, id, item_id):
	if cosmetic_data.has("weapon") and cosmetic_data["weapon"] != null:
		if id == cosmetic_data.get("weapon", -1):
			#print("ignoring weapon")
			_weapon.queue_free()
			return
	if weapon:
		empty_hand()
	weapon = _weapon
	weapon_item_id = item_id
	visible_native_character_item(false)
	var right = get_right_hand_bone()
	if right:
		right.add_child(weapon)
	else:
		right = add_right_hand()
		right.add_child(weapon)
	return weapon


func empty_hand():
	if hand_item_mesh:
		if is_instance_valid(hand_item_mesh):
			if is_client_and_remote():
				hand_item_mesh.queue_free()
				if is_instance_valid(remote_hand_slot):
					remote_hand_slot.clear()
			else:
				if not hand_item_mesh is Food:
					hand_item_mesh.queue_free()
				else:
					InventoryManager.my_food_in_hand.get_parent().remove_child(InventoryManager.my_food_in_hand)
		
		hand_item_mesh = null
		visible_native_character_item(true)#Character cosmetic weapons


func update_title_label(title, color):
	title_label.visible = true
	title_label.text = title
	title_label.modulate = Color(color)
	cosmetic_data["title"] = {
		"title": title,
		"color": color
	}


var remote_hand_slot = InventorySlot.new()
var hand_item_mesh = null
func update_remote_cosmetic(data):
	#print(data["hand"])
	if data["hand"] == 255:
		empty_hand()
	else:
		if is_remote_hand_data_new(data["hand"]):
			empty_hand()
			remote_hand_slot.item = InventoryManager.get_item_by_id(data["hand"])
			if remote_hand_slot.item == null:
				return
			hand_item_mesh = InventoryManager.get_item_mesh(remote_hand_slot.item)
			if hand_item_mesh:
				var right = get_right_hand_bone()
				if right:
					right.add_child(hand_item_mesh)
				else:
					right = add_right_hand()
					right.add_child(hand_item_mesh)
				if hand_item_mesh.has_method("set_data"):
					hand_item_mesh.set_data(data["reserved"])


	if data["title"] != null:
		update_title_label(data["title"]["title"], data["title"]["color"])
	else:
		title_label.visible = false

	cosmetic_data = data


func is_remote_hand_data_new(hand_data_id):
	if remote_hand_slot.item == null:
		return hand_data_id != 255
	
	return remote_hand_slot.item.id != hand_data_id


func play_random_sit_animation():
	if animation_player.current_animation in sit_animations:
		return
	var to_play = sit_animations[randi() % sit_animations.size()]
	animation_player.get_animation(to_play).loop_mode = 1
	animation_player.stop(false)
	animation_player.play(to_play)
	await Constants.wait_timer(animation_player.get_animation(to_play).length)
	animation_player.play(SIT_ANIMATION)


func consume_food(food_data):
	statManager.consume_food(food_data)


#Free all unused childs
func set_dummy():
	player_area.queue_free()
	name_label.queue_free()
	title_label.queue_free()
	particles.queue_free()
	clientNetworkHistory.queue_free()
	serverNetworkHistory.queue_free()
	emote.queue_free()
	chat_message.queue_free()
	attack_place_holder.queue_free()
	statManager.queue_free()
	remote_hand_slot.queue_free()
	if is_instance_valid(remote_hand_slot):
		remote_hand_slot.queue_free()
	set_physics_process(false)


func _exit_tree() -> void:
	if is_instance_valid(statManager):
		statManager.queue_free()
	
	if is_instance_valid(remote_hand_slot):
		remote_hand_slot.queue_free()
	if is_instance_valid(hand_item_mesh):
		hand_item_mesh.queue_free()


var arm_holdster = false
#Player

func holdster():
	if arm_holdster:#Draw Weapon
		arm_holdster = false
		var item = InventoryManager.get_hand_item()
		if item == null or item.type != "Gun":
			return
		
		if item.gun_type == "range":
			controller.aim_texture.visible = true
		else:
			controller.aim_texture.visible = false
		controller.shoot_button.visible = true
		controller.arrest_button.visible = false
	else:#Holdster
		arm_holdster = true
		controller.aim_texture.visible = false
		controller.shoot_button.visible = false
		controller.arrest_button.visible = true


func reset_gun_ui():
	arm_holdster = true
	controller.aim_texture.visible = false
	controller.shoot_button.visible = false
	controller.arrest_button.visible = true


func start_shoot():
	if DataSaver.get_item("im_dead", false):
		return
	
	if state in NO_SHOOT_STATES:
		return
	
	var item = InventoryManager.get_hand_item()
	if item == null or item.type != "Gun":
		return
	
	if item.ammo <= 0:
		return

	var speed = 2.2
	if item.get("animation_speed") != null:
		speed = item.animation_speed
	if item.gun_type == "melee":
		actionState.start_state(animation_prefix + baton_animation, "arm_shoot", false, speed, 1.4 / speed)
	if item.gun_type == "range":
		actionState.start_state(animation_prefix + pistol_shoot_animation, "arm_shoot", false, speed, 1.25 / speed)


func arm_shoot():
	if DataSaver.get_item("im_dead", false):
		return
	
	var item = InventoryManager.get_hand_item()
	if item == null or item.type != "Gun":
		return
	if item.ammo <= 0:
		return

	if item.gun_type == "range":
		var pos = controller.aim_texture.global_position + Vector2(controller.aim_texture.size) / 2
		var start = camera_controller.camera3d.project_position(pos, 0)
		var end = camera_controller.camera3d.project_position(pos, 50)
		
		SoundManager.play_3d_sound.rpc_id(1, global_position, item.range, SoundManager.SOUND_TYPE.Pistol)
		server.gunshot.rpc_id(1, start, end, item.damage)
		
	elif item.gun_type == "melee":
		SoundManager.play_3d_sound.rpc_id(1, global_position, 10, SoundManager.SOUND_TYPE.Melee)
		server.melee_attack.rpc_id(1, item.range, item.damage)
	
	item.ammo -= 1
	DataSaver.send_save_request()
	controller.ammo_label.text = str(item.ammo)


func start_snowball_shoot():
	if DataSaver.get_item("im_dead", false):
		return
	
	if state in NO_SHOOT_STATES:
		return
	
	
	var item = InventoryManager.get_hand_item()
	if item == null:
		return
	if item.get("count") == null:
		return
	
	if item.count == 0:
		InventoryManager.remove_item_in_hand()
		return
	
	var look_at_pos = camera_controller.get_node("LookAt").global_position
	look_at(look_at_pos)

	var speed = 1.8
	actionState.start_state(animation_prefix + throw_animation,
	 "snowball_shoot", false, speed, 1.4 / speed)


func snowball_shoot():
	if DataSaver.get_item("im_dead", false):
		return
	
	var force = 25.0
	SpawnManager.call_spawn_snowball(throw_place_holder2.global_position,
	 (throw_place_holder.global_position - global_position).normalized(), force)


	var item = InventoryManager.get_hand_item()
	if item.type != "Snowball":
		return
	if item.get("count") == null:
		return
	
	if item.count == 0:
		InventoryManager.remove_item_in_hand()
		return

	item.count -= 1
	controller.snowball_ammo.text = str(item.count)
	if item.count == 0:
		InventoryManager.remove_item_in_hand()
		return
	
	state = State.STATE_IDLE
	DataSaver.send_save_request()


func im_dead():
	deadState.start_state()


#Calls from touch controller button
#Police arrest
var last_arrest_time = 0
func arrest_action():
	if Time.get_ticks_msec() - last_arrest_time <= 500:
		print("time should pass")
		return
	last_arrest_time = Time.get_ticks_msec()
	var policeOffice = Constants.client.game_scene.policeOffice as PoliceOffice
	if policeOffice:
		policeOffice.arrest_around_me.rpc_id(1)
	else:
		print_debug("Police office not found")


func on_arrest_finished():
	jail_me.emit()
	EmoteManager.show_emote(EmoteManager.Jail)


func on_heal_finished():
	print("heal finished im alive now!")
	state = State.STATE_IDLE
	enable_gravity()
	Selector.my_warmth = 50
	DataSaver.set_item("im_freezed", false, false)
	DataSaver.send_save_request()


func im_alive():
	state = State.STATE_IDLE
	enable_gravity()
	DataSaver.set_item("im_dead", false, false)


func check_ami_dead():
	if DataSaver.get_item("im_dead", false) == true:
		im_dead()


func ami_damageable():
	if Selector.im_in_game:
		return false

	if Selector.in_prison:
		return false
	
	if state in [State.STATE_FREEZE, State.STATE_DEAD, State.STATE_FOLLOW_PATH,
		State.STATE_FOLLOW_PATH, State.STATE_FREEZE_DEATH]:
		return false
	
	return true


# Rifle state transition functions
func set_rifle_idle():
	rifleIdleState.start_state()


func set_rifle_run():
	rifleRunState.start_state()


func set_rifle_reload():
	rifleReloadState.start_state()


func set_rifle_reload_run():
	rifleReloadRunState.start_state()


func set_rifle_jump():
	rifleJumpState.start_state()


func set_rifle_shoot():
	rifleShootState.start_state()


func set_rifle_shoot_run():
	rifleShootRunState.start_state()


func is_in_rifle_state():
	return state in [State.RIFLE_IDLE, State.RIFLE_RUN, State.RIFLE_RELOAD, State.RIFLE_RELOAD_RUN, State.RIFLE_JUMP, State.RIFLE_SHOOT, State.RIFLE_SHOOT_RUN]


func check_rifle_shoot(to_go_state):
	var prop_controller:ProphauntTouchController = controller
	if prop_controller.shoot_pressed or Input.is_action_pressed("rifle_shoot"):
		to_go_state.start_state()


func heal_drug():#Doctor with syringe
	var WAIT_TIME = 30000
	if Time.get_ticks_msec() - Selector.last_syringe_use <= WAIT_TIME:
		var diff = Time.get_ticks_msec() - Selector.last_syringe_use
		@warning_ignore("integer_division")
		var wait = int(WAIT_TIME - diff) / 1000
		Constants.show_toast(tr("SYRINGE_WAIT") % [wait])
		return
	var hospital = Constants.client.game_scene.hospital
	if hospital:
		hospital.heal_around_me.rpc_id(1)
	else:
		print_debug("Hospital not found")


func item_action_start():
	var item = InventoryManager.get_hand_item()
	if item == null:
		return
	if item.get("count") == null:
		return
	
	if item.count == 0:
		InventoryManager.remove_item_in_hand()
		return
	
	if item.has_method("start_action"):
		item.start_action(self)


func item_action():
	var item = InventoryManager.get_hand_item()
	if item == null:
		return
	
	if item.has_method("action"):
		item.action(self)


func update_remote_character_from_vehicle_sit(sit:VehicleSit):
	global_position = sit.global_position
	global_rotation = sit.global_rotation
	look_at(sit.look_position())
	global_rotation.z = -sit.global_rotation.z
	velocity = Vector3()


var prop_collisions = []
func add_prop_collisions(object:PropObject):
	remove_prop_collisions()
	for col in object.body.get_children():
		var c = col.duplicate(8)
		add_child(c)
		prop_collisions.append(c)


func remove_prop_collisions():
	for col in prop_collisions:
		col.queue_free()
	prop_collisions = []


func hide_armature_mesh():
	var mesh = get_node_or_null("Armature")
	if mesh:
		mesh.visible = false


func show_armature_mesh():
	var mesh = get_node_or_null("Armature")
	if mesh:
		mesh.visible = true


@rpc("authority", "call_remote", "reliable", Constants.CHANNEL_CHANGE_CHARACTER)
func force_set_position(pos):
	global_position = pos
	velocity = Vector3.ZERO
	checkpoint(global_position)


@rpc("authority", "call_remote", "reliable", Constants.CHANNEL_CHANGE_CHARACTER)
func force_set_rotation(rot):
	global_rotation = rot
	velocity = Vector3.ZERO
	checkpoint(global_position)


@rpc("authority", "call_remote", "reliable", Constants.CHANNEL_CHANGE_CHARACTER)
func force_set_pos_rot(pos, rot):
	global_position = pos
	global_rotation = rot
	velocity = Vector3.ZERO
	checkpoint(global_position)


func remove_native_character_item():
	var right = get_right_hand_bone()
	if right:
		for w in right.get_children():
			w.queue_free()
	var left = get_left_hand_bone()
	if left:
		for w in left.get_children():
			w.queue_free()


func add_selected_rifle():
	var rifle_item:ProphauntGunItem = Selector.my_prophaunt_item_data["selected_weapon_item"]
	rifle_item.on_player_equip(self)


func on_rifle_shoot_finished():
	#print("shoot finished")
	var pos = controller.haunter_aim.global_position + Vector2(controller.haunter_aim.size) / 2
	var start = camera_controller.camera3d.project_position(pos, 0)
	var end = camera_controller.camera3d.project_position(pos, 50)
	
	rifle_shoot.emit(start, end)
	
	rifleIdleState.start_state()


#Prophaunt
func start_grenade_shoot():
	if state in NO_SHOOT_STATES:
		return
	
	if not prophaunt_player.health_system.is_alive():
		return
	
	if Selector.my_prophaunt_item_data["grenades"] <= 0:
		return
	
	var look_at_pos = camera_controller.get_node("LookAt").global_position
	look_at(look_at_pos)

	var speed = 1.8
	actionState.start_state(animation_prefix + throw_animation,
	 "on_grenade_shoot", false, speed, 1.4 / speed)


func on_grenade_shoot():
	if Selector.my_prophaunt_item_data["grenades"] <= 0:
		return
	
	grenade_shoot.emit(throw_place_holder2.global_position, 
	(throw_place_holder.global_position - global_position).normalized())
	
	rifleIdleState.start_state()
	#var force = 25.0
	#SpawnManager.call_spawn_snowball(throw_place_holder2.global_position,
	 #(throw_place_holder.global_position - global_position).normalized(), force)
