[gd_scene load_steps=4 format=3 uid="uid://ct8hd3agokdkv"]

[ext_resource type="PackedScene" uid="uid://cy82bsxfj1ddv" path="res://prophaunt/scenes/PropObject.tscn" id="1_5wxim"]
[ext_resource type="PackedScene" uid="uid://cyx5sjyey13n7" path="res://prophaunt/maps/Source/ArenaProp/weapon_spear.tscn" id="2_yb6fm"]

[sub_resource type="CapsuleShape3D" id="CapsuleShape3D_5q4rx"]
radius = 0.2
height = 2.2

[node name="WeaponSwordProp" instance=ExtResource("1_5wxim")]

[node name="WeaponSpear" parent="Meshes" index="0" instance=ExtResource("2_yb6fm")]

[node name="CollisionShape3D" type="CollisionShape3D" parent="StaticBody3D" index="0"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 1, -0.010301)
shape = SubResource("CapsuleShape3D_5q4rx")
