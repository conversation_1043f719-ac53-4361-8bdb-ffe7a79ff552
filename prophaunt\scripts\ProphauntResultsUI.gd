extends Control
class_name ProphauntResultsUI

# Prophaunt Results UI Controller
# Displays round results, statistics, and leaderboard

# UI References
@onready var title_label = $CenterContainer/MainPanel/MainContainer/Header/TitleLabel
@onready var winner_label = $CenterContainer/MainPanel/MainContainer/Header/WinnerLabel
@onready var round_info_label = $CenterContainer/MainPanel/MainContainer/Header/RoundInfoLabel

@onready var props_survived = $CenterContainer/MainPanel/MainContainer/StatsContainer/TeamStats/TeamStatsPanel/TeamStatsContainer/PropsSurvived
@onready var props_survival_rate = $CenterContainer/MainPanel/MainContainer/StatsContainer/TeamStats/TeamStatsPanel/TeamStatsContainer/PropsSurvivalRate
@onready var haunters_eliminations = $CenterContainer/MainPanel/MainContainer/StatsContainer/TeamStats/TeamStatsPanel/TeamStatsContainer/HauntersEliminations
@onready var haunters_accuracy = $CenterContainer/MainPanel/MainContainer/StatsContainer/TeamStats/TeamStatsPanel/TeamStatsContainer/HauntersAccuracy

@onready var leaderboard_list = $CenterContainer/MainPanel/MainContainer/StatsContainer/Leaderboard/LeaderboardPanel/LeaderboardScroll/LeaderboardList

@onready var shots_label = $CenterContainer/MainPanel/MainContainer/RoundDetails/RoundDetailsContainer/DetailsLeft/ShotsLabel
@onready var grenades_label = $CenterContainer/MainPanel/MainContainer/RoundDetails/RoundDetailsContainer/DetailsLeft/GrenadesLabel
@onready var hexes_label = $CenterContainer/MainPanel/MainContainer/RoundDetails/RoundDetailsContainer/DetailsRight/HexesLabel
@onready var disguises_label = $CenterContainer/MainPanel/MainContainer/RoundDetails/RoundDetailsContainer/DetailsRight/DisguisesLabel

@onready var next_round_button = $CenterContainer/MainPanel/MainContainer/ButtonContainer/NextRoundButton
@onready var leave_game_button = $CenterContainer/MainPanel/MainContainer/ButtonContainer/LeaveGameButton
@onready var countdown_label = $CenterContainer/MainPanel/MainContainer/CountdownLabel

# Data
var round_results: Dictionary = {}
var countdown_time: float = 15.0
var is_final_results: bool = false

signal next_round_requested()
signal leave_game_requested()

func _ready():
	# Initialize UI
	setup_ui()

func setup_ui():
	"""Initialize UI elements"""
	title_label.text = "ROUND RESULTS"
	winner_label.text = "WAITING FOR RESULTS..."
	round_info_label.text = ""
	countdown_label.text = ""
	
	# Clear leaderboard
	clear_leaderboard()

func display_round_results(results: Dictionary):
	"""Display round results"""
	round_results = results
	
	# Update header
	var round_number = results.get("round_number", 1)
	var winner_team = results.get("winner", -1)
	var duration = results.get("actual_duration", 0.0)
	
	title_label.text = "ROUND " + str(round_number) + " RESULTS"
	
	# Display winner
	if winner_team == Constants.ProphauntTeam.PROPS:
		winner_label.text = "PROPS WIN!"
		winner_label.modulate = Color.BLUE
	elif winner_team == Constants.ProphauntTeam.HAUNTERS:
		winner_label.text = "HAUNTERS WIN!"
		winner_label.modulate = Color.RED
	else:
		winner_label.text = "DRAW"
		winner_label.modulate = Color.YELLOW
	
	# Display round info
	var duration_text = format_time(duration)
	var win_reason = results.get("win_reason", "")
	round_info_label.text = "Duration: " + duration_text + " - " + format_win_reason(win_reason)
	
	# Update team statistics
	update_team_statistics(results)
	
	# Update round details
	update_round_details(results)
	
	# Update leaderboard
	update_leaderboard(results)

func display_final_results(results: Dictionary):
	"""Display final game results"""
	is_final_results = true
	round_results = results
	
	title_label.text = "FINAL RESULTS"
	winner_label.text = "GAME COMPLETED"
	winner_label.modulate = Color.WHITE
	
	var total_rounds = results.get("total_rounds", 0)
	round_info_label.text = "Total Rounds: " + str(total_rounds)
	
	# Update final leaderboard
	update_final_leaderboard(results)
	
	# Change button text
	next_round_button.text = "PLAY AGAIN"
	countdown_label.text = "Game ended"

func update_team_statistics(results: Dictionary):
	"""Update team statistics display"""
	var props_count = results.get("props_count", 0)
	var props_survived_count = results.get("props_survived", 0)
	var survival_rate = results.get("survival_rate", 0.0)
	var eliminations = results.get("eliminations", 0)
	
	props_survived.text = "Survived: " + str(props_survived_count) + "/" + str(props_count)
	props_survival_rate.text = "Survival Rate: " + str(int(survival_rate * 100)) + "%"
	
	haunters_eliminations.text = "Eliminations: " + str(eliminations)
	
	# Calculate accuracy (placeholder)
	var shots_fired = results.get("shots_fired", 1)
	var accuracy = float(eliminations) / float(shots_fired) * 100.0 if shots_fired > 0 else 0.0
	haunters_accuracy.text = "Accuracy: " + str(int(accuracy)) + "%"

func update_round_details(results: Dictionary):
	"""Update round details display"""
	shots_label.text = "Shots Fired: " + str(results.get("shots_fired", 0))
	grenades_label.text = "Grenades Thrown: " + str(results.get("grenades_thrown", 0))
	hexes_label.text = "Hexes Cast: " + str(results.get("hex_casts", 0))
	disguises_label.text = "Disguise Changes: " + str(results.get("disguise_changes", 0))

func update_leaderboard(results: Dictionary):
	"""Update player leaderboard"""
	clear_leaderboard()
	
	var player_stats = results.get("player_stats", {})
	var leaderboard_data = []
	
	# Convert to array and sort by score
	for player_id in player_stats.keys():
		var stats = player_stats[player_id]
		stats["player_id"] = player_id
		leaderboard_data.append(stats)
	
	leaderboard_data.sort_custom(func(a, b): return a.get("score", 0) > b.get("score", 0))
	
	# Display leaderboard entries
	for i in range(leaderboard_data.size()):
		var entry = leaderboard_data[i]
		create_leaderboard_entry(i + 1, entry)

func update_final_leaderboard(results: Dictionary):
	"""Update final game leaderboard"""
	clear_leaderboard()
	
	var leaderboard = results.get("leaderboard", [])
	
	for i in range(leaderboard.size()):
		var entry = leaderboard[i]
		create_final_leaderboard_entry(i + 1, entry)

func create_leaderboard_entry(rank: int, player_data: Dictionary):
	"""Create a leaderboard entry for round results"""
	var entry_container = HBoxContainer.new()
	entry_container.custom_minimum_size.y = 30
	
	# Rank
	var rank_label = Label.new()
	rank_label.text = str(rank) + "."
	rank_label.custom_minimum_size.x = 30
	entry_container.add_child(rank_label)
	
	# Player name
	var name_label = Label.new()
	name_label.text = str(player_data.get("player_id", "Unknown"))
	name_label.size_flags_horizontal = Control.SIZE_EXPAND_FILL
	entry_container.add_child(name_label)
	
	# Team
	var team_label = Label.new()
	var team = player_data.get("team", -1)
	if team == Constants.ProphauntTeam.PROPS:
		team_label.text = "PROP"
		team_label.modulate = Color.BLUE
	else:
		team_label.text = "HAUNTER"
		team_label.modulate = Color.RED
	team_label.custom_minimum_size.x = 80
	entry_container.add_child(team_label)
	
	# Status
	var status_label = Label.new()
	var survived = player_data.get("survived", false)
	status_label.text = "ALIVE" if survived else "DEAD"
	status_label.modulate = Color.GREEN if survived else Color.GRAY
	status_label.custom_minimum_size.x = 60
	entry_container.add_child(status_label)
	
	# Score
	var score_label = Label.new()
	score_label.text = str(player_data.get("score", 0))
	score_label.custom_minimum_size.x = 50
	score_label.horizontal_alignment = HORIZONTAL_ALIGNMENT_RIGHT
	entry_container.add_child(score_label)
	
	leaderboard_list.add_child(entry_container)

func create_final_leaderboard_entry(rank: int, player_data: Dictionary):
	"""Create a leaderboard entry for final results"""
	var entry_container = HBoxContainer.new()
	entry_container.custom_minimum_size.y = 35
	
	# Rank
	var rank_label = Label.new()
	rank_label.text = str(rank) + "."
	rank_label.custom_minimum_size.x = 30
	
	# Add medal for top 3
	if rank <= 3:
		var colors = [Color.GOLD, Color.LIGHT_GRAY, Color(0.8, 0.5, 0.2)]  # Gold, Silver, Bronze
		rank_label.modulate = colors[rank - 1]
	
	entry_container.add_child(rank_label)
	
	# Player name
	var name_label = Label.new()
	name_label.text = player_data.get("name", "Unknown")
	name_label.size_flags_horizontal = Control.SIZE_EXPAND_FILL
	entry_container.add_child(name_label)
	
	# Total score
	var score_label = Label.new()
	score_label.text = str(player_data.get("total_score", 0))
	score_label.custom_minimum_size.x = 80
	score_label.horizontal_alignment = HORIZONTAL_ALIGNMENT_RIGHT
	entry_container.add_child(score_label)
	
	# Rounds survived
	var survived_label = Label.new()
	survived_label.text = str(player_data.get("rounds_survived", 0)) + " survived"
	survived_label.custom_minimum_size.x = 100
	survived_label.horizontal_alignment = HORIZONTAL_ALIGNMENT_RIGHT
	entry_container.add_child(survived_label)
	
	leaderboard_list.add_child(entry_container)

func clear_leaderboard():
	"""Clear the leaderboard list"""
	for child in leaderboard_list.get_children():
		child.queue_free()

func start_countdown(duration: float = 15.0):
	"""Start countdown to next round"""
	countdown_time = duration
	countdown_label.visible = true

func _process(delta):
	"""Update countdown"""
	if countdown_time > 0:
		countdown_time -= delta
		
		if is_final_results:
			countdown_label.text = "Game ended"
		else:
			countdown_label.text = "Next round in: " + str(int(countdown_time + 1)) + " seconds"
		
		if countdown_time <= 0:
			countdown_label.text = "Starting next round..."

# Button handlers
func _on_next_round_pressed():
	"""Handle next round button press"""
	SoundManager.play_click_sound()
	next_round_requested.emit()

func _on_leave_game_pressed():
	"""Handle leave game button press"""
	SoundManager.play_click_sound()
	leave_game_requested.emit()

# Utility functions
func format_time(seconds: float) -> String:
	"""Format time as MM:SS"""
	@warning_ignore("integer_division")
	var minutes = int(seconds) / 60
	var secs = int(seconds) % 60
	return "%02d:%02d" % [minutes, secs]

func format_win_reason(reason: String) -> String:
	"""Format win reason for display"""
	match reason:
		"time_expired":
			return "Time Expired"
		"all_props_eliminated":
			return "All Props Eliminated"
		_:
			return reason.capitalize()

# Animation functions
func animate_in():
	"""Animate the results UI in"""
	modulate.a = 0.0
	var tween = create_tween()
	tween.tween_property(self, "modulate:a", 1.0, 0.5)

func animate_out():
	"""Animate the results UI out"""
	var tween = create_tween()
	tween.tween_property(self, "modulate:a", 0.0, 0.3)
	await tween.finished
	visible = false
