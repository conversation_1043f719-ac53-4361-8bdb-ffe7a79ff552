[gd_scene load_steps=4 format=3 uid="uid://copnl1facw0an"]

[ext_resource type="PackedScene" uid="uid://cy82bsxfj1ddv" path="res://prophaunt/scenes/PropObject.tscn" id="1_24enn"]
[ext_resource type="PackedScene" uid="uid://dtf0h3xhxsox1" path="res://prophaunt/maps/Source/Furniture/lounge_sofa.tscn" id="2_ce5gh"]

[sub_resource type="BoxShape3D" id="BoxShape3D_f8ox4"]
size = Vector3(3.93564, 1.82065, 1.61197)

[node name="LoungeSofaProp" instance=ExtResource("1_24enn")]

[node name="LoungeSofa" parent="Meshes" index="0" instance=ExtResource("2_ce5gh")]

[node name="CollisionShape3D" type="CollisionShape3D" parent="StaticBody3D" index="0"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -6.71744e-05, 0.910324, 0.0243893)
shape = SubResource("BoxShape3D_f8ox4")
