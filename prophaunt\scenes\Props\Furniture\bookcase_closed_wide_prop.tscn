[gd_scene load_steps=4 format=3 uid="uid://c1vesst43k6ng"]

[ext_resource type="PackedScene" uid="uid://cy82bsxfj1ddv" path="res://prophaunt/scenes/PropObject.tscn" id="1_bapl4"]
[ext_resource type="PackedScene" uid="uid://vaa4c6nt4kdw" path="res://prophaunt/maps/Source/Furniture/bookcase_closed_wide.tscn" id="2_ppaim"]

[sub_resource type="BoxShape3D" id="BoxShape3D_n2ccm"]
size = Vector3(2.57625, 2.41287, 0.759491)

[node name="BookcaseClosedWideProp" instance=ExtResource("1_bapl4")]

[node name="BookcaseClosedWide" parent="Meshes" index="0" instance=ExtResource("2_ppaim")]

[node name="CollisionShape3D" type="CollisionShape3D" parent="StaticBody3D" index="0"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -0.0073109, 1.2093, 0.00761414)
shape = SubResource("BoxShape3D_n2ccm")
