extends Node


var weapons = [
	load("res://Inventory/Items/Weapons/Prophaunt/ak.tres"),
	load("res://Inventory/Items/Weapons/Prophaunt/blaster_d.tres"),
	load("res://Inventory/Items/Weapons/Prophaunt/blaster_e.tres"),
	load("res://Inventory/Items/Weapons/Prophaunt/blaster_f.tres"),
	load("res://Inventory/Items/Weapons/Prophaunt/blaster_j.tres"),
	load("res://Inventory/Items/Weapons/Prophaunt/blaster_o.tres"),
	load("res://Inventory/Items/Weapons/Prophaunt/blaster_p.tres"),
	load("res://Inventory/Items/Weapons/Prophaunt/blaster_q.tres"),
	load("res://Inventory/Items/Weapons/Prophaunt/M4.tres"),
	load("res://Inventory/Items/Weapons/Prophaunt/smg.tres"),
	load("res://Inventory/Items/Weapons/Prophaunt/shotgun.tres"),
]

func _ready() -> void:
	pass # Replace with function body.


func get_weapon_by_id(id:int):
	for w in weapons:
		if w.id == id:
			return w
	
	return null
