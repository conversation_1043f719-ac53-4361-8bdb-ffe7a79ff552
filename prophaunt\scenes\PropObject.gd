extends Node3D
class_name PropObject

@export var enabled = true
@export var export_mesh: Node3D
@onready var body: StaticBody3D = $StaticBody3D

var mesh = null
var index = 0#Set in disguiseSystem

func _ready() -> void:
	if export_mesh == null:
		if get_child_count() == 0:
			printerr("No mesh found in children in PropObject")
		else:
			mesh = get_child(0)
	else:
		mesh = export_mesh


func get_mesh():
	return mesh


func disable():
	body.disable_mode = CollisionObject3D.DISABLE_MODE_REMOVE
	visible = false


func enable():
	body.disable_mode = CollisionObject3D.DISABLE_MODE_KEEP_ACTIVE
	visible = true
