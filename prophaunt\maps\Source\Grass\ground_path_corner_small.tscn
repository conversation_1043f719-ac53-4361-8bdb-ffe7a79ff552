[gd_scene load_steps=5 format=4 uid="uid://7am6ib58fe4k"]

[ext_resource type="Material" uid="uid://dkmcjc7wc3ws6" path="res://prophaunt/Mat/GrassPath.tres" id="1_qv2tr"]

[sub_resource type="ArrayMesh" id="ArrayMesh_l57ib"]
_surfaces = [{
"aabb": AABB(-1, -0.1, -1, 2, 0.1, 2),
"format": 34896613377,
"index_count": 72,
"index_data": PackedByteArray("AgAAAAEAAQADAAIAAQAEAAMABwAFAAYABgAIAAcACwAJAAoACgAMAAsACAAGAAoACgAJAAgADwANAA4ADgAQAA8AEQAOAA0AEQANAAwAEQAMAAoAEQAKAAYAEQAGAAUAEgARAAUABQATABIAAAAHAAgACAABAAAACAAEAAEACAADAAQACAALAAMACAAJAAsA"),
"lods": [0.113959, PackedByteArray("AAADAAIABgADAAAABgALAAMABgAMAAsAAAAHAAYABwAFAAYADwANAA4AEQAOAA0AEQANAAwAEQAMAAYAEQAGAAUAEgARAAUADgAQAA8ABQATABIA")],
"name": "Material",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 20,
"vertex_data": PackedByteArray("///+//keAACC8P7/+R4AAP///v8AAAAABeH+/wAAAAAF4f7/fA8AAP//AABySAAA/98AAHJIAAD/////kzgAAP/f//+TOAAAa8f+//8fAACMtwAA/x8AAGvH/v8AAAAAjLcAAAAAAABySAAAAAAAAAAAAACMtwAAAAAAAAAAAAAAAAAAckgAAAAAAAD//wAA//8AAP//AAD//wAAjLcAAA==")
}]
blend_shape_mode = 0

[sub_resource type="ArrayMesh" id="ArrayMesh_mo5o3"]
resource_name = "GroundPathCornerSmall_GroundPathCornerSmall"
_surfaces = [{
"aabb": AABB(-1, -0.1, -1, 2, 0.1, 2),
"attribute_data": PackedByteArray("c7NQghSzvZpQglCCs4KVswybQrMa/LGFTFqg/UxaoP2d+LK3TFos7p34src93HiDPdx4g0/Z/bNP2f2zT9n9sym2qtkptqrZKbaq2dVto9o1vK75Nbyu+fKDodzyg6HcSn2j2geHhP5KffqkoyT8gaMk/IFKffyBSn38gUxanoGmAfyBpgGg/aYBoP2jJP/9nfiyt/KDodw="),
"format": 34896613399,
"index_count": 72,
"index_data": PackedByteArray("AgAAAAEAAQADAAIAAQAEAAMACwAFAAgACAANAAsAFwASABUAFQAZABcADwAKABQAFAARAA8AHQAaABsAHAAfAB4AIAAbABoAIAAaABgAIAAYABMAIAATAAkAIAAJAAYAIQAgAAYABwAjACIAAAAMAA4ADgABAAAADgAEAAEADgADAAQADgAWAAMADgAQABYA"),
"lods": [0.113959, PackedByteArray("AAADAAIAJAADAAAAJAAlAAMAJAAZACUAAAALACQACwAFACQAHQAaABsAIAAbABoAIAAaABgAIAAYAAkAIAAJAAYAIQAgAAYAHAAfAB4ABwAjACIA")],
"material": ExtResource("1_qv2tr"),
"name": "Material",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 38,
"vertex_data": PackedByteArray("///+//keOtWC8P7/+R6b1f///v8AAGbVBeH+/wAAktUF4f7/fA+l1f//AABySEnF//8AAHJIVNX//wAAckhU1f/fAABySDvE/98AAHJIVNX/3wAAckiNvf////+TOLDE/////5M4otT/3///kziiw//f//+TOJHV/9///5M4/rxrx/7//x/a1mvH/v//Hzi6a8f+//8fmciMtwAA/x9U1Yy3AAD/H2W7jLcAAP8fy8hrx/7/AAD21WvH/v8AAPLIjLcAAAAAVNWMtwAAAAAlyXJIAAAAAFTVAAAAAIy3VNUAAAAAjLdU1QAAAAAAAFTVAAAAAAAAVNUAAAAAckj/vwAAAAD//1TV//8AAP//VNX//wAA//9U1f//AACMtwAA/98AAHJIhMlrx/7/AAC9zsmqmqpUqtSqlKq0ql+qz6pIqtqq9aC9nlTVqir/f///v6F8nlTVqir2kBy2Z6GZnoOrPaoyoleeYarOqo2RJbbbqJGroJRRtiWd38FU1aoqSpM+truc9cHnqQurapwGwlTVqioBnBzCVNWqKlTVqir/fwAAVNWqKv9/AAD/fwAAVNWqKlTVqir/f////z//f3OjKKEtpnay")
}]
blend_shape_mode = 0
shadow_mesh = SubResource("ArrayMesh_l57ib")

[sub_resource type="ConcavePolygonShape3D" id="ConcavePolygonShape3D_cidp5"]
data = PackedVector3Array(1, 0, -1, 1, 0, -0.758, 0.879, 0, -0.758, 0.879, 0, -0.758, 0.758, 0, -1, 1, 0, -1, 0.879, 0, -0.758, 0.758, 0, -0.879, 0.758, 0, -1, 1, 0, -0.558, 1, -0.1, -0.434, 0.75, -0.1, -0.434, 0.75, -0.1, -0.434, 0.75, 0, -0.558, 1, 0, -0.558, 0.558, 0, -1, 0.558, 0, -0.75, 0.434, -0.1, -0.75, 0.434, -0.1, -0.75, 0.434, -0.1, -1, 0.558, 0, -1, 0.75, 0, -0.558, 0.75, -0.1, -0.434, 0.434, -0.1, -0.75, 0.434, -0.1, -0.75, 0.558, 0, -0.75, 0.75, 0, -0.558, -1, -0.1, -1, -0.434, -0.1, -1, -1, -0.1, 0.434, -1, -0.1, 0.434, -1, -0.1, -0.434, -1, -0.1, -1, -1, -0.1, 1, -1, -0.1, 0.434, -0.434, -0.1, -1, -1, -0.1, 1, -0.434, -0.1, -1, 0.434, -0.1, -1, -1, -0.1, 1, 0.434, -0.1, -1, 0.434, -0.1, -0.75, -1, -0.1, 1, 0.434, -0.1, -0.75, 0.75, -0.1, -0.434, -1, -0.1, 1, 0.75, -0.1, -0.434, 1, -0.1, -0.434, 1, -0.1, 1, -1, -0.1, 1, 1, -0.1, -0.434, 1, -0.1, -0.434, 1, -0.1, 0.434, 1, -0.1, 1, 1, 0, -0.758, 1, 0, -0.558, 0.75, 0, -0.558, 0.75, 0, -0.558, 0.879, 0, -0.758, 1, 0, -0.758, 0.75, 0, -0.558, 0.758, 0, -0.879, 0.879, 0, -0.758, 0.75, 0, -0.558, 0.758, 0, -1, 0.758, 0, -0.879, 0.75, 0, -0.558, 0.558, 0, -1, 0.758, 0, -1, 0.75, 0, -0.558, 0.558, 0, -0.75, 0.558, 0, -1)

[node name="GroundPathCornerSmall" type="MeshInstance3D"]
mesh = SubResource("ArrayMesh_mo5o3")
skeleton = NodePath("")

[node name="StaticBody3D" type="StaticBody3D" parent="."]

[node name="CollisionShape3D" type="CollisionShape3D" parent="StaticBody3D"]
shape = SubResource("ConcavePolygonShape3D_cidp5")
