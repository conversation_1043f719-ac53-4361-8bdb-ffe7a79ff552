[gd_scene load_steps=12 format=4 uid="uid://6awrh6yk77f1"]

[ext_resource type="PackedScene" uid="uid://cy82bsxfj1ddv" path="res://prophaunt/scenes/PropObject.tscn" id="1_d13mx"]
[ext_resource type="Texture2D" uid="uid://cq8xh43fvjb7s" path="res://Scenes/FreeRide/Assets/Buildings/Restaurant/restaurant.png" id="2_dyawf"]

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_s3oqj"]
cull_mode = 1
albedo_color = Color(0, 0, 0, 1)
grow = true
grow_amount = 0.02

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_7n6n0"]
resource_name = "restaurant"
next_pass = SubResource("StandardMaterial3D_s3oqj")
cull_mode = 2
diffuse_mode = 3
vertex_color_use_as_albedo = true
albedo_texture = ExtResource("2_dyawf")
roughness = 0.14
backlight_enabled = true
backlight = Color(0.435294, 0.0980392, 0.0509804, 1)

[sub_resource type="ArrayMesh" id="ArrayMesh_a533n"]
_surfaces = [{
"aabb": AABB(-1, -1, -0.799998, 2, 2.00002, 0.8),
"format": 34359742465,
"index_count": 396,
"index_data": PackedByteArray("BQAHABAAAQAEAAMAAgABAAMAAgAAAAEACAACAAMACAAJAAIABgABAAAABgAFAAEABgACAAkABgAAAAIABwAFAAgABgAIAAUABgAJAAgABAABAAwADAABAAsABQANAAEABQARAA0ADQALAAEADQAKAAsAOwBHAD8AOwBEAEcACwAPAA4ACwAKAA8ABQAQAA4ADwAFAA4ADwARAAUAFwAiACAAEwAWABUAGgAUABUAGgAbABQAFAATABUAFAASABMAGAATABIAGAAXABMAGAAUABsAGAASABQAGQAVABYAGQAaABUAGAAaABcAGAAbABoAFgATAB4AIgAWAB4AIgAZABYAHgATAB0AFwAfABMAFwAjAB8AHwAdABMAHwAcAB0AIQAdABwAIQAgAB0AIgAdACAAIgAeAB0AIQAfACMAIQAcAB8AGQAiABcAIQAXACAAIQAjABcAGQAXABoAKQA0ADIAJQAoACcALAAmACcALAAtACYAJgAlACcAJgAkACUAKgAlACQAKgApACUAKgAmAC0AKgAkACYAKwAnACgAKwAsACcAKgAsACkAKgAtACwAKAAlADAANAAoADAANAArACgAMAAlAC8AKQAxACUAKQA1ADEAMQAvACUAMQAuAC8AMwAvAC4AMwAyAC8ANAAvADIANAAwAC8AMwAxADUAMwAuADEAKwA0ACkAMwApADIAMwA1ACkAKwApACwAFgAoABkAFgArACgANwA5ADgANwA2ADkACgARAA8ACgANABEAEAALAA4AEAAMAAsAAwAHAAgAAwAEAAcARQBJAEIARQBLAEkAPgBAAEoAPgBBAEAARgA+AEoARgA6AD4AQQA6ADwAQQA+ADoAPABGAD0APAA6AEYAPwBKAEAARABGAEMARQBDAEYARQBCAEMAQAA7AD8AQAA9ADsARgA7AD0ASQBKAEgASQBLAEoARgBEADsASgBHAEgARwBKAD8AQwBJAEgAQwBCAEkARQBKAEsARQBGAEoAQAA8AD0AQABBADwASABEAEMASABHAEQAEAAEAAwAEAAHAAQA"),
"name": "restaurant",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 76,
"vertex_data": PackedByteArray("ZAR4PxEAgD8uqr2+AACAP/vOdz+Y+8y+ZAR4PxEAgD8Ej++8AACAP/vOdz+9NwY2AACAPwAAQD+9NwY2AACAv/vOdz9eFM2+ZAR4vxEAgD8uqr2+AACAvwAAQD+9NwY2AACAv/vOdz+9NwY2ZAR4vxEAgD8Ej++8ZAR4PxEAgD+/10S/AACAP/vOdz+rzEy/AACAPwAAQD+rzEy/ZAR4PxEAgD9cjdy+AACAv/vOdz+rzEy/ZAR4vxEAgD+/10S/AACAvwAAQD+rzEy/ZAR4vxEAgD9cjdy+MzNzPwZkN78uqr2+zcxsPwAAQL9EUM2+MzNzPwZkN78Ej++8zcxsPwAAQL+9NwY2AABAPwAAQL+9NwY23sxsPwAAQD/XE82+RDNzP/VjNz8uqr2+EQBAPwAAQD+9NwY23sxsPwAAQD+9NwY2RDNzP/VjNz8Ej++8MzNzPwZkN7+/10S/zcxsPwAAQL+rzEy/AABAPwAAQL+rzEy/MzNzPwZkN79cjdy+3sxsPwAAQD+rzEy/RDNzP/VjNz+/10S/EQBAPwAAQD+rzEy/RDNzP/VjNz9cjdy+MzNzvwZkNz8uqr2+zcxsvwAAQD9EUM2+MzNzvwZkNz8Ej++8zcxsvwAAQD+9NwY2AABAvwAAQD+9NwY23sxsvwAAQL/XE82+RDNzv/VjN78uqr2+EQBAvwAAQL+9NwY23sxsvwAAQL+9NwY2RDNzv/VjN78Ej++8MzNzvwZkNz+/10S/zcxsvwAAQD+rzEy/AABAvwAAQD+rzEy/MzNzvwZkNz9cjdy+3sxsvwAAQL+rzEy/RDNzv/VjN7+/10S/EQBAvwAAQL+rzEy/RDNzv/VjN79cjdy+EQBAPwAAQD/NzMy9AABAPwAAQL/NzMy9EQBAvwAAQL/NzMy9AABAvwAAQD/NzMy9UwR4PxkAgL9cjdy+AACAPwAAQL+rzEy/UwR4PxEAgL+/10S/AACAP/vOd7+rzEy/dQR4vwgAgL9cjdy+AACAvwAAQL+rzEy/AACAv+rOd7+rzEy/dQR4vwgAgL+/10S/UwR4PxkAgL8Ej++8AACAP/vOd7+9NwY2AACAPwAAQL+9NwY2UwR4PxEAgL8uqr2+AACAP/vOd79eFM2+AACAvwAAQL+9NwY2AACAv+rOd7+9NwY2dQR4vwgAgL8Ej++8AACAv+rOd7+Y+8y+dQR4vwgAgL8uqr2+")
}]
blend_shape_mode = 0

[sub_resource type="ArrayMesh" id="ArrayMesh_psvhb"]
resource_name = "FoodHolder_crate_Cube_177"
lightmap_size_hint = Vector2i(134, 130)
_surfaces = [{
"aabb": AABB(-1, -1, -0.799998, 2, 2.00001, 0.8),
"attribute_data": PackedByteArray("3WBIP9wmDD4XfnU/HrsPP91gSD9MeFI+dgt/Pwh+ID/dYEg/cA2MPXYLfz/gB/4+BoJYP0AvDD4XfnU/kuvMPQaCWD9MeFI+dgt/P93IDT0Gglg/THhSPhd+dT/dyA09mkFYPyhWXT5jIn0/UZIvPwaCWD9ALxw+dgt/PwAAQD8Gglg/THhiPnYLfz/kRi4/mkFYP8hvIT5jIn0/86w+P91gSD+IQns+jYl0O/zATz+aQVg/BBt3PukfnD7dyE0/BoJYP4hCez4ykZ4+/MBPP0mhSD8EG3c+YUsLPN3ITT9JoUg/kN/CPYLDKT/dyE0/BoJYP4BeuD09ULk+/MBPP5pBWD+Q38I9d8G7Pk3KTT/dYEg/uE24PS78Kj/8wE8/SaFIP8hvET4bEyk/9EKvPppBWD8oVk0+x0Q6P4ZeKD9JoUg/KFZNPsdEOj/0Qq8+mkFYP8hvET4bEyk/hl4oP91gSD9MeFI+F351Pwh+ID9JoUg/yG8hPliAZz/0Qq8+3WBIP0x4Yj4qZ3c/MzOzPt1gSD/cJhw+fjVmPzMzsz5JoUg/KFZdPssldj/0Qq8+BoJYP3ANjD12C38/lVopPgaCWD9wDYw9F351P5ZaKT7dYEg/jF1CPo2JdDt6oVc/mkFYP6QSPj7bH5w+W6lVPwaCWD8cZEI+MpGePnqhVz9JoUg/pBI+Pp9JCzzKqlU/mkFYP7DYFj5rhGc/8AO/PgaCWD9wDaw9Kmd3Py/0wj4Gglg/QC8cPn41Zj8v9MI+mkFYP9j2tj3WEHY/8AO/Pt1gSD9wDaw9jYl0O/zAjz4Gglg/THhiPjKRnj4rtdI+BoJYP3ANrD0ykZ4+/MCPPt1gSD9MeGI+jYl0Oyu10j4Gglg/wDWwPHqgMj9bqVU/SaFIP2Db2zweCss+eqFXP91gSD/ANbA81pjIPlupVT+aQVg/YNvbPNZnMT96oVc/3WBIP3ANjD0XfnU/4Af+PkmhSD/Y9rY9s3wTP1dqZT/dYEg/3CYcPuFXIz92Ymc/3WBIP3ANrD01JhI/dmJnP0mhSD+w2BY+4AoiP1dqZT8p51c/ICcMPswH6T5TK3U/3X1WP3ANjD3WmMg+8AN/PynnVz9wDYw91pjIPlMrdT8p51c/bBIMPqJKjT7wA38/3X1WP0x4Uj7L2a0+Uyt1PynnVz9MeFI+y9mtPvADfz8p51c/iEJ7PpEeaD9u5AY/zhpYPwQbdz5+NWY/gmtGPynnVz+IQns+kR5oP37gRz/OGlg/BBt3Pn41Zj9sWQg/zhpYPyhWXT5+NWY/PvRWPynnVz9sEhw+kR5oP3ZiZz8p51c/THhiPpEeaD9bqVU/zhpYP8hvIT5+NWY/pghmP84aWD+Q38I9/d1tP6LpJj8p51c/2CS4Pd7Cbz/sxM4+zhpYP5Dfwj3L2W0/GK/RPinnVz9ATrg93sJvP4ZeKD/OGlg/yG8RPkV6YD/0Qm8/zhpYPyhWTT4bEyk/2YldP84aWD8oVk0+RXpgP9mJXT/OGlg/yG8RPhsTKT/0Qm8/3X1WP0x4cj7l7FY/ip0YPynnVz9MeHI+RXpgP/VCrz7dfVY/THhyPuXsVj/0Qq8+KedXP0x4cj5FemA/ip0YP84aWD/IbyE+5exWP/qpgj4p51c/THhiPvjVWD83cqM+KedXPyAnHD741Vg/AACAPs4aWD8oVl0+5exWP63boD7dfVY/cA2MPYLKWT5TK3U/3X1WP3ANrD3hVyM/jtwIP919Vj9MeGI+nd0CP/zAjz7dfVY/cA2sPeFXIz/8wI8+3X1WP0x4Yj6d3QI/jtwIPynnVz9wDYw9g8pZPvADfz8p51c/jF1CPt7Cbz/dyA09zhpYP6QSPj4G3m0/eceQPinnVz/ITEI+3sJvPzuxkz7OGlg/pBI+PsvZbT9hGiU9zhpYP7DYFj7xxhI/DL0QPynnVz9wDaw9nd0CP+zEDj8p51c/bBIcPkkPFD/sxA4/zhpYP9j2tj1+NAQ/DL0QP84aWD9g29s83sJvP+K7Lz8p51c/wDWwPMvZbT/0Qm8/zhpYP2Db2zzewm8/+M1tPynnVz/ANbA8y9ltP+RGLj/dfVY/4BoYPWKNCT3ZiV0/KedXP+AaGD2YSI8+dmJnPynnVz/gGhg9YI0JPXZiZz/dfVY/4BoYPZlIjz7ZiV0/zhpYP9j2lj2wxgQ/9EJvP84aWD+w2AY+C7+aPtmJXT/OGlg/sNgGPrDGBD/ZiV0/zhpYP9j2lj0Lv5o+9EJvP919Vj9MeFI+sMYEP/ADfz/OGlg/2Pa2PWMifT8gAmY+KedXPyAnHD52C38/O7GTPinnVz9wDaw9dgt/Pwh+YD7OGlg/sNgWPmMifT+mApE+KedXP0x4Uj6wxgQ/Uyt1P7n7SD8gJww+ZLkaP1MrdT8GZUo/cA2MPemBCj/wA38/uftIP3ANjD3pgQo/Uyt1P7n7SD9sEgw+NFzGPfADfz8GZUo/THhSPmtMJD5TK3U/uftIP0x4Uj5rTCQ+8AN/P7n7SD+IQns+rDFRPwAAgD4VyEg/BBt3PplITz8nDv8+uftIP4hCez6sMVE/EPwAPxXISD8EG3c+mUhPP/zpgj4VyEg/KFZdPh5UKj9iJzY/uftIP2wSHD7HRDo/gh84P7n7SD9MeGI+GxMpP4IfOD8VyEg/yG8hPkr1OD9iJzY/FchIP5Dfwj34WHo+KE9tP7n7SD/YJLg9jYl0O/RCbz8VyEg/kN/CPQifFzzVSm0/uftIP0BOuD0AAIA+9EJvPxXISD/IbxE+RXpgP3qhVz8VyEg/KFZNPplITz/ohR4/FchIPyhWTT6ZSE8/eqFXPxXISD/IbxE+RXpgP+iFHj8GZUo/THhyPoLKGT8+8IM+uftIP0x4cj7hVyM/GBB8OwZlSj9MeHI+gsoZP8EPfDu5+0g/THhyPuFXIz8/8IM+FchIP8hvIT4FcRM/1UptP7n7SD9MeGI+4VcjP/RCbz+5+0g/ICccPjUmEj/0Qm8/FchIPyhWXT6AFiI/1UptPwZlSj9wDYw9YI0JPVMrdT8GZUo/cA2sPcdE+j4MvRA/BmVKP0x4Yj49ULk++IGfPgZlSj9wDaw9x0T6PviBnz4GZUo/THhiPj1QuT4MvRA/uftIP3ANjD1ijQk98AN/P7n7SD+MXUI+y9mtPvzAjz4VyEg/pBI+PhsQqj6tZwc/uftIP8hMQj7L2a0+jtwIPxXISD+kEj4+pAeqPiyrkj4VyEg/sNgWPipndz9UzaA+uftIP3ANrD0XfnU/AACAPrn7SD9sEhw+F351Pzdyoz4VyEg/2Pa2PSpndz/dwoI+FchIP2Db2zyRHmg/vhglPbn7SD/ANbA8fjVmPzuxkz4VyEg/YNvbPJEeaD9Ex5A+uftIP8A1sDx+NWY/3cgNPQZlSj/gGhg96YEKP+wPfDu5+0g/4BoYPUkPFD8+8IM+uftIP+AaGD1JDxQ/wQ98OwZlSj/gGhg96YEKPz/wgz4VyEg/2PaWPUV6YD+GXmg+FchIP7DYBj6ZSE8/wQ98OxXISD+w2AY+mUhPP4ZeaD4VyEg/2PaWPUV6YD/BD3w7BmVKP0x4Uj4u/Co/8AN/PxXISD/Y9rY9rudfP/iBnz65+0g/ICccPt7Cbz83cqM+uftIP3ANrD0ykV4/N3KjPhXISD+w2BY+0nVuP/iBnz65+0g/THhSPi78Kj9TK3U/3X1WP0x4Yj54dos+wQ98OwZlSj9MeGI+rcYEPz/wgz7dfVY/THhiPnN2iz4/8IM+BmVKP0x4Yj6wxgQ/wQ98O919Vj9c6iA+WIx0O8EPfDsGZUo/XOogPvX/fz4/8IM+BmVKP1zqID6NiXQ7P/CDPt19Vj9c6iA+AACAPsEPfDuaQVg/2PaWPcdEOj/BD3w7SaFIP7DYBj4bEyk/N3KjPkmhSD/Y9pY9x0Q6Pzdyoz6aQVg/sNgGPhsTKT/sD3w73WBIP+AaGD3HRDo/fuBHPwaCWD/gGhg9b+HXPuAHPj/dYEg/4BoYPcdEOj/gBz4/BoJYP+AaGD1v4dc+fuBHPwaCWD9MeHI+y9mtPn7gRz/dYEg/THhyPmCNCT3gBz4/3WBIP0x4cj5gjQk9fuBHPwaCWD9MeHI+y9mtPuAHPj9JoUg/yG8RPuFXIz+GXig/mkFYPyhWTT6kB6o+aqUWP0mhSD8oVk0+4VcjP2qlFj+aQVg/yG8RPqQHqj6GXig/mkFYP7DYFj5jIn0/ksNmPwaCWD9wDaw9dgt/P3Ijdz8Gglg/QC8cPnYLfz9XamU/mkFYP9j2tj1jIn0/lsJ1P91gSD+MXUI+YI1JP8EPfDuaQVg/pBI+PkykRz+n7aA+BoJYPxxkQj5gjUk/N3KjPkmhSD+kEj4+sKVHP2iTDzyaQVg/2PaWPY2JdDuGXig/SaFIP7DYBj4ykZ4+aqUWP0mhSD/Y9pY9MpGePoZeKD+aQVg/sNgGPreJdDtqpRY/SaFIP9j2tj1IMG8/0RF8O91gSD/cJhw+dgt/P9ELPTzdYEg/cA2sPcvZbT/RCz08SaFIP7DYFj52vn0/wQ98OwaCWD9wDYw9rDFRP/ADfz8Gglg/QC8MPkX4QD9TK3U/BoJYP3ANjD2sMVE/Uyt1P91gSD9MeFI+Kmd3P/ADfz/dYEg/3CYMPlYkZz9TK3U/3WBIP0x4Uj4qZ3c/Uyt1P0mhSD/IbyE+GX51P2p8Nz/dYEg/THhiPipndz9+4Ec/3WBIP9wmHD4qZ3c/Yic2P0mhSD8oVl0+F351PzqVRj8Gglg/4BoYPaQHqj7nRi4/3WBIP+AaGD3hVyM/gh84PwaCWD/gGhg9pAeqPoIfOD/dYEg/4BoYPeFXIz/kRi4/3WBIP3ANjD3l7FY/8AN/P91gSD9wDYw95exWP1MrdT+aQVg/KFZdPrfB9z47sZM+BoJYP0AvHD5v4dc+/MCPPgaCWD9MeGI+x0T6PvzAjz6aQVg/yG8hPkpz2j47sZM+BoJYP0x4Uj5ntzA/8AN/PwaCWD9MeFI+Z7cwP1MrdT/dYEg/iEJ7PgAAQD83cqM+mkFYPwQbdz4T6UE/DJQPPAaCWD+IQns+AgBAP8EPfDtJoUg/BBt3PhPpQT+s7aA+SaFIP5Dfwj0AAEA/MZ5GPwaCWD+AXrg9E+lBP+RG7j6aQVg/kN/CPWUBQD9Wy/A+3WBIP7hNuD0T6UE/fuBHPwaCWD/ANbA8TKRHP4IfOD9JoUg/YNvbPGCNST94SdE+3WBIP8A1sDxMpEc/7MTOPppBWD9g29s8YI1JP0HdNj8Gglg/THhyPtqJdDuCHzg/3WBIP0x4cj4ykZ4+5EYuP91gSD9MeHI+MpGePoIfOD8Gglg/THhyPo2JdDvnRi4/3WBIP3ANrD0ykZ4+6IXePgaCWD9MeGI+jYl0Owy9ED8Gglg/cA2sPY2JdDvohd4+3WBIP0x4Yj4ykZ4+DL0QPw=="),
"format": ***********,
"index_count": 396,
"index_data": PackedByteArray("AAABAAIAAwAEAAUABgAHAAgABgAJAAcACgALAAwACgANAAsADgAPABAADgARAA8AEgATABQAEgAVABMAAQAAABYAFwAYABkAFwAaABgABAADABsAGwADABwAHQAeAB8AHQAgAB4AIQAiACMAIQAkACIAJQAmACcAJQAoACYAKQAqACsAKQAsACoAAAACAC0ALgAvADAALgAxAC8AMgAzADQANQA2ADcAOAA5ADoAOAA7ADkAPAA9AD4APAA/AD0AQABBAEIAQABDAEEARABFAEYARABHAEUASABJAEoASABLAEkATABNAE4ATABPAE0ANgA1AFAAUQBSAFMAUQBUAFIAUAA1AFUAVgBXAFgAVgBZAFcAWgBbAFwAWgBdAFsAXgBfAGAAXgBhAF8AYgBjAGQAYgBlAGMAZgBnAGgAZgBpAGcAagAzADIAawBsAG0AawBuAGwAagAyAG8AcABxAHIAcwB0AHUAdgB3AHgAdgB5AHcAegB7AHwAegB9AHsAfgB/AIAAfgCBAH8AggCDAIQAggCFAIMAhgCHAIgAhgCJAIcAigCLAIwAigCNAIsAdABzAI4AjwCQAJEAjwCSAJAAjgBzAJMAlACVAJYAlACXAJUAmACZAJoAmACbAJkAnACdAJ4AnACfAJ0AoAChAKIAoACjAKEApAClAKYApACnAKUAqABxAHAAqQCqAKsAqQCsAKoAqABwAK0ArgCvALAArgCxAK8AsgCzALQAsgC1ALMAtgC3ALgAtgC5ALcAugC7ALwAugC9ALsAvgC/AMAAvgDBAL8AwgDDAMQAwgDFAMMAxgDHAMgAxgDJAMcAygDLAMwAygDNAMsAzgDPANAAzgDRAM8A0gDTANQA0gDVANMA1gDXANgA2QDaANsA3ADdAN4A3ADfAN0A4ADhAOIA4ADjAOEA2gDkAOUA5gDnAOgA5gDpAOcA2gDZAOQA1wDqAOsA6gDXANYA7ADtAO4A7ADvAO0A8ADxAPIA8ADzAPEA9AD1APYA9AD3APUA+AD5APoA+AD7APkA/AD9AP4A/AD/AP0A"),
"material": SubResource("StandardMaterial3D_7n6n0"),
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 256,
"vertex_data": PackedByteArray("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")
}]
blend_shape_mode = 0
shadow_mesh = SubResource("ArrayMesh_a533n")

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_i3mnq"]
cull_mode = 1
albedo_color = Color(0, 0, 0, 1)
grow = true
grow_amount = 0.02

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_n526l"]
resource_name = "restaurant"
next_pass = SubResource("StandardMaterial3D_i3mnq")
cull_mode = 2
diffuse_mode = 3
specular_mode = 2
vertex_color_use_as_albedo = true
albedo_texture = ExtResource("2_dyawf")
roughness = 0.14
backlight_enabled = true
backlight = Color(0.258824, 0.12549, 0.0117647, 1)

[sub_resource type="ArrayMesh" id="ArrayMesh_inryh"]
_surfaces = [{
"aabb": AABB(-0.296312, -0.358659, -0.606166, 0.684831, 0.822775, 0.595569),
"format": 34359742465,
"index_count": 468,
"index_data": PackedByteArray("DAArAA0ADAApACsACgApAAsACgAoACkACQBOAEwACQAIAE4AKgAiACsAKgAjACIACwAMAAEACwApAAwAMQAyADMAMQAwADIAKwADAA0AKwAiAAMAEgAvABMAEgAtAC8ATwAjACoATwAOACMAIgAsAC0AIgAjACwAEQBKAEgAEQAQAEoALgAkAC8ALgAlACQAIgASAAMAIgAtABIANQA2ADcANQA0ADYALwAHABMALwAkAAcAGAAzABkAGAAxADMAIQAoAAoAIQBNACgASwAlAC4ASwAUACUAJAAwADEAJAAlADAAFwBGAEQAFwAWAEYAMgAmADMAMgAnACYAJAAYAAcAJAAxABgAEAA4ADkAEAARADgAMwAFABkAMwAmAAUAHgA3AB8AHgA1ADcADgAsACMADgBJACwARwAnADIARwAaACcAJgA0ADUAJgAnADQAHQBCAEAAHQAcAEIANgALADcANgAKAAsAJgAeAAUAJgA1AB4AOQA6ADsAOQA4ADoANwABAB8ANwALAAEANABDADYANABBAEMAFAAwACUAFABFADAAQwAKADYAQwAhAAoAFgA5ADsAFgAXADkAAgA4ABEAAgAIADgAOgAdADsAOgAcAB0AEAAXAAYAEAA5ABcACAA6ADgACAAJADoAOwAEABYAOwAdAAQAPQA+AD8APQA8AD4ACQAcADoACQAAABwAEgA8AD0AEgATADwADAA9AD8ADAANAD0ABwA8ABMABwAYADwAPgAfAD8APgAeAB8AEgANAAMAEgA9AA0AGAA+ADwAGAAZAD4APwABAAwAPwAfAAEAQQBCAEMAQQBAAEIAGQAeAD4AGQAFAB4ARQBGAEcARQBEAEYAGgBAAEEAGgAbAEAABABAABsABAAdAEAAQgAhAEMAQgAgACEAMABHADIAMABFAEcAHAAgAEIAHAAAACAASQBKAEsASQBIAEoAFABEAEUAFAAVAEQABgBEABUABgAXAEQARgAaAEcARgAbABoALABLAC4ALABJAEsAFgAbAEYAFgAEABsATQBOAE8ATQBMAE4ADgBIAEkADgAPAEgAAgBIAA8AAgARAEgASgAUAEsASgAVABQAKABPACoAKABNAE8AEAAVAEoAEAAGABUAIABNACEAIABMAE0AAABMACAAAAAJAEwATgAOAE8ATgAPAA4ACAAPAE4ACAACAA8AGgA0ACcAGgBBADQAKAArACkAKAAqACsALQAuAC8ALQAsAC4A"),
"lods": [0.0618395, PackedByteArray("KQArAD0ATgAOACsAKQA9AD8APQA+AD8APwA3ACkAPgA3AD8AQgApADcAQgAgACkAQQBCADcAIABNACkATQArACkANQBBADcAPgA1ADcANQBHAEEAMwA1AD4AMwBHADUAGAAzAD4AGAA+ADwAPQA8AD4AGAAxADMAMQBHADMALwAYADwAMQAYAC8ALQA8AD0ALQAvADwALQA9ACsALQBJAC8AKwBJAC0ALwBLADEASQBLAC8AKwAOAEkASwBEADEAMQBEAEcASQBKAEsASwBKAEQADgBIAEkASQBIAEoADgBOAEgATQBOACsATQBMAE4AIABMAE0AOABIAE4ATAA4AE4AOgBMACAAOABMADoAOgAgAEIAOQBIADgAOQA4ADoASAA5AEoAOwA6AEIAOQA6ADsAOwBCAEAAQQBAAEIARwBAAEEARwBGAEAAOwBAAEYARABGAEcAFgA5ADsAFgA7AEYAFwBGAEQAFwAWAEYAFwBEAEoAFgAXADkAOQAXAEoA"), 0.104998, PackedByteArray("KQArAD8ALwA/ACsAPwA3ACkAPAA3AD8APAA1ADcANQBAADcAQAApADcAQABMACkAKQBOACsAKQBMAE4ATgBIACsAKwBIAC8ALwA8AD8ASABLAC8AMQA8AC8ALwBLADEAMQA1ADwASwBEADEAOQBEAEsAMQBEAEYAMQBGADUARgBAADUASAA5AEsAOwBAAEYARgBEADkARgA5ADsAOQBIADgAOQA4ADsAOABIAE4ATAA4AE4AOABMADsAOwBMAEAA"), 0.166158, PackedByteArray("TABLACkAKQBLAC8ALwA8ACkAMQA8AC8ALwBLADEASwBGADEAMQA1ADwAMQBGADUANQBGAEEAOwBBAEYAOwBMAEEAQQBMACkAQQApADUAPAA1ACkAOwBLAEwAOwBGAEsA"), 0.310812, PackedByteArray("KwA7AEsAMQBGADUASwBGADEAOwA1AEYAOwBGAEsAMQA1ACsAOwArADUAKwBLADEA")],
"name": "restaurant",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 80,
"vertex_data": PackedByteArray("ysJhPigYvj5vd/u+erGFPmeKiL4NCr++5Jw+vhpTnT4ytgG/5NsavZGhoL6N48S+cCBOPotB0j5Qd+i9NiN9PlVbcL7KE9i9uidVvudBsT6wQQS+/pNeveZ6kL7kre+9hwDTvcnprT44kgi/E94IPvD8wD5gQQa/WhCfPoOSW737EfW+TMyQPv3vVL5JcNK+bqxOPmUimL4e1MW+YMXrPIMUpr4FN8m+DDxNvm4fFD24Dgu/bu5VvkOJZj6AzQm/VpJyvoWXtj7g/Fe+ZDtlviv2qj4dnNu+PE5wvasSqb7jAqe+ByGMvUDxn76MwS++Eg1oviG9qT0Gdcq93gJvvodaiT5M1da9IFjiPZTt2D5AEqq90F4DvqquxT6+w7y98K4pPDChk74MBLC9QtY9PheGhb6UUKK9MjuYPnlRKT4ucqG9zEp9PlcZsD7uM7G9kBt4PpWH0D7rfNK+3gBtPuRV3D6+qEW+UDyMPrRahL4OXCK+GjGQPu+ljb4yWqC+5gyKPpfVmT7cHgW/asqjPqHL8T1u9wW/WbKWvdcwh76Tadm+hr4Rvv/Q/70x7/2+7329vSk3aL62KOa9KD0pvhtmqL1iVuS9Kj2IPtFpLr6QFsq9KNiUPolCK7x8pcC9xttUPv2zmL0kDwm/HHtQPntra77dvue+OssLvcC25r1EbQu/sAg7PK0yhb6Lgeu+2LdZvmeR/72Z39K+ym0BvusLir5lura+HjNmvg7v0b3U2Va+hOcLvusFgL7o1UK+4VKBvaLVZ71sxDW9GFVJvIn+WL54ynC9V0o5PqTslLzojQ+9Z8I5PiHGOb6AeFK9hrO4Ph1lKbwu7z++FEqkPg1ONr4+7jC+Dvy9PgfTBr22c8e+lL+oPreYSr48zq2+hyP7vW8qzD4XK9y+Uq0LvvJ12T60BUW+M3UBPrI84D6HS9e+kCDpPZmg7T6QOju+ICGMPIAHrr56BBW+kLTdPDSit77HlZ++NgVMPttZn7584w2+JulUPtMOqb7QC5y+BmmjPk37sz6Ge0C+rv/APr91KT7iaz2+bFupPiwvqD55rdW+6uvGPvKWED4jE9W+yqYCvqm3mD4YBwa9r7TfvRmP4j2QwK6824oHPs+qrT5gqbq8VcgvPtuNHj5gmy28Jv2QvhNpbT4XseG+5quPvsLzEj0lK+K+QbaXvjthgj7+kVi+q66WvooQdT3ssFe+zSomPuNVkD4m7Ba/GGJPPq0uvD0AdRi/r/nEvS8Ddz56cxm/L+aavbYHRT2qLRu/")
}]
blend_shape_mode = 0

[sub_resource type="ArrayMesh" id="ArrayMesh_0u7c1"]
resource_name = "Potatoes_crate_potatoes_Cube_001"
lightmap_size_hint = Vector2i(34, 46)
_surfaces = [{
"aabb": AABB(-0.296312, -0.358659, -0.606166, 0.684831, 0.822775, 0.595569),
"attribute_data": PackedByteArray("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"),
"format": ***********,
"index_count": 468,
"index_data": PackedByteArray("AAABAAIAAAADAAEABAADAAUABAAGAAMABwAIAAkABwAKAAgACwAMAAEACwANAAwABQAAAA4ABQADAAAADwAQABEADwASABAAAQATAAIAAQAMABMAFAAVABYAFAAXABUAGAANAAsAGAAZAA0AGgAbABcAGgAcABsAHQAeAB8AHQAgAB4AIQAiABUAIQAjACIAGgAUACQAGgAXABQAJQAmACcAJQAoACYAFQApABYAFQAiACkAKgARACsAKgAPABEALAAGAAQALAAtAAYALgAjACEALgAvACMAMAASAA8AMAAxABIAMgAzADQAMgA1ADMAEAA2ABEAEAA3ADYAMAAqADgAMAAPACoAIAA5ADoAIAAdADkAEQA7ACsAEQA2ADsAPAAnAD0APAAlACcAPgAbABwAPgA/ABsAQAA3ABAAQABBADcAQgAoACUAQgBDACgARABFAEYARABHAEUAJgBIACcAJgBJAEgAQgA8AEoAQgAlADwAOgBLAEwAOgA5AEsAJwBNAD0AJwBIAE0AKABOACYAKABPAE4AUAASADEAUABRABIATgBJACYATgBSAEkAUwA6AEwAUwBUADoAVQA5AB0AVQAKADkASwBEAEwASwBHAEQAVgAyAFcAVgBYADIACgBLADkACgAHAEsATABZAFMATABEAFkAWgBbAFwAWgBdAFsABwBHAEsABwBeAEcAXwBdAFoAXwBgAF0AYQBaAFwAYQBiAFoAKQBjABYAZABlAF0AWwBmAFwAWwBnAGYAXwBiAGgAXwBaAGIAZQBbAF0AZQBpAFsAXABqAGEAXABmAGoATwBrAE4ATwBsAGsAaQBnAFsAaQBtAGcAUQAzAEAAUQA0ADMAbgBsAE8AbgBvAGwAcABsAG8AWQBEAEYAawBSAE4AawBxAFIAEgBAABAAEgBRAEAARwByAEUARwBeAHIAPwBzAC4APwB0AHMAUAA0AFEAUAB1ADQAVwA0AHUAVwAyADQAMwBBAEAAMwB2AEEAGwAuACEAGwA/AC4ANQB2ADMANQB3AHYALQB4ABgALQB5AHgAPgB0AD8APgB6AHQAVQAfAHsAVQAdAB8AcwAvAC4AcwB8AC8ABgAYAAsABgAtABgAVgB1AH0AVgBXAHUAfgAtACwAfgB5AC0AXgAJAHIAXgAHAAkAeAAZABgAeAB/ABkACgB7AAgACgBVAHsAbgAoAEMAbgBPACgABgABAAMABgALAAEAFwAhABUAFwAbACEA"),
"material": SubResource("StandardMaterial3D_n526l"),
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 128,
"vertex_data": PackedByteArray("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")
}]
blend_shape_mode = 0
shadow_mesh = SubResource("ArrayMesh_inryh")

[sub_resource type="BoxShape3D" id="BoxShape3D_c2m6h"]
size = Vector3(2.03979, 1.2652, 2.0144)

[node name="PotatoesBasketProp" instance=ExtResource("1_d13mx")]

[node name="FoodHolder" type="MeshInstance3D" parent="Meshes" index="0" groups=["VisibleGroupInterior"]]
transform = Transform3D(1, 0, 0, 0, 0, -1, 0, 1, 0, 0, 0, 0)
mesh = SubResource("ArrayMesh_psvhb")
skeleton = NodePath("")

[node name="Potatoes" type="MeshInstance3D" parent="Meshes" index="1" groups=["VisibleGroupInterior"]]
transform = Transform3D(1, 0, 0, 0, -0.177257, -0.984165, 0, 0.984165, -0.177257, -0.418803, 0.202944, 0.262866)
mesh = SubResource("ArrayMesh_0u7c1")
skeleton = NodePath("")

[node name="Potatoes2" type="MeshInstance3D" parent="Meshes" index="2" groups=["VisibleGroupInterior"]]
transform = Transform3D(0.0999796, -0.979234, 0.176368, 0, -0.177257, -0.984165, 0.994989, 0.0983964, -0.017722, -0.157472, 0.202944, -0.43075)
mesh = SubResource("ArrayMesh_0u7c1")
skeleton = NodePath("")

[node name="Potatoes3" type="MeshInstance3D" parent="Meshes" index="3" groups=["VisibleGroupInterior"]]
transform = Transform3D(0.746917, -0.654388, 0.117861, -1.85331e-09, -0.177257, -0.984165, 0.664918, 0.735089, -0.132396, 0.357001, 0.202944, 0.152053)
mesh = SubResource("ArrayMesh_0u7c1")
skeleton = NodePath("")

[node name="Potatoes4" type="MeshInstance3D" parent="Meshes" index="4" groups=["VisibleGroupInterior"]]
transform = Transform3D(0.716347, -0.677798, -0.165641, -0.211498, 0.015296, -0.977259, 0.664918, 0.735089, -0.132396, 0.357001, 0.567067, -0.40195)
mesh = SubResource("ArrayMesh_0u7c1")
skeleton = NodePath("")

[node name="CollisionShape3D" type="CollisionShape3D" parent="StaticBody3D" index="0"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -0.0125732, 0.626312, -0.00012207)
shape = SubResource("BoxShape3D_c2m6h")
