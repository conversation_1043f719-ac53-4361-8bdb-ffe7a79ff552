extends Node
class_name ProphauntAudioSystem

# Prophaunt 3D audio system
# Handles prop detection sounds, ambient audio, and game-specific sound effects

# Audio configuration
var prop_detection_range: float = 15.0
var prop_detection_interval: float = Constants.PROPHAUNT_PROP_SOUND_COOLDOWN
var ambient_sound_range: float = 25.0

# Sound types for Prophaunt
enum ProphauntSoundType {
	PROP_DETECTION,
	PROP_MOVEMENT,
	HAUNTER_FOOTSTEPS,
	WEAPON_SHOT,
	GRENADE_EXPLOSION,
	HEX_CAST,
	DISGUISE_CHANGE,
	ROUND_WARNING,
	ROUND_START,
	ROUND_END,
	PLAYER_ELIMINATION
}

# Audio sources tracking
var active_audio_sources: Dictionary = {}
var prop_detection_timers: Dictionary = {}

# Audio resources
var sound_resources = {
	ProphauntSoundType.PROP_DETECTION: SoundManager.SOUND_TYPE.Checkpoint,
	ProphauntSoundType.PROP_MOVEMENT: SoundManager.SOUND_TYPE.Busy,
	ProphauntSoundType.HAUNTER_FOOTSTEPS: SoundManager.SOUND_TYPE.Busy,
	ProphauntSoundType.WEAPON_SHOT: SoundManager.SOUND_TYPE.Pistol,
	ProphauntSoundType.GRENADE_EXPLOSION: SoundManager.SOUND_TYPE.ExplosionMedium,
	ProphauntSoundType.HEX_CAST: SoundManager.SOUND_TYPE.ORDER,
	ProphauntSoundType.DISGUISE_CHANGE: SoundManager.SOUND_TYPE.SnowballHit,
	ProphauntSoundType.ROUND_WARNING: SoundManager.SOUND_TYPE.Horn1,
	ProphauntSoundType.ROUND_START: SoundManager.SOUND_TYPE.Horn2,
	ProphauntSoundType.ROUND_END: SoundManager.SOUND_TYPE.Horn3,
	ProphauntSoundType.PLAYER_ELIMINATION: SoundManager.SOUND_TYPE.ExplosionBig
}

signal audio_played(sound_type: ProphauntSoundType, position: Vector3, range: float)

func _ready():
	# Initialize audio system
	setup_audio_system()

func setup_audio_system():
	"""Set up the Prophaunt audio system"""
	print("Prophaunt Audio System initialized")

func play_3d_sound(sound_type: ProphauntSoundType, position: Vector3, _range: float = 15.0, player_filter: Array = []):
	"""Play a 3D sound at the specified position"""
	var sound_manager_type = sound_resources.get(sound_type, SoundManager.SOUND_TYPE.Checkpoint)
	
	if Constants.is_server:
		# Server: send sound to clients in range
		send_sound_to_players_in_range(sound_manager_type, position, _range, player_filter)
	else:
		# Client: play sound locally
		SoundManager.play_sound(sound_manager_type)
	
	audio_played.emit(sound_type, position, range)

func send_sound_to_players_in_range(sound_type: SoundManager.SOUND_TYPE, position: Vector3, _range: float, player_filter: Array = []):
	"""Send sound to all players within range (server-side)"""
	if not Constants.server:
		return
	
	var server = Constants.server
	for key in server.players_data.keys():
		if server.is_bot(key) or server.is_dc(key):
			continue
		
		# Skip filtered players
		if player_filter.size() > 0 and key not in player_filter:
			continue
		
		# Check if player is in range
		var player_position = server.players[key].global_position
		var distance = position.distance_to(player_position)
		
		if distance <= range:
			SoundManager.play_sound.rpc_id(key, sound_type)

func play_prop_detection_sound(prop_player_id: int, position: Vector3):
	"""Play prop detection sound for a specific prop"""
	# Only haunters should hear prop detection sounds
	var haunter_filter = get_haunter_player_ids()
	play_3d_sound(ProphauntSoundType.PROP_DETECTION, position, prop_detection_range, haunter_filter)
	
	print("Prop detection sound played at: ", position, " for prop: ", prop_player_id)

func play_movement_sound(player_id: int, position: Vector3, is_prop: bool):
	"""Play movement sound for a player"""
	var sound_type = ProphauntSoundType.PROP_MOVEMENT if is_prop else ProphauntSoundType.HAUNTER_FOOTSTEPS
	var _range = 8.0 if is_prop else 12.0  # Props are quieter
	
	# Don't play movement sound for the player themselves
	var filter = get_all_player_ids()
	filter.erase(player_id)
	
	play_3d_sound(sound_type, position, _range, filter)

func play_weapon_sound(position: Vector3, weapon_type: String):
	"""Play weapon sound"""
	var sound_type = ProphauntSoundType.WEAPON_SHOT
	var _range = 25.0
	
	match weapon_type:
		"gun":
			sound_type = ProphauntSoundType.WEAPON_SHOT
			_range = 25.0
		"grenade":
			sound_type = ProphauntSoundType.GRENADE_EXPLOSION
			_range = 35.0
	
	play_3d_sound(sound_type, position, _range)

func play_ability_sound(position: Vector3, ability_type: String):
	"""Play ability sound"""
	var sound_type = ProphauntSoundType.HEX_CAST
	var _range = 10.0
	
	match ability_type:
		"hex":
			sound_type = ProphauntSoundType.HEX_CAST
			_range = 10.0
		"disguise":
			sound_type = ProphauntSoundType.DISGUISE_CHANGE
			_range = 5.0
	
	play_3d_sound(sound_type, position, _range)

func play_round_sound(sound_type: ProphauntSoundType):
	"""Play round-related sound to all players"""
	play_3d_sound(sound_type, Vector3.ZERO, 1000.0)  # Large range to reach all players

func play_elimination_sound(position: Vector3):
	"""Play elimination sound"""
	play_3d_sound(ProphauntSoundType.PLAYER_ELIMINATION, position, 30.0)

# Prop detection system
func start_prop_detection_timer(prop_player_id: int):
	"""Start detection timer for a prop"""
	if prop_player_id in prop_detection_timers:
		return  # Timer already running
	
	prop_detection_timers[prop_player_id] = prop_detection_interval
	print("Started detection timer for prop: ", prop_player_id)

func update_prop_detection_timers(delta: float):
	"""Update prop detection timers"""
	var to_remove = []
	
	for prop_id in prop_detection_timers.keys():
		prop_detection_timers[prop_id] -= delta
		
		if prop_detection_timers[prop_id] <= 0:
			# Time to play detection sound
			trigger_prop_detection_sound(prop_id)
			to_remove.append(prop_id)
	
	# Remove expired timers
	for prop_id in to_remove:
		prop_detection_timers.erase(prop_id)

func trigger_prop_detection_sound(prop_player_id: int):
	"""Trigger detection sound for a prop"""
	if not Constants.server:
		return
	
	var server = Constants.server
	if prop_player_id in server.players and server.players[prop_player_id]:
		var position = server.players[prop_player_id].global_position
		play_prop_detection_sound(prop_player_id, position)
		
		# Restart timer
		start_prop_detection_timer(prop_player_id)

func stop_prop_detection_timer(prop_player_id: int):
	"""Stop detection timer for a prop"""
	if prop_player_id in prop_detection_timers:
		prop_detection_timers.erase(prop_player_id)
		print("Stopped detection timer for prop: ", prop_player_id)

# Ambient audio system
func play_ambient_map_audio(map_name: String):
	"""Play ambient audio for the current map"""
	# This would play map-specific ambient sounds
	print("Playing ambient audio for map: ", map_name)

func stop_ambient_audio():
	"""Stop all ambient audio"""
	print("Stopping ambient audio")

# Audio filtering helpers
func get_haunter_player_ids() -> Array:
	"""Get all haunter player IDs"""
	var haunters = []
	if not Constants.server:
		return haunters
	
	var server = Constants.server
	for key in server.players_data.keys():
		if server.is_bot(key) or server.is_dc(key):
			continue
		
		if server.players_data[key].get("prophaunt_team", -1) == Constants.ProphauntTeam.HAUNTERS:
			haunters.append(key)
	
	return haunters

func get_prop_player_ids() -> Array:
	"""Get all prop player IDs"""
	var props = []
	if not Constants.server:
		return props
	
	var server = Constants.server
	for key in server.players_data.keys():
		if server.is_bot(key) or server.is_dc(key):
			continue
		
		if server.players_data[key].get("prophaunt_team", -1) == Constants.ProphauntTeam.PROPS:
			props.append(key)
	
	return props

func get_all_player_ids() -> Array:
	"""Get all player IDs"""
	var players = []
	if not Constants.server:
		return players
	
	var server = Constants.server
	for key in server.players_data.keys():
		if server.is_bot(key) or server.is_dc(key):
			continue
		players.append(key)
	
	return players

# Audio management
func _process(delta):
	"""Update audio system"""
	if Constants.is_server:
		update_prop_detection_timers(delta)

func reset_audio_system():
	"""Reset audio system for new round"""
	prop_detection_timers.clear()
	active_audio_sources.clear()

func cleanup_audio_system():
	"""Clean up audio system"""
	stop_ambient_audio()
	reset_audio_system()

# Volume and range configuration
func set_prop_detection_range(_range: float):
	"""Set prop detection sound range"""
	prop_detection_range = _range

func set_prop_detection_interval(interval: float):
	"""Set prop detection sound interval"""
	prop_detection_interval = interval

func get_prop_detection_range() -> float:
	"""Get prop detection sound range"""
	return prop_detection_range

func get_prop_detection_interval() -> float:
	"""Get prop detection sound interval"""
	return prop_detection_interval

# Debug functions
func debug_play_all_sounds():
	"""Debug function to test all sounds"""
	var test_position = Vector3(0, 1, 0)
	
	for sound_type in ProphauntSoundType.values():
		play_3d_sound(sound_type, test_position)
		await get_tree().create_timer(1.0).timeout

func debug_print_active_timers():
	"""Debug function to print active timers"""
	print("Active prop detection timers: ", prop_detection_timers)
