extends Node


const snowball_spawn_radius = 50
const snowball_scene = preload("res://Scenes/FreeRide/Assets/SnowField/Items/SnowballBody.tscn")
const grenade_scene = preload("res://prophaunt/assets/Guns/Scene/GrenadeBody.tscn")

func _ready() -> void:
	pass # Replace with function body.


#Server
func call_spawn_grenade(position: Vector3, direction: Vector3, force: float):
	var dir = direction * force
	var buf = PackedByteArray()
	
	buf.resize(6 + 6)
	buf.encode_half(0, position.x) #2bytes
	buf.encode_half(2, position.y) #2bytes
	buf.encode_half(4, position.z) #2bytes
	buf.encode_half(6, dir.x) #2bytes
	buf.encode_half(8, dir.y) #2bytes
	buf.encode_half(10, dir.z) #2bytes
	
	return server_spawn_grenade(buf)


#Client
@rpc("reliable", "authority", "call_remote", Constants.CHANNEL_SPAWN_MANAGER)
func client_spawn_grenade(buf):
	var spawn_position = Vector3(buf.decode_half(0), buf.decode_half(2), buf.decode_half(4))
	var direction = Vector3(buf.decode_half(6), buf.decode_half(8), buf.decode_half(10))
	
	var grenade = grenade_scene.instantiate()
	add_child(grenade)
	grenade.global_position  = spawn_position
	grenade.apply_central_impulse(direction)
	SoundManager.play_sound(SoundManager.SOUND_TYPE.SnowballThrow)


#Server
func server_spawn_grenade(buf: PackedByteArray):
	var server = Constants.server
	
	var spawn_position = Vector3(buf.decode_half(0), buf.decode_half(2), buf.decode_half(4))
	var direction = Vector3(buf.decode_half(6), buf.decode_half(8), buf.decode_half(10))
	
	var grenade = grenade_scene.instantiate()
	add_child(grenade)
	grenade.global_position = spawn_position
	grenade.apply_central_impulse(direction)
	#SoundManager.play_sound(SoundManager.SOUND_TYPE.SnowballThrow)
	
	var send_data = buf.duplicate()
	send_data.resize(6 + 6)
	for key in server.players_data.keys():
		if not server.is_bot(key):
			client_spawn_grenade.rpc_id(key, send_data)
	
	return grenade


#Client
func call_spawn_snowball(position: Vector3, direction: Vector3, force: float):
	var dir = direction * force
	var buf = PackedByteArray()
	var pos = Constants.client.my_player_scene.global_position
	
	buf.resize(6 + 6 + 6)
	buf.encode_half(0, position.x) #2bytes
	buf.encode_half(2, position.y) #2bytes
	buf.encode_half(4, position.z) #2bytes
	buf.encode_half(6, dir.x) #2bytes
	buf.encode_half(8, dir.y) #2bytes
	buf.encode_half(10, dir.z) #2bytes
	buf.encode_half(12, pos.x) #2bytes
	buf.encode_half(14, pos.y) #2bytes
	buf.encode_half(16, pos.z) #2bytes
	
	server_spawn_snowball.rpc_id(1, buf)


#Client
@rpc("reliable", "authority", "call_remote", Constants.CHANNEL_SPAWN_MANAGER)
func client_spawn_snowball(buf):
	var spawn_position = Vector3(buf.decode_half(0), buf.decode_half(2), buf.decode_half(4))
	var direction = Vector3(buf.decode_half(6), buf.decode_half(8), buf.decode_half(10))
	
	var snowball = snowball_scene.instantiate()
	add_child(snowball)
	snowball.make_client()
	snowball.global_position  = spawn_position
	snowball.apply_central_impulse(direction)
	SoundManager.play_sound(SoundManager.SOUND_TYPE.SnowballThrow)


#Server
@rpc("reliable", "any_peer", "call_remote", Constants.CHANNEL_SPAWN_MANAGER)
func server_spawn_snowball(buf: PackedByteArray):
	var dist = snowball_spawn_radius * snowball_spawn_radius
	var server = Constants.server
	var position = Vector3(buf.decode_half(12), buf.decode_half(14), buf.decode_half(16))
	
	
	var spawn_position = Vector3(buf.decode_half(0), buf.decode_half(2), buf.decode_half(4))
	var direction = Vector3(buf.decode_half(6), buf.decode_half(8), buf.decode_half(10))
	
	var snowball = snowball_scene.instantiate()
	add_child(snowball)
	snowball.make_server(multiplayer.get_remote_sender_id())
	snowball.global_position = spawn_position
	snowball.apply_central_impulse(direction)
	SoundManager.play_sound(SoundManager.SOUND_TYPE.SnowballThrow)
	
	var send_data = buf.duplicate()
	send_data.resize(6 + 6)
	for key in server.players_data.keys():
		if not server.is_bot(key):
			var pos = server.players[key].global_position as Vector3
			if (pos - position).length_squared() <= dist:
				client_spawn_snowball.rpc_id(key, send_data)


######################################Cigarrete
#Client
func call_spawn_throwable_item(position: Vector3, direction: Vector3, item:ThrowableFireworkInventoryItem):
	var dir = direction * item.force
	var buf = PackedByteArray()
	var pos = Constants.client.my_player_scene.global_position
	
	buf.resize(1 + 6 + 6 + 6)
	buf.encode_u8(0, item.id) #byte
	buf.encode_half(1, position.x) #2bytes
	buf.encode_half(3, position.y) #2bytes
	buf.encode_half(5, position.z) #2bytes
	buf.encode_half(7, dir.x) #2bytes
	buf.encode_half(9, dir.y) #2bytes
	buf.encode_half(11, dir.z) #2bytes
	buf.encode_half(13, pos.x) #2bytes
	buf.encode_half(15, pos.y) #2bytes
	buf.encode_half(17, pos.z) #2bytes
	
	
	server_spawn_item.rpc_id(1, buf)


#Client
@rpc("reliable", "authority", "call_remote", Constants.CHANNEL_SPAWN_MANAGER)
func client_spawn_item(buf):
	var spawn_position = Vector3(buf.decode_half(1), buf.decode_half(3), buf.decode_half(5))
	var direction = Vector3(buf.decode_half(7), buf.decode_half(9), buf.decode_half(11))
	var id = buf.decode_u8(0)
	var item = InventoryManager.get_item_by_id(id) as ThrowableFireworkInventoryItem
	
	var scene = item.throw_body.instantiate()
	add_child(scene)
	scene.item = item
	scene.make_client()
	scene.global_position  = spawn_position
	scene.apply_central_impulse(direction)


#Server
@rpc("reliable", "any_peer", "call_remote", Constants.CHANNEL_SPAWN_MANAGER)
func server_spawn_item(buf: PackedByteArray):
	var id = buf.decode_u8(0)
	var item = InventoryManager.get_item_by_id(id) as ThrowableFireworkInventoryItem
	
	var dist = item.spawn_radius * item.spawn_radius
	var server = Constants.server
	var position = Vector3(buf.decode_half(13), buf.decode_half(15), buf.decode_half(17))
	
	
	var spawn_position = Vector3(buf.decode_half(1), buf.decode_half(3), buf.decode_half(5))
	var direction = Vector3(buf.decode_half(7), buf.decode_half(9), buf.decode_half(11))
	
	var scene = item.throw_body.instantiate()
	add_child(scene)
	scene.make_server(multiplayer.get_remote_sender_id())
	scene.global_position = spawn_position
	scene.apply_central_impulse(direction)
	scene.item = item
	
	var send_data = buf.duplicate()
	send_data.resize(6 + 6 + 1)
	for key in server.players_data.keys():
		if not server.is_bot(key):
			var pos = server.players[key].global_position as Vector3
			if (pos - position).length_squared() <= dist:
				client_spawn_item.rpc_id(key, send_data)
