[gd_scene load_steps=4 format=3 uid="uid://dxtnwmdp7gkd8"]

[ext_resource type="PackedScene" uid="uid://cy82bsxfj1ddv" path="res://prophaunt/scenes/PropObject.tscn" id="1_87rcj"]
[ext_resource type="PackedScene" uid="uid://m3cevewpsgy1" path="res://prophaunt/maps/Source/Furniture/lounge_design_sofa.tscn" id="2_qoc5w"]

[sub_resource type="BoxShape3D" id="BoxShape3D_f8ox4"]
size = Vector3(4.53665, 1.64198, 1.60167)

[node name="LoungeDesignSofaProp" instance=ExtResource("1_87rcj")]

[node name="LoungeDesignSofa" parent="Meshes" index="0" instance=ExtResource("2_qoc5w")]

[node name="CollisionShape3D" type="CollisionShape3D" parent="StaticBody3D" index="0"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -0.00968015, 0.820992, 0.045311)
shape = SubResource("BoxShape3D_f8ox4")
