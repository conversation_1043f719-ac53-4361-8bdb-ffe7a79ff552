[gd_scene load_steps=4 format=4 uid="uid://dhjupu2wjd04m"]

[ext_resource type="Material" uid="uid://b6gd8jjb1kopc" path="res://prophaunt/Mat/Colormap2.tres" id="1_44xqi"]

[sub_resource type="ArrayMesh" id="ArrayMesh_v75p6"]
_surfaces = [{
"aabb": AABB(-0.415387, -3.69903e-08, -0.489951, 0.830774, 1.71146, 0.936497),
"format": 34359742465,
"index_count": 204,
"index_data": PackedByteArray("AQACAAAAAgABAAMAAgADAAQABQADAAEAAwAFAAYAAwAGAAcABgAIAAcACAAGAAkACwAMAAoADAALAAkACQALAAgADQALAAoACwANAA4ACwAOAA8ABQARABAAEQAFAAwADAAFAAEADQACAA4ADwASABMADwAUAAsAFAAPABMACwAVAAgAFQALABQABwAVABYAFQAHAAgABwAXAAMAFwAHABYAGQAaABgAGgAZABsAGwAZABwAFwAdABIABgAQAB4ADAAfABEAHwAGAB4ABgAfAAkAEAAfAB4AIQAiACAABAAgAAIAAgAiAA4AIwAEAA8ADAABAAoACgABAA0ADQABAAAAAgANAAAAEgAPAAQAAwASAAQAEgADABcAGwAcAB0AFQAaABYAGgAVABgAGgAXABYAFwAaABsAHQAXABsAHQATABIAEwAdABwAFAAcABkAHAAUABMAFAAYABUAGAAUABkAEAAGAAUAHwAMAAkAHwAQABEAIgAhACMAIAAEACEAIgACACAAIgAPAA4ADwAiACMABAAjACEA"),
"name": "Material",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 36,
"vertex_data": PackedByteArray("mq1UvuxL6TJJL7g+oq3Uvvssoif//38zmq1Uvtqerj9GL7g+oq3UvpqCvD//jF2zmq1UvpqCvD9FL7g+nK1UvuhL6bJGL7i+nK1UvitXBT5GL7i+nK1UvpqCvD9KL7i+mq1UPpqCvD9NL7i+mq1UPitXBT5JL7i+oq3UPt99oqf//3+zoq3UPpqCvD8/Yze0mq1UPuxL6bJJL7i+nK1UPuhL6TJGL7g+nK1UPtqerj9DL7g+nK1UPpqCvD9CL7g+bXQ+vkDfHrPi2vq+ZXQ+PkDfHrPi2vq+mq1Uvllmyj9FL7g+nK1UPllmyj9CL7g+oq3UPllmyj8vL0C0mq1UPllmyj9NL7i+nK1Uvllmyj9KL7i+oq3Uvllmyj9eXoCzAp8QPkER2z/JfXq+BJ+QPkER2z+CvSq0B58QvkER2z/GfXq+BJ+QvkER2z8Ee9WzB58QPkER2z+0fXo+Ap8QvkER2z+3fXo+bXQ+vvA73j3i2vq+ZXQ+PvA73j3i2vq+Nec2vsKXrz+qoeQ+Nec2vrKJuz+poeQ+Pec2PsKXrz+qoeQ+Pec2PrKJuz+poeQ+")
}]
blend_shape_mode = 0

[sub_resource type="ArrayMesh" id="ArrayMesh_cg8pt"]
resource_name = "Trashcan_trashcan"
_surfaces = [{
"aabb": AABB(-0.415387, -3.69903e-08, -0.489951, 0.830774, 1.71146, 0.936497),
"attribute_data": PackedByteArray("nICUPhJMaj9ZRpU+w1J2P5yAlD4STGo/WUaVPsNSdj+cgJQ+EkxqP5yAlD4STGo/nICUPhJMaj9ZRpU+w1J2P5yAlD4STGo/WUaVPsNSdj+cgJQ+EkxqP1lGlT7DUnY/nICUPhJMaj9ZRpU+w1J2P1lGlT7DUnY/WUaVPsNSdj+cgJQ+EkxqP1lGlT7DUnY/WUaVPsNSdj9ZRpU+w1J2P5yAlD4STGo/WUaVPsNSdj+cgJQ+EkxqP1lGlT7DUnY/nICUPhJMaj9ZRpU+w1J2P5yAlD4STGo/WUaVPsNSdj+cgJQ+EkxqP1lGlT7DUnY/nICUPhJMaj9ZRpU+w1J2P5yAlD4STGo/WUaVPsNSdj+cgJQ+EkxqP1lGlT7DUnY/nICUPhJMaj9ZRpU+w1J2P1lGlT7DUnY/nICUPhJMaj+cgJQ+EkxqP5yAlD4STGo/WUaVPsNSdj+cgJQ+EkxqP1lGlT7DUnY/WUaVPsNSdj+cgJQ+EkxqP1lGlT7DUnY/nICUPhJMaj9ZRpU+w1J2P5yAlD4STGo/nICUPhJMaj9ZRpU+w1J2P5yAlD4STGo/WUaVPsNSdj9ZRpU+w1J2P1lGlT7DUnY/nICUPhJMaj9ZRpU+w1J2P1lGlT7DUnY/WUaVPsNSdj9ZRpU+w1J2P1lGlT7DUnY/WUaVPsNSdj9ZRpU+w1J2P1lGlT7DUnY/WUaVPsNSdj9ZRpU+w1J2P1lGlT7DUnY/WUaVPsNSdj9ZRpU+w1J2P1lGlT7DUnY/WUaVPsNSdj9ZRpU+w1J2P1lGlT7DUnY/WUaVPsNSdj9ZRpU+w1J2P1lGlT7DUnY/WUaVPsNSdj9ZRpU+w1J2P1lGlT7DUnY/WUaVPsNSdj9YRpU+w1J2P1hGlT7DUnY/WEaVPsNSdj9YRpU+w1J2P1lGlT7DUnY/WUaVPsNSdj9ZRpU+w1J2P1lGlT7DUnY/WUaVPsNSdj9ZRpU+w1J2P1lGlT7DUnY/WUaVPsNSdj9ZRpU+w1J2P1lGlT7DUnY/WkaVPsNSdj9aRpU+w1J2P1pGlT7DUnY/WEaVPsNSdj9YRpU+w1J2P1hGlT7DUnY/WUaVPsNSdj9ZRpU+w1J2P1lGlT7DUnY/WUaVPsNSdj9ZRpU+w1J2P1lGlT7DUnY/WUaVPsNSdj9ZRpU+w1J2P1lGlT7DUnY/WUaVPsNSdj9ZRpU+w1J2P1lGlT7DUnY/WUaVPsNSdj9ZRpU+w1J2P1lGlT7DUnY/WUaVPsNSdj9ZRpU+w1J2P1lGlT7DUnY/WUaVPsNSdj9ZRpU+w1J2P1lGlT7DUnY/WUaVPsNSdj9ZRpU+w1J2P1lGlT7DUnY/"),
"format": 34359742487,
"index_count": 204,
"index_data": PackedByteArray("BAAIAAIACAAEAAoACAAKABAAFAAMAAUADAAUABgADAAYABwAFgAeABoAHgAWACIAKwAuACgALgArACQAJAArACAAMgApACcAKQAyADUAKQA1ADkAEwBAAD0AQAATAC0ALQATAAMAMAAGADMANwBCAEYAOgBMACoATAA6AEkALABRACEAUQAsAE0AGwBOAFIATgAbAB8AHQBZAA0AWQAdAFUAXQBhAFsAYQBdAGMAYwBdAGcAVgBrAEQAGQA+AG4ALwBxAEEAcAAXAG0AFwBwACMAPABvAGwAdQB4AHIAEgB0AAkABwB5ADQAfAAPADgALQADACYAJgADADEAMQADAAEABgAwAAAAQgA3AA4ACwBFABEARQALAFgAYwBnAGoATwBgAFMAYABPAFoAYgBXAFQAVwBiAGUAawBWAGQAaQBHAEMARwBpAGYASgBoAF4AaABKAEgASwBcAFAAXABLAF8APgAZABUAcQAvACUAbwA8AD8AeAB1AHsAdAASAHcAeQAHAHMAegA7ADYAOwB6AH0ADwB8AHYA"),
"material": ExtResource("1_44xqi"),
"name": "Material",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 126,
"vertex_data": PackedByteArray("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")
}]
blend_shape_mode = 0
shadow_mesh = SubResource("ArrayMesh_v75p6")

[node name="trashcan" type="MeshInstance3D"]
mesh = SubResource("ArrayMesh_cg8pt")
skeleton = NodePath("")
