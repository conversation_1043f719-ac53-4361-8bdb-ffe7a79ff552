extends Node
class_name ProphauntPlayer

# Prophaunt-specific player functionality
# This script extends the base character functionality for Prophaunt mode

var character: Character
var team: Constants.ProphauntTeam
var player_state: Constants.ProphauntPlayerState = Constants.ProphauntPlayerState.ALIVE
var hex_cooldown: float = 0.0
var sound_cooldown: float = 0.0

# Health system
var health_system: ProphauntHealthSystem

# Haunter-specific variables
var weapon_system: ProphauntWeaponSystem

# Prop-specific variables
var disguise_system: ProphauntDisguiseSystem

signal team_assigned(team: Constants.ProphauntTeam)
signal hp_changed(new_hp: int)
signal state_changed(new_state: Constants.ProphauntPlayerState)
signal disguise_changed(new_disguise: String)

func _ready():
	# This will be called when attached to a character
	pass


func initialize(player_character: Character, assigned_team: Constants.ProphauntTeam):
	"""Initialize the Prophaunt player with character and team"""
	character = player_character
	team = assigned_team
	
	# Set initial values
	player_state = Constants.ProphauntPlayerState.ALIVE
	hex_cooldown = 0.0
	if team == Constants.ProphauntTeam.PROPS:
		sound_cooldown = Constants.PROPHAUNT_PROP_SOUND_COOLDOWN  
	else:
		sound_cooldown = 0.0

	# Initialize health system
	health_system = ProphauntHealthSystem.new()
	health_system.name = "HealthSystem"
	add_child(health_system)
	health_system.initialize(character, self)

	# Connect health system signals
	health_system.health_changed.connect(_on_health_changed)
	health_system.damage_taken.connect(_on_damage_taken)
	health_system.player_died.connect(_on_player_died)
	
	team_assigned.emit(team)
	
	# Set up team-specific functionality
	setup_team_abilities()


func setup_team_abilities():
	"""Set up abilities based on team"""
	if team == Constants.ProphauntTeam.HAUNTERS:
		setup_haunter_abilities()
	else:
		setup_prop_abilities()


func setup_haunter_abilities():
	"""Set up haunter-specific abilities"""
	# Create weapon system
	weapon_system = ProphauntWeaponSystem.new()
	weapon_system.name = "WeaponSystem"
	add_child(weapon_system)
	weapon_system.initialize(character, self)


func setup_prop_abilities():
	"""Set up prop-specific abilities"""
	# Create disguise system
	disguise_system = ProphauntDisguiseSystem.new()
	disguise_system.name = "DisguiseSystem"
	add_child(disguise_system)
	disguise_system.initialize(character, self)

	#if Constants.is_server:
		## Set initial random disguise
		#var initial_disguise = disguise_system.get_random_disguise()
		#disguise_system.apply_disguise(initial_disguise)
		#character.global_position = initial_disguise.global_position
		#character.global_rotation = initial_disguise.global_rotation
		#print(initial_disguise, " ", initial_disguise.global_position)


func _process(delta):
	"""Update cooldowns and effects"""
	update_cooldowns(delta)
	update_effects(delta)


func update_cooldowns(delta):
	"""Update various cooldowns"""
	if hex_cooldown > 0:
		hex_cooldown -= delta
	
	if sound_cooldown > 0:
		sound_cooldown -= delta
	elif team == Constants.ProphauntTeam.PROPS and player_state == Constants.ProphauntPlayerState.ALIVE:
		# Time to make detection sound
		play_detection_sound()
		sound_cooldown = Constants.PROPHAUNT_PROP_SOUND_COOLDOWN


func update_effects(_delta):
	"""Update ongoing effects like hex"""
	if player_state == Constants.ProphauntPlayerState.HEXED:
		# Hexed players can't move or shoot
		if character:
			character.velocity = Vector3.ZERO
			# Disable input processing temporarily


func take_damage(damage: int, source: String = "unknown"):
	"""Take damage (props only)"""
	if team != Constants.ProphauntTeam.PROPS:
		return

	if health_system:
		health_system.take_damage(damage, source)


func die():
	"""Handle player death"""
	player_state = Constants.ProphauntPlayerState.DEAD
	state_changed.emit(player_state)

	if character:
		character.disable()  # Disable the character


func apply_hex(duration: float):
	"""Apply hex effect"""
	if team != Constants.ProphauntTeam.HAUNTERS:
		return
	
	player_state = Constants.ProphauntPlayerState.HEXED
	state_changed.emit(player_state)
	
	# Set up timer to remove hex
	get_tree().create_timer(duration).timeout.connect(_on_hex_expired)


func _on_hex_expired():
	"""Called when hex effect expires"""
	if player_state == Constants.ProphauntPlayerState.HEXED:
		player_state = Constants.ProphauntPlayerState.ALIVE
		state_changed.emit(player_state)


# Haunter abilities
func shoot_at_target(target_position: Vector3):
	"""Haunter shoots at target"""
	if team != Constants.ProphauntTeam.HAUNTERS:
		return false

	if not weapon_system:
		return false

	return weapon_system.shoot(target_position)


func throw_grenade(target_position: Vector3):
	"""Haunter throws grenade"""
	if team != Constants.ProphauntTeam.HAUNTERS:
		return false

	if not weapon_system:
		return false

	return weapon_system.throw_grenade(target_position)


# Prop abilities
func change_disguise(new_disguise: PropObject):
	"""Prop changes disguise"""
	if team != Constants.ProphauntTeam.PROPS:
		return false

	if not disguise_system:
		return false

	return disguise_system.apply_disguise(new_disguise)


func cast_hex():
	"""Prop casts hex on nearby haunters"""
	if team != Constants.ProphauntTeam.PROPS:
		return false

	if not disguise_system:
		return false

	return disguise_system.cast_hex_ability()


func apply_disguise(disguise: PropObject):
	"""Apply visual disguise to the character"""
	if disguise_system:
		disguise_system.apply_disguise(disguise)


func apply_random_disguise():
	if disguise_system:
		disguise_system.apply_disguise(disguise_system.get_random_disguise())


func play_detection_sound():
	"""Play 3D sound to help haunters locate this prop"""
	if team != Constants.ProphauntTeam.PROPS:
		return
	
	if player_state != Constants.ProphauntPlayerState.ALIVE:
		return
	
	if character:
		var position = character.global_position
		SoundManager.play_3d_sound.rpc_id(1, position, 15, SoundManager.SOUND_TYPE.Checkpoint)


func _on_health_changed(new_hp: int, max_hp: int):
	"""Called when health changes"""
	hp_changed.emit(new_hp)
	print("Health changed: ", new_hp, "/", max_hp)


func _on_damage_taken(damage: int, source: String):
	"""Called when damage is taken"""
	print("Took ", damage, " damage from ", source)


func _on_player_died():
	"""Called when player dies"""
	die()
	print("Player died")


# Health utility functions
func get_current_hp() -> int:
	"""Get current HP"""
	if health_system:
		return health_system.get_current_hp()
	return 0


func get_max_hp() -> int:
	"""Get maximum HP"""
	if health_system:
		return health_system.get_max_hp()
	return Constants.PROPHAUNT_PROP_DEFAULT_HP


func get_health_percentage() -> float:
	"""Get health as percentage"""
	if health_system:
		return health_system.get_health_percentage()
	return 0.0


func is_alive() -> bool:
	"""Check if player is alive"""
	if health_system:
		return health_system.is_alive()
	return player_state == Constants.ProphauntPlayerState.ALIVE


func heal(amount: int) -> bool:
	"""Heal the player"""
	if health_system:
		return health_system.heal(amount)
	return false


func reset_health():
	"""Reset health for new round"""
	if health_system:
		health_system.reset_health()
	player_state = Constants.ProphauntPlayerState.ALIVE
