[gd_scene load_steps=5 format=4 uid="uid://c6k8sndevbvr5"]

[ext_resource type="Material" uid="uid://dkmcjc7wc3ws6" path="res://prophaunt/Mat/GrassPath.tres" id="1_feq64"]

[sub_resource type="ArrayMesh" id="ArrayMesh_v2h1v"]
_surfaces = [{
"aabb": AABB(-1, -0.1, -1, 2, 0.1, 2),
"format": 34896613377,
"index_count": 186,
"index_data": PackedByteArray("AgAAAAEAAQADAAIABAACAAMAAwAFAAQABgAEAAUABQAHAAYAAQAAAAgACAAJAAEADAAKAAsACwANAAwAEAAOAA8ADwARABAACQAIABIAEgATAAkACgAUABUAFQALAAoAFgAMAA0ADQAXABYAFQAUABAAEAARABUAGgAYABkAGQAbABoAGQAcABsAGQAdABwAHQAeABwAHgAfABwAIgAgACEAIQAjACIAIAAkACEAIQAlACMAJAAgACYAJgAnACQABwAUAAoACgAWAAcACgAMABYADgAUAAcAEwAOAAcACQATAAcACQAHAAUACQAFAAMAAwABAAkADgAQABQAFwAYABoAGgAcABcAGgAbABwADwAYABcAEQAPABcAEQAXAA0AEQANAAsACwAVABEADwAdABgAHQAZABgAIwAAAAIAAgAGACMAAgAEAAYAJgAAACMAJwAmACMAJwAjACUAJwAlACEAIQAkACcAJgASAAAAEgAIAAAA"),
"lods": [0.0681117, PackedByteArray("BgAjAAEAAQAFAAYABQAHAAYAAQAjABIAEgAJAAEAEgATAAkAJgASACMAJQAjACIAIgAgACUAJwAgACYAIAAnACUAFAAOAA8ADwARABQAEQANABQAFgAUAA0ADQAXABYADwAdABcAHQAZABcAGQAbABcAFwAbABwAGQAcABsAHQAeABwAHgAfABwAGQAdABwAFAAWAAcADgAUAAcAEwAOAAcACQATAAcACQAHAAUACQAFAAEAEQAPABcAEQAXAA0AJwAmACMAJwAjACUA")],
"name": "Material",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 40,
"vertex_data": PackedByteArray("Stf+//+fAABrxwAA/58AAErX/v//XwAAa8cAAP9fAABrx/7//x8AAIy3AAD/HwAAa8f+/wAAAACMtwAAAAAAAGvH/v//3wAAjLcAAP/fAABRWAAA/18AAHJI/v//XwAAckgAAP8fAACTOP7//x8AAHJIAAD//wAAkzj/////AABySAAA/98AAJM4/v//3wAAa8f/////AACMtwAA//8AAFFYAAD/nwAAckj+//+fAABySAAAAAAAAJM4/v8AAAAA2C7+//+fAAD5Hv7//98AANgu/v//XwAA+R7+//8fAAD5Hv7/AAAAAPke/////wAAAAD/////AAAAAP7/AAAAAP///////wAA5PD+//9fAAD///7/AAAAAAXh/v8AAAAA5PD+//+fAAAF4f7//x8AAAXh/////wAABeH+///fAAA=")
}]
blend_shape_mode = 0

[sub_resource type="ArrayMesh" id="ArrayMesh_qtg7e"]
resource_name = "GroundPathStraight_GroundPathStraight"
_surfaces = [{
"aabb": AABB(-1, -0.1, -1, 2, 0.1, 2),
"attribute_data": PackedByteArray("XJaNsFyWjbBclo2wqAFBsEigwbBIoMGwY5YI0GOWCNBjlgjQpQFaz2Kgxs9ioMbP+p0H7/qdB+/6nQfvWQl07sin1O7Ip9TuWQl07vWdY/71nWP+nQkA/p0JAP7Zp1n+nQkA/s2dq5HNnauRzZ2rkWAJKZFgCSmRkKfWkZCn1pFgCSmR4zdbz06C889OgvPPLoy+zy6Mvs8ujL7PmT9z7r2JHO+9iRzvspPG7rKTxu6yk8buspPG7pk/nYFBik+CIZSKgiGUioIhlIqCmT8pkeWJ0JHlidCR3pMikt6TIpLekyKS3pMikt6TIpK5nVyCuZ1cgp0JnYGdCZ2BlKdfguM3QrBgguawYILmsC+MKLEvjCixL4wosZk/AP4gioX+75NO/u+TTv7vk07+75NO/rqY6rCGoEiShqBIkoagSJKsmBHQf6CG7n+ghu7DoDv+w6A7/sOgO/7DoJ2Cw6CdgrivnYK4rzv+ToJPgomJE9BOgoX+wpFk/sKRZP7CkWT+konWsHiRre54ka3ulZFkgpWRZIJdkRiSXZEYkl2RGJL1nWP+nQkA/mAJKZFgCSmRspPG7iGUioIhlIqC3pMikrmdXIKdCZ2Bw6CdgsKRZP6VkWSCXZEYkg=="),
"format": 34896613399,
"index_count": 186,
"index_data": PackedByteArray("CAACAAUABQALAAgADQAHAAoACgAQAA0AFAAOABEAEQAXABQABAABABoAGgAeAAQAKAAiACUAJQArACgANQAvADEAMQA4ADUAHwAbADwAPAA/AB8AIwBCAEUARQAmACMARwApACwALABJAEcARABBADQANAA3AEQAUABMAE0ATQBRAFAATwBVAFIATgBXAFQAVgBYAFMAWABZAFMAXABaAFsAWwBdAFwAWgBgAFsAWwBhAF0AYABaAGMAYwBlAGAAFQBAACEAIQBGABUAIQAnAEYALgBAABUAPQAuABUAHQA+ABYAIAAYABIAHAAPAAkACQADABwALgAzAEAASABMAFAAUABTAEgAUABRAFMAMABMAEgAOgAyAEsAOQBKAC0ANgAqACQAJABDADYAMABWAEwAVgBNAEwAXQAAAAYABgATAF0ABgAMABMAYwAAAF0AZwBkAF8AZgBeAGIAZQBhAFsAWwBgAGUAYwA7AAAAOwAZAAAA"),
"lods": [0.0681117, PackedByteArray("aABdAAQABAAQAGgAEAAXAGgABABdAHAAcAAeAAQAcAA/AB4AYwBwAF0AYQBdAFwAXABaAGEAZQBaAGMAWgBlAGEAQQAvAG0AbQBvAEEAbwBsAEEARwBBAGwAbABIAEcAbQBWAEgAVgBNAEgATQBRAEgASABRAFMATQBTAFIAVgBYAFMAWABZAFMATgByAFQAQABGABUALgBAABUAPQAuABUAagBxABYAawBpABIAHAAPAAMAOgBuAEoAOQBKAC0AdQB0AHMAZgBeAGIA")],
"material": ExtResource("1_feq64"),
"name": "Material",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 118,
"vertex_data": PackedByteArray("Stf+//+fPP5K1/7//5+u7UrX/v//nxLsa8cAAP+f//9rxwAA/5+y7WvHAAD/n1PsStf+//9fWPtK1/7//1/260rX/v//Xxzta8cAAP9f//9rxwAA/1/u62vHAAD/X13ta8f+//8f+v5rx/7//x/P62vH/v//HzPtjLcAAP8f//+MtwAA/x/H64y3AAD/HwntjLcAAP8fAABrx/7/AADk/2vH/v8AAOnsjLcAAAAA//+MtwAAAADIzIy3AAAAAL/sjLcAAAAAVNVrx/7//9/7/mvH/v//38Lta8f+///fKOyMtwAA/9///4y3AAD/3z8JjLcAAP/fxu2MtwAA/99S7Iy3AAD/31TVUVgAAP9f//9RWAAA/1+s7VFYAAD/X0zsckj+//9fhP9ySP7//1+d7XJI/v//XwrsckgAAP8f//9ySAAA/x9m7XJIAAD/H67rkzj+//8fbP+TOP7//x9W7ZM4/v//H83rkzj+//8f//9ySAAA/////3JIAAD//0ftkzj/////2f+TOP////9g7ZM4/////xnfckgAAP/f//9ySAAA/98f7HJIAAD/33Ltkzj+///fef+TOP7//98p7JM4/v//34vtkzj+///faMuTOP7//98VSmvH/////9L/a8f/////cuyMtwAA/////4y3AAD//xnfjLcAAP//m+xRWAAA/5///1FYAAD/n/TrUVgAAP+fW+1ySP7//59Y/nJI/v//n/3rckj+//+fGe1ySAAAAAD//3JIAAAAAObrkzj+/wAA5v+TOP7/AAAG7JM4/v8AAGjLkzj+/wAAyMzYLv7//58i//ke/v//34n9+R7+///fFUr5Hv7//99nx9gu/v//Xwr/+R7+//8frvz5Hv7//x/jcvke/v8AAHn/+R7+/wAAyMz5Hv7/AABnx/ke/////5H/+R7/////Gd8AAP///////wAA/v8AAP//////////3v/k8P7//1/F/////v8AANH/BeH+/wAA8v8F4f7/AAAKwQXh/v8AAMjM5PD+//+fuP8F4f7//x8c/QXh/v//H///BeH/////y/8F4f////8Z3wXh/v//37n9BeH+///fCsEF4f7//98VSmvH/v8AAP//jLcAAAAAVNWMtwAA/99cDoy3AAD/31TVkzj+//8f//+TOP///////5M4/////0XOkzj+///f//9rx/////+f/Yy3AAD////V+R7/////5tAF4f7/AABdzQXh/////6TQBeH+///fBkT1wOwBDmxnukNt0rb/f/+/Cmxjug1tobb5hIK9gW1DsmdsCbb/f/+/h21IsjFs2LUdgXC/nG1cslNs97X/f/+/om1hsnZsF7b///+/HYDwv5BsL7b/f/+//38AALNsT7b/f///jcAdAftrVbowbcG2AoD9v/azcof3a1G6DW2htv9/////f/+/aTruE6Y27RKGgLu/dzrgE9k2tRL/f/+/qDqpEyA3aBKhgK6/tjqbEwc3ghLjsP9//3//v+k1uxMpgOq/1zXPE/9/AAD/f/+/KTKbEsk13xNJwJMAIzKhErY18hP/fwAA9J/GDRfAMADzbIm2/3//v/9/AADQbGq2/3//v0UyfBLaNcwT5sDPAT8yghILNpYT/3//v/U2lxIMwBsA3DayEv9/AAD/fwAA84CFv6yCqL70n8YNaeOUnAyBeL/JwZUD/3/+/0nAkwD/fwAAZuOYnHmAwr//fwAA/3//v/9//78jgO2/H8BAABjAMgANgPi//38AAP9/AABNgNi/j8EhA96R/38bwDkA/38AAHmCwr7/fwAA9J/GDTlna+b///9/rKotmv///3/sZBMfhmi4GFOUUxSIZKsXIWRb5eDbHqQ2kTYRepl7GWT2mYnHwuQi")
}]
blend_shape_mode = 0
shadow_mesh = SubResource("ArrayMesh_v2h1v")

[sub_resource type="ConcavePolygonShape3D" id="ConcavePolygonShape3D_xd8yy"]
data = PackedVector3Array(0.682, 0, -0.25, 0.682, 0, 0.25, 0.558, -0.1, 0.25, 0.558, -0.1, 0.25, 0.558, -0.1, -0.25, 0.682, 0, -0.25, 0.558, 0, -0.75, 0.682, 0, -0.25, 0.558, -0.1, -0.25, 0.558, -0.1, -0.25, 0.434, -0.1, -0.75, 0.558, 0, -0.75, 0.558, 0, -1, 0.558, 0, -0.75, 0.434, -0.1, -0.75, 0.434, -0.1, -0.75, 0.434, -0.1, -1, 0.558, 0, -1, 0.558, -0.1, 0.25, 0.682, 0, 0.25, 0.558, 0, 0.75, 0.558, 0, 0.75, 0.434, -0.1, 0.75, 0.558, -0.1, 0.25, -0.434, -0.1, -0.75, -0.31, -0.1, -0.25, -0.434, 0, -0.25, -0.434, 0, -0.25, -0.558, 0, -0.75, -0.434, -0.1, -0.75, -0.434, -0.1, 0.75, -0.434, -0.1, 1, -0.558, 0, 1, -0.558, 0, 1, -0.558, 0, 0.75, -0.434, -0.1, 0.75, 0.434, -0.1, 0.75, 0.558, 0, 0.75, 0.558, 0, 1, 0.558, 0, 1, 0.434, -0.1, 1, 0.434, -0.1, 0.75, -0.31, -0.1, -0.25, -0.31, -0.1, 0.25, -0.434, 0, 0.25, -0.434, 0, 0.25, -0.434, 0, -0.25, -0.31, -0.1, -0.25, -0.434, -0.1, -1, -0.434, -0.1, -0.75, -0.558, 0, -0.75, -0.558, 0, -0.75, -0.558, 0, -1, -0.434, -0.1, -1, -0.434, 0, 0.25, -0.31, -0.1, 0.25, -0.434, -0.1, 0.75, -0.434, -0.1, 0.75, -0.558, 0, 0.75, -0.434, 0, 0.25, -0.634, 0, -0.25, -0.634, 0, 0.25, -0.758, 0, 0.75, -0.758, 0, 0.75, -0.758, 0, -0.75, -0.634, 0, -0.25, -0.758, 0, 0.75, -0.758, 0, -1, -0.758, 0, -0.75, -0.758, 0, 0.75, -0.758, 0, 1, -0.758, 0, -1, -0.758, 0, 1, -1, 0, 1, -0.758, 0, -1, -1, 0, 1, -1, 0, -1, -0.758, 0, -1, 1, 0, -1, 1, 0, 1, 0.882, 0, -0.25, 0.882, 0, -0.25, 0.758, 0, -1, 1, 0, -1, 1, 0, 1, 0.882, 0, 0.25, 0.882, 0, -0.25, 0.882, 0, -0.25, 0.758, 0, -0.75, 0.758, 0, -1, 0.882, 0, 0.25, 1, 0, 1, 0.758, 0, 1, 0.758, 0, 1, 0.758, 0, 0.75, 0.882, 0, 0.25, 0.434, -0.1, -1, -0.31, -0.1, 0.25, -0.31, -0.1, -0.25, -0.31, -0.1, -0.25, -0.434, -0.1, -1, 0.434, -0.1, -1, -0.31, -0.1, -0.25, -0.434, -0.1, -0.75, -0.434, -0.1, -1, -0.434, -0.1, 1, -0.31, -0.1, 0.25, 0.434, -0.1, -1, 0.434, -0.1, 1, -0.434, -0.1, 1, 0.434, -0.1, -1, 0.434, -0.1, 0.75, 0.434, -0.1, 1, 0.434, -0.1, -1, 0.434, -0.1, 0.75, 0.434, -0.1, -1, 0.434, -0.1, -0.75, 0.434, -0.1, 0.75, 0.434, -0.1, -0.75, 0.558, -0.1, -0.25, 0.558, -0.1, -0.25, 0.558, -0.1, 0.25, 0.434, -0.1, 0.75, -0.434, -0.1, 1, -0.434, -0.1, 0.75, -0.31, -0.1, 0.25, -0.558, 0, -1, -0.634, 0, 0.25, -0.634, 0, -0.25, -0.634, 0, -0.25, -0.758, 0, -1, -0.558, 0, -1, -0.634, 0, -0.25, -0.758, 0, -0.75, -0.758, 0, -1, -0.558, 0, 1, -0.634, 0, 0.25, -0.558, 0, -1, -0.558, 0, 0.75, -0.558, 0, 1, -0.558, 0, -1, -0.558, 0, 0.75, -0.558, 0, -1, -0.558, 0, -0.75, -0.558, 0, 0.75, -0.558, 0, -0.75, -0.434, 0, -0.25, -0.434, 0, -0.25, -0.434, 0, 0.25, -0.558, 0, 0.75, -0.558, 0, 1, -0.758, 0, 1, -0.634, 0, 0.25, -0.758, 0, 1, -0.758, 0, 0.75, -0.634, 0, 0.25, 0.758, 0, -1, 0.682, 0, 0.25, 0.682, 0, -0.25, 0.682, 0, -0.25, 0.558, 0, -1, 0.758, 0, -1, 0.682, 0, -0.25, 0.558, 0, -0.75, 0.558, 0, -1, 0.758, 0, 1, 0.682, 0, 0.25, 0.758, 0, -1, 0.758, 0, 0.75, 0.758, 0, 1, 0.758, 0, -1, 0.758, 0, 0.75, 0.758, 0, -1, 0.758, 0, -0.75, 0.758, 0, 0.75, 0.758, 0, -0.75, 0.882, 0, -0.25, 0.882, 0, -0.25, 0.882, 0, 0.25, 0.758, 0, 0.75, 0.758, 0, 1, 0.558, 0, 1, 0.682, 0, 0.25, 0.558, 0, 1, 0.558, 0, 0.75, 0.682, 0, 0.25)

[node name="GroundPathStraight" type="MeshInstance3D"]
mesh = SubResource("ArrayMesh_qtg7e")
skeleton = NodePath("")

[node name="StaticBody3D" type="StaticBody3D" parent="."]

[node name="CollisionShape3D" type="CollisionShape3D" parent="StaticBody3D"]
shape = SubResource("ConcavePolygonShape3D_xd8yy")
