[gd_scene load_steps=16 format=3 uid="uid://ct4wbxojo0qnj"]

[ext_resource type="PackedScene" uid="uid://cmdaistopeoan" path="res://Scenes/FreeRide/Assets/Actionables/BaseActionable.tscn" id="1_hxsg2"]
[ext_resource type="Script" path="res://Scenes/FreeRide/Assets/Actionables/Shops/TeleportToCenterShop.gd" id="2_mcv15"]
[ext_resource type="Texture2D" uid="uid://r7n4eqwqtiue" path="res://Scenes/ui/assets/gradiant_bg.png" id="3_rdnjt"]
[ext_resource type="StyleBox" uid="uid://3jmm6a0rtg6b" path="res://Scenes/ui/panel_background.tres" id="4_mqpm0"]
[ext_resource type="FontFile" uid="uid://djnxbn02v2032" path="res://Scenes/ui/assets/Font/Lalezar-Regular.ttf" id="4_nowc2"]
[ext_resource type="PackedScene" uid="uid://jqbc5awj1g5t" path="res://Scenes/ui/CustomButton.tscn" id="5_3wbme"]
[ext_resource type="Texture2D" uid="uid://bw3w61kw7wpki" path="res://Scenes/ui/assets/PNG/grey_crossWhite.png" id="6_a33ud"]
[ext_resource type="Texture2D" uid="uid://cw0ou8f3ia7b4" path="res://Scenes/ui/assets/gray.png" id="7_olp1f"]
[ext_resource type="Texture2D" uid="uid://8q8ki5ld0acy" path="res://Scenes/ui/assets/Coin.png" id="9_e8a5t"]
[ext_resource type="Texture2D" uid="uid://0pg6iqc4n45o" path="res://Scenes/ui/assets/yellow.png" id="9_pybeu"]
[ext_resource type="Texture2D" uid="uid://cdi8kfb3vhhfs" path="res://Scenes/ui/assets/button-back.png" id="11_73c15"]
[ext_resource type="PackedScene" uid="uid://dfw3vd3t4jtva" path="res://Scenes/ui/coin_show.tscn" id="12_ttlin"]
[ext_resource type="PackedScene" uid="uid://c54taf607j3sh" path="res://Scenes/FreeRide/Assets/Actionables/Misc/Teleport/Teleport.tscn" id="13_5nx5f"]

[sub_resource type="BoxShape3D" id="BoxShape3D_nkip3"]
size = Vector3(4, 3, 4)

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_sx3pf"]

[node name="TeleportToCenterShop" instance=ExtResource("1_hxsg2")]
script = ExtResource("2_mcv15")

[node name="CollisionShape3D" parent="." index="0"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 1.49322, 0)
shape = SubResource("BoxShape3D_nkip3")

[node name="Panel" type="Panel" parent="." index="1"]
layout_direction = 2
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 2.0
grow_horizontal = 2
grow_vertical = 2
mouse_filter = 2
theme_override_styles/panel = SubResource("StyleBoxEmpty_sx3pf")

[node name="BG" type="TextureRect" parent="Panel" index="0"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
mouse_filter = 2
texture = ExtResource("3_rdnjt")
expand_mode = 1
stretch_mode = 3
metadata/_edit_lock_ = true

[node name="BGPanel" type="Panel" parent="Panel" index="1"]
custom_minimum_size = Vector2(800, 511)
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -400.0
offset_top = -255.5
offset_right = 400.0
offset_bottom = 255.5
grow_horizontal = 2
grow_vertical = 2
theme_override_styles/panel = ExtResource("4_mqpm0")

[node name="Title" type="Label" parent="Panel/BGPanel" index="0"]
layout_mode = 1
offset_left = 21.0
offset_top = 2.0
offset_right = 779.0
offset_bottom = 53.0
theme_override_colors/font_shadow_color = Color(0, 0, 0, 1)
theme_override_colors/font_outline_color = Color(0, 0, 0, 1)
theme_override_constants/shadow_offset_y = 2
theme_override_constants/outline_size = 3
theme_override_constants/shadow_outline_size = 5
theme_override_fonts/font = ExtResource("4_nowc2")
theme_override_font_sizes/font_size = 30
text = "TELEPORT_TITLE"
horizontal_alignment = 1
vertical_alignment = 1

[node name="ExitButton" parent="Panel/BGPanel" index="1" instance=ExtResource("5_3wbme")]
visible = false
layout_mode = 1
anchors_preset = 1
anchor_left = 1.0
anchor_bottom = 0.0
offset_left = -59.0
offset_top = 18.0
offset_right = -34.0
offset_bottom = 47.0
grow_horizontal = 0
grow_vertical = 1
pivot_offset = Vector2(12.5, 14.5)

[node name="TextureRect" type="TextureRect" parent="Panel/BGPanel/ExitButton" index="1"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
mouse_filter = 2
texture = ExtResource("6_a33ud")
expand_mode = 1
stretch_mode = 4

[node name="Text" type="Label" parent="Panel/BGPanel" index="2"]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -368.0
offset_top = -168.5
offset_right = 376.0
offset_bottom = 114.5
grow_horizontal = 2
grow_vertical = 2
theme_override_colors/font_shadow_color = Color(0, 0, 0, 1)
theme_override_colors/font_outline_color = Color(0, 0, 0, 1)
theme_override_constants/shadow_offset_y = 2
theme_override_constants/outline_size = 3
theme_override_constants/shadow_outline_size = 5
theme_override_fonts/font = ExtResource("4_nowc2")
theme_override_font_sizes/font_size = 30
text = "TELEPORT_TEXT"
horizontal_alignment = 1
vertical_alignment = 1

[node name="ResumeButton" parent="Panel/BGPanel" index="3" instance=ExtResource("5_3wbme")]
custom_minimum_size = Vector2(200, 80)
layout_mode = 1
anchors_preset = 7
anchor_left = 0.5
anchor_top = 1.0
anchor_right = 0.5
offset_left = 60.0
offset_top = -104.0
offset_right = 260.0
offset_bottom = -24.0
grow_vertical = 0
pivot_offset = Vector2(100, 40)
mouse_filter = 0

[node name="NinePatchRect" type="NinePatchRect" parent="Panel/BGPanel/ResumeButton" index="1"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
texture = ExtResource("7_olp1f")
patch_margin_left = 16
patch_margin_top = 18
patch_margin_right = 23
patch_margin_bottom = 18

[node name="Label" type="Label" parent="Panel/BGPanel/ResumeButton" index="2"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
theme_override_colors/font_shadow_color = Color(0, 0, 0, 1)
theme_override_colors/font_outline_color = Color(0, 0, 0, 1)
theme_override_constants/outline_size = 5
theme_override_constants/shadow_outline_size = 5
theme_override_fonts/font = ExtResource("4_nowc2")
theme_override_font_sizes/font_size = 30
text = "CANCEL"
horizontal_alignment = 1
vertical_alignment = 1

[node name="ConfirmButton" parent="Panel/BGPanel" index="4" instance=ExtResource("5_3wbme")]
custom_minimum_size = Vector2(200, 80)
layout_mode = 1
anchors_preset = 7
anchor_left = 0.5
anchor_top = 1.0
anchor_right = 0.5
offset_left = -260.0
offset_top = -105.0
offset_right = -60.0
offset_bottom = -25.0
grow_vertical = 0
pivot_offset = Vector2(100, 40)
mouse_filter = 0

[node name="TextureRect" type="TextureRect" parent="Panel/BGPanel/ConfirmButton" index="1"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
mouse_filter = 2
texture = ExtResource("9_pybeu")
expand_mode = 1
metadata/_edit_lock_ = true

[node name="Label" type="Label" parent="Panel/BGPanel/ConfirmButton" index="2"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_bottom = -22.0
grow_horizontal = 2
grow_vertical = 2
theme_override_colors/font_shadow_color = Color(0, 0, 0, 1)
theme_override_colors/font_outline_color = Color(0, 0, 0, 1)
theme_override_constants/outline_size = 5
theme_override_constants/shadow_outline_size = 5
theme_override_fonts/font = ExtResource("4_nowc2")
theme_override_font_sizes/font_size = 30
text = "OK"
horizontal_alignment = 1
vertical_alignment = 1

[node name="coin" type="TextureRect" parent="Panel/BGPanel/ConfirmButton" index="3"]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -40.0
offset_top = -1.0
offset_right = 4.0
offset_bottom = 42.0
grow_horizontal = 2
grow_vertical = 2
mouse_filter = 2
texture = ExtResource("9_e8a5t")
expand_mode = 1
stretch_mode = 4

[node name="value" type="Label" parent="Panel/BGPanel/ConfirmButton" index="4"]
layout_mode = 1
anchors_preset = 7
anchor_left = 0.5
anchor_top = 1.0
anchor_right = 0.5
anchor_bottom = 1.0
offset_left = -2.5
offset_top = -45.0
offset_right = 85.5
offset_bottom = 6.0
grow_horizontal = 2
grow_vertical = 0
theme_override_colors/font_shadow_color = Color(0, 0, 0, 1)
theme_override_colors/font_outline_color = Color(0, 0, 0, 1)
theme_override_constants/shadow_offset_y = 2
theme_override_constants/outline_size = 7
theme_override_constants/shadow_outline_size = 6
theme_override_fonts/font = ExtResource("4_nowc2")
theme_override_font_sizes/font_size = 30
text = "10"
vertical_alignment = 1

[node name="ExitButton" parent="Panel" index="2" instance=ExtResource("5_3wbme")]
custom_minimum_size = Vector2(50, 50)
anchors_preset = 0
anchor_right = 0.0
anchor_bottom = 0.0
offset_left = 56.0
offset_top = 20.0
offset_right = 138.0
offset_bottom = 93.0
grow_horizontal = 1
grow_vertical = 1
pivot_offset = Vector2(53, 49)
metadata/_edit_lock_ = true

[node name="TextureRect" type="TextureRect" parent="Panel/ExitButton" index="1"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 7.0
offset_right = 7.0
grow_horizontal = 2
grow_vertical = 2
mouse_filter = 2
texture = ExtResource("11_73c15")
expand_mode = 1
stretch_mode = 4
metadata/_edit_lock_ = true

[node name="CoinShow" parent="Panel" index="3" instance=ExtResource("12_ttlin")]
layout_mode = 1
anchors_preset = 1
anchor_left = 1.0
anchor_bottom = 0.0
offset_left = -167.0
offset_top = 33.0
offset_right = -17.0
offset_bottom = 83.0
grow_horizontal = 0
grow_vertical = 1
link_to_shop = false

[node name="Teleport" parent="." index="2" instance=ExtResource("13_5nx5f")]

[connection signal="gui_input" from="Panel" to="." method="_on_panel_gui_input"]
[connection signal="pressed" from="Panel/BGPanel/ExitButton" to="." method="_on_resume_button_pressed"]
[connection signal="pressed" from="Panel/BGPanel/ResumeButton" to="." method="_on_resume_button_pressed"]
[connection signal="pressed" from="Panel/BGPanel/ConfirmButton" to="." method="_on_confirm_button_pressed"]
