[remap]

importer="texture"
type="CompressedTexture2D"
uid="uid://djol2dbfdw16k"
path.s3tc="res://.godot/imported/persia_file1-file1.png-1efab07b9a94e40ec9ab03e9dad99085.s3tc.ctex"
path.etc2="res://.godot/imported/persia_file1-file1.png-1efab07b9a94e40ec9ab03e9dad99085.etc2.ctex"
metadata={
"imported_formats": ["s3tc_bptc", "etc2_astc"],
"vram_texture": true
}
generator_parameters={
"md5": "339bf373a19ac3faf46c9b2df6294932"
}

[deps]

source_file="res://Scenes/Vehicle/Scenes/Persia/persia_file1-file1.png"
dest_files=["res://.godot/imported/persia_file1-file1.png-1efab07b9a94e40ec9ab03e9dad99085.s3tc.ctex", "res://.godot/imported/persia_file1-file1.png-1efab07b9a94e40ec9ab03e9dad99085.etc2.ctex"]

[params]

compress/mode=2
compress/high_quality=false
compress/lossy_quality=0.7
compress/hdr_compression=1
compress/normal_map=0
compress/channel_pack=0
mipmaps/generate=true
mipmaps/limit=-1
roughness/mode=0
roughness/src_normal=""
process/fix_alpha_border=true
process/premult_alpha=false
process/normal_map_invert_y=false
process/hdr_as_srgb=false
process/hdr_clamp_exposure=false
process/size_limit=0
detect_3d/compress_to=0
