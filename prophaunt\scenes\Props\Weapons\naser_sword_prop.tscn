[gd_scene load_steps=4 format=3 uid="uid://dntdon3pvh63j"]

[ext_resource type="PackedScene" uid="uid://cy82bsxfj1ddv" path="res://prophaunt/scenes/PropObject.tscn" id="1_qwpkv"]
[ext_resource type="PackedScene" uid="uid://bbawqvm2mn0rm" path="res://Scenes/FreeRide/Assets/NetworkNodes/MiniGames/PvP/BattleHeroes/Weapon/NaserSword.tscn" id="2_8bq86"]

[sub_resource type="CapsuleShape3D" id="CapsuleShape3D_5q4rx"]
radius = 0.128505
height = 2.60334

[node name="NaserSwordProp" instance=ExtResource("1_qwpkv")]

[node name="NaserSword" parent="Meshes" index="0" instance=ExtResource("2_8bq86")]

[node name="CollisionShape3D" type="CollisionShape3D" parent="StaticBody3D" index="0"]
transform = Transform3D(1, 0, 0, 0, -4.37114e-08, -1, 0, 1, -4.37114e-08, 0, 0, 0.989699)
shape = SubResource("CapsuleShape3D_5q4rx")
