[gd_scene load_steps=4 format=3 uid="uid://778co83upaj1"]

[ext_resource type="PackedScene" uid="uid://cy82bsxfj1ddv" path="res://prophaunt/scenes/PropObject.tscn" id="1_m0n7e"]
[ext_resource type="PackedScene" uid="uid://r4p1el05b4oj" path="res://prophaunt/maps/Source/Furniture/bookcase_closed.tscn" id="2_n03g1"]

[sub_resource type="BoxShape3D" id="BoxShape3D_n2ccm"]
size = Vector3(1.23079, 2.56427, 0.759491)

[node name="BookcaseClosedProp" instance=ExtResource("1_m0n7e")]

[node name="BookcaseClosed" parent="Meshes" index="0" instance=ExtResource("2_n03g1")]

[node name="CollisionShape3D" type="CollisionShape3D" parent="StaticBody3D" index="0"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0.00249478, 1.285, 0.00761414)
shape = SubResource("BoxShape3D_n2ccm")
