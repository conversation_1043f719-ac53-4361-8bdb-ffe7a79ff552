[gd_scene load_steps=4 format=3 uid="uid://ds1i6slc5m52o"]

[ext_resource type="PackedScene" uid="uid://cy82bsxfj1ddv" path="res://prophaunt/scenes/PropObject.tscn" id="1_i03td"]
[ext_resource type="PackedScene" uid="uid://c0j7b1oca5758" path="res://prophaunt/maps/Source/Chairs/bench_cushion_low.tscn" id="2_fnk7d"]

[sub_resource type="BoxShape3D" id="BoxShape3D_f8ox4"]
size = Vector3(1.6859, 0.799682, 0.885193)

[node name="BenchCushionLowProp" instance=ExtResource("1_i03td")]

[node name="BenchCushionLow" parent="Meshes" index="0" instance=ExtResource("2_fnk7d")]

[node name="CollisionShape3D" type="CollisionShape3D" parent="StaticBody3D" index="0"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -2.2918e-05, 0.399841, -0.00161743)
shape = SubResource("BoxShape3D_f8ox4")
