extends Node

const SYNC_TIMER = 1.0 / 30
const SEESAW = 1
const ROTATE_ITEM = 2
const PUSH_ITEM = 3
const PANDOL_ITEM = 4
const MOVING_PLATFORM_ITEM = 5
const CANNON_ITEM = 6
const V_ROTATE_PIPE = 7
const ROTATE_CIRCLE = 8
const UP_DOWN_DOOR = 9
const RANDOM_DOOR = 10
const MOVING_PATH = 11
const REMOVE_PLATFORM = 12
const HIDE_AND_SEEK_PLATFORM = 13
const GIANT_ROLLETE = 14
const ROTATE_ITEM_LOCAL = 15
const TILE_FALL = 16
const WATER_SPLASH = 17
const FIRE_HYDRANT = 18


var gamescene
var map = null
var packed_data = PackedByteArray()
var client_tick_counter = 0

var counter = 0
func _process(delta):
	if Constants.LOCAL_MODE:
		return

	if Constants.is_client():
		return
	if not is_instance_valid(Constants.server):
		return
	if Constants.server and !(Constants.server.state == Constants.ServerState.InGame or Constants.server.state == Constants.ServerState.CountDown):
		return
	
	counter += delta
	if counter >= SYNC_TIMER:
		counter -= SYNC_TIMER
		encode_all()
		if packed_data.size() > 0:
			sync.rpc(packed_data)


#Server
func encode_all():
	if map == null:
		return
	if map.get("universal_sync_scenes") == null:
		return
	packed_data = PackedByteArray()
	for scene in map.universal_sync_scenes:
		match scene.TYPE:
			SEESAW:
				encode_seesaw(scene)
			ROTATE_ITEM:
				encode_rotate_item(scene)
			PUSH_ITEM:
				encode_push_item(scene)
			PANDOL_ITEM:
				encode_pandol_item(scene)
			MOVING_PLATFORM_ITEM:
				encode_moving_platform_item(scene)
			CANNON_ITEM:
				packed_data = scene.encode(packed_data)
			V_ROTATE_PIPE:
				packed_data = scene.encode(packed_data)
			ROTATE_CIRCLE:
				packed_data = scene.encode(packed_data)
			_:
				if scene.has_method("encode"):
					packed_data = scene.encode(packed_data)
				else:
					print("Unknown Encode TYPE: ", scene.TYPE) 


#Calls on client
@rpc("authority", "call_remote", "unreliable_ordered", Constants.CHANNEL_UNIVERSAL_SYNC)
@warning_ignore("shadowed_variable")
func sync(packed_data):
	client_tick_counter += 1
	var start_index = 0
	if not is_instance_valid(map):
		return
	for scene in map.universal_sync_scenes:
		if scene == null:
			continue
		match scene.TYPE:
			SEESAW:
				var size = decode_seesaw(start_index, packed_data, scene)
				start_index += size
			ROTATE_ITEM:
				var size = decode_rotate_item(start_index, packed_data, scene)
				start_index += size
			PUSH_ITEM:
				var size = decode_push_item(start_index, packed_data, scene)
				start_index += size
			PANDOL_ITEM:
				var size = decode_pandol_item(start_index, packed_data, scene)
				start_index += size
			MOVING_PLATFORM_ITEM:
				var size = decode_moving_platform_item(start_index, packed_data, scene)
				start_index += size
			CANNON_ITEM:
				var size = scene.decode(start_index, packed_data)
				start_index += size
			V_ROTATE_PIPE:
				var size = scene.decode(start_index, packed_data)
				start_index += size
			ROTATE_CIRCLE:
				var size = scene.decode(start_index, packed_data)
				start_index += size
			_:
				if scene.has_method("decode"):
					var size = scene.decode(start_index, packed_data)
					start_index += size
				else:
					print("Unknown Decode TYPE: ", scene.TYPE) 


#Server
func encode_seesaw(seesaw_scene):
	var start_index = packed_data.size()
	packed_data.resize(packed_data.size() + 2)
	packed_data.encode_half(start_index, seesaw_scene.rotation.z) #2bytes


#Client
@warning_ignore("shadowed_variable")
func decode_seesaw(start_index, packed_data, scene):
	var dataSyncer = scene.data_syncer
	dataSyncer.rot = Vector3(0, 0, packed_data.decode_half(start_index))
	return 2


#Server
func encode_rotate_item(scene):
	var start_index = packed_data.size()
	var rot = scene.pipes[0].global_rotation.y
	packed_data.resize(packed_data.size() + scene.get_packet_size())
	packed_data.encode_half(start_index, Constants.convert_angle_to_lerpable(rot)) #2bytes
	start_index += 2


#Client
@warning_ignore("shadowed_variable")
func decode_rotate_item(start_index, packed_data, scene):
	var dataSyncer = scene.data_syncer
	dataSyncer.rotation[0] = Vector3(0, packed_data.decode_half(start_index), 0)
	var index = 1
	for i in range(1, len(scene.pipes)):
		dataSyncer.rotation[index] = dataSyncer.rotation[index - 1]
		dataSyncer.rotation[index].y += deg_to_rad(scene.pipe_degree)
		index += 1
	return scene.get_packet_size()


#Server
func encode_push_item(scene):
	var start_index = packed_data.size()
	packed_data.resize(packed_data.size() + 2)
	packed_data.encode_half(start_index, scene.rotation.y) #2bytes


#Client
@warning_ignore("shadowed_variable")
func decode_push_item(start_index, packed_data, scene):
	scene.data_rotation = Vector3(0, packed_data.decode_half(start_index), 0)
	return 2


#Server
func encode_pandol_item(scene):
	var start_index = packed_data.size()
	packed_data.resize(packed_data.size() + 2)
	packed_data.encode_half(start_index, scene.ball.rotation.x) #2bytes


#Client
@warning_ignore("shadowed_variable")
func decode_pandol_item(start_index, packed_data, scene):
	scene.data_rotation = Vector3(packed_data.decode_half(start_index), 0, 0)
	return 2


#Server
func encode_moving_platform_item(scene):
	var start_index = packed_data.size()
	packed_data.resize(packed_data.size() + 4)
	packed_data.encode_half(start_index, scene.global_position.x) #2bytes
	packed_data.encode_half(start_index + 2, scene.global_position.z) #2bytes


#Client
@warning_ignore("shadowed_variable")
func decode_moving_platform_item(start_index, packed_data, scene):
	scene.sync_position = Vector3(packed_data.decode_half(start_index), 0, packed_data.decode_half(start_index + 2))
	return 4
