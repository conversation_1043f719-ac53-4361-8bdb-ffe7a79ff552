[gd_scene load_steps=6 format=4 uid="uid://buf7oe0xguvdy"]

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_c50t2"]
resource_name = "Wall.001"
cull_mode = 2
metallic = 0.2

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_6fng3"]
resource_name = "Material.001"
transparency = 4
cull_mode = 2
albedo_color = Color(0.25827, 0.687782, 1, 0.20614)
roughness = 0.1

[sub_resource type="ArrayMesh" id="ArrayMesh_rlt7f"]
_surfaces = [{
"aabb": AABB(-2, -2.38419e-07, -2.39951, 4, 4, 0.8),
"format": 34896613377,
"index_count": 840,
"index_data": PackedByteArray("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"),
"name": "Wall.001",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 156,
"vertex_data": PackedByteArray("/7/KzP+/AAD/P8rM/78AAP+/bjb/vwAA/z9uNv+/AAD/v8rM/z8AAP8/ysz/PwAA/79uNv8/AAD/P242/z8AAP//AABUswAA//8AAKtMAAAAAAAAVLMAAAAAAACrTAAA/////6tMAAD/////VLMAAAAA//+rTAAAAAD//1SzAAD/v/9/VLMAAP+//3+rTAAA////f1SzAAD///9/q0wAAP+/cH3/vwAA/79wff8/AAD//3B9VLMAAP//cH2rTAAA//+OglSzAAD//46Cq0wAAP+/joL/vwAA/7+Ogv8/AAD/P/9/VLMAAP8//3+rTAAAAAD/f1SzAAAAAP9/q0wAAP8/cH3/vwAA/z9wff8/AAAAAHB9VLMAAAAAcH2rTAAAAACOglSzAAAAAI6Cq0wAAP8/joL/vwAA/z+Ogv8/AAD/f242VLMAAP9/bjarTAAA/38AAFSzAAD/fwAAq0wAAP9/ysxUswAA/3/KzKtMAAD/f///q0wAAP9///9UswAAjoJuNv+/AACOgm42/z8AAI6CAABUswAAjoIAAKtMAABwfW42/78AAHB9bjb/PwAAcH0AAFSzAABwfQAAq0wAAI6Cysz/vwAAjoLKzP8/AACOgv//q0wAAI6C//9UswAAcH3KzP+/AABwfcrM/z8AAHB9//+rTAAAcH3//1SzAABv/Y8C/78AAG/9b/3/vwAAjwKPAv+/AACPAm/9/78AAG/9/39UswAAb/1wff+/AABv/Y6C/78AAI8C/39UswAAjwJwff+/AACPAo6C/78AAP9/jwJUswAA/39v/VSzAACOgo8C/78AAHB9jwL/vwAAjoJv/f+/AABwfW/9/78AAI8CjwL/PwAAjwJv/f8/AABv/Y8C/z8AAG/9b/3/PwAAb/3/f6tMAABv/XB9/z8AAG/9joL/PwAAjwL/f6tMAACPAnB9/z8AAI8CjoL/PwAA/3+PAqtMAAD/f2/9q0wAAI6CjwL/PwAAcH2PAv8/AACOgm/9/z8AAHB9b/3/PwAA/7/KzP7/AAD/P8rM/v8AAP+/bjb//wAA/79uNv+/AAD/P242//8AAP8/bjb/vwAAMrNqQf//AAAys2rB/v8AAMxMasH+/wAAzExqQf//AAD/v242AAAAAMxMakEAAAAA/z9uNgAAAAD/P8rMAAAAAMxMasEAAAAAMrNqwQAAAAAys2pBAAAAAP+/yswAAAAA/79uNv8/AAD/P242/z8AAMxMaoH/fwAAMrNqgf9/AAAyU9GHulwAAMys0Ye6XAAAMlMEu7pcAADMrAS7ulwAADJTBLtuQAAAMlPRh25AAAAyUwS7/38AADJT0Yf/fwAAzKwEu25AAADMrAS7/38AAMys0YduQAAAzKzRh/9/AADMTGqBbkAAAMxMasFuQAAAzExqwf9/AAAys2rBbkAAADKzasH/fwAAMrNqgW5AAAAyU9NHRKMAAMys00dEowAAMlMGe0SjAADMrAZ7RKMAADJTBnv/fwAAMlPTR/9/AAAyUwZ7kL8AADJT00eQvwAAzKwGe/9/AADMrAZ7kL8AAMys00f/fwAAzKzTR5C/AADMTG1B/38AAMxMbYH/fwAAzExtQZC/AADMTG2BkL8AADKzbYH/fwAAMrNtgZC/AAAys21BkL8AADKzbUH/fwAA")
}, {
"aabb": AABB(-0.7, 1.12232, -2.12175, 1.4, 1.79985, 0.23367),
"format": 34359742465,
"index_count": 72,
"index_data": PackedByteArray("AgABAAMAAgAAAAEABgAFAAQABgAHAAUAAwAFAAcAAwABAAUAAAAGAAQAAAACAAYAAgAHAAYAAgADAAcAAQAEAAUAAQAAAAQACgAJAAsACgAIAAkADgANAAwADgAPAA0ACQAMAA0ACQAIAAwACwANAA8ACwAJAA0ACAAOAAwACAAKAA4ACgAPAA4ACgALAA8A"),
"name": "Material.001",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 16,
"vertex_data": PackedByteArray("MjMzv6LRB0DGygfAMjMzP6LRB0DGygfAMjMzv9YEO0DGygfAMjMzP9YEO0DGygfAMjMzv6LRB0BSAgXAMjMzP6LRB0BSAgXAMjMzv9YEO0BSAgXAMjMzP9YEO0BSAgXAMjMzvxCojz+OPfe/MjMzPxCojz+OPfe/MjMzv3QO9j+OPfe/MjMzP3QO9j+OPfe/MjMzvxCojz+krPG/MjMzPxCojz+krPG/MjMzv3QO9j+krPG/MjMzP3QO9j+krPG/")
}]
blend_shape_mode = 0

[sub_resource type="ArrayMesh" id="ArrayMesh_qg0hx"]
resource_name = "WallWindowSlideUV_WallWindowSlideUV_001"
_surfaces = [{
"aabb": AABB(-2, -2.38419e-07, -2.39951, 4, 4, 0.8),
"attribute_data": PackedByteArray("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"),
"format": 34896613399,
"index_count": 840,
"index_data": PackedByteArray("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"),
"material": SubResource("StandardMaterial3D_c50t2"),
"name": "Wall.001",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 520,
"vertex_data": PackedByteArray("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")
}, {
"aabb": AABB(-0.7, 1.12232, -2.12175, 1.4, 1.79985, 0.23367),
"attribute_data": PackedByteArray("LbEyPpzpNz8tsTI+nOk3Py2xMj6c6Tc/J7EyPpzpNz8nsTI+nOk3PyexMj6c6Tc/LbEyPpzpNz8tsTI+nOk3Py2xMj6c6Tc/KbEyPpzpNz8psTI+nOk3PymxMj6c6Tc/LbEyPpzpNz8tsTI+nOk3Py2xMj6c6Tc/J7EyPpzpNz8nsTI+nOk3PyexMj6c6Tc/LbEyPpzpNz8tsTI+nOk3Py2xMj6c6Tc/KbEyPpzpNz8psTI+nOk3PymxMj6c6Tc/J7EyPpzpNz8nsTI+nOk3PyexMj6c6Tc/IbEyPpzpNz8hsTI+nOk3PyGxMj6c6Tc/KbEyPpzpNz8psTI+nOk3PymxMj6c6Tc/I7EyPpzpNz8jsTI+nOk3PyOxMj6c6Tc/J7EyPpzpNz8nsTI+nOk3PyexMj6c6Tc/IbEyPpzpNz8hsTI+nOk3PyGxMj6c6Tc/KbEyPpzpNz8psTI+nOk3PymxMj6c6Tc/I7EyPpzpNz8jsTI+nOk3PyOxMj6c6Tc/"),
"format": 34359742487,
"index_count": 72,
"index_data": PackedByteArray("BgADAAoABgABAAMAEwAQAAwAEwAVABAACwARABcACwAFABEAAgAUAA4AAgAIABQABwAWABIABwAJABYABAANAA8ABAAAAA0AHgAbACEAHgAYABsAKgAnACQAKgAtACcAHAAlACgAHAAZACUAIwApAC8AIwAdACkAGgAsACYAGgAgACwAHwAuACsAHwAiAC4A"),
"material": SubResource("StandardMaterial3D_6fng3"),
"name": "Material.001",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 48,
"vertex_data": PackedByteArray("MjMzv6LRB0DGygfAMjMzv6LRB0DGygfAMjMzv6LRB0DGygfAMjMzP6LRB0DGygfAMjMzP6LRB0DGygfAMjMzP6LRB0DGygfAMjMzv9YEO0DGygfAMjMzv9YEO0DGygfAMjMzv9YEO0DGygfAMjMzP9YEO0DGygfAMjMzP9YEO0DGygfAMjMzP9YEO0DGygfAMjMzv6LRB0BSAgXAMjMzv6LRB0BSAgXAMjMzv6LRB0BSAgXAMjMzP6LRB0BSAgXAMjMzP6LRB0BSAgXAMjMzP6LRB0BSAgXAMjMzv9YEO0BSAgXAMjMzv9YEO0BSAgXAMjMzv9YEO0BSAgXAMjMzP9YEO0BSAgXAMjMzP9YEO0BSAgXAMjMzP9YEO0BSAgXAMjMzvxCojz+OPfe/MjMzvxCojz+OPfe/MjMzvxCojz+OPfe/MjMzPxCojz+OPfe/MjMzPxCojz+OPfe/MjMzPxCojz+OPfe/MjMzv3QO9j+OPfe/MjMzv3QO9j+OPfe/MjMzv3QO9j+OPfe/MjMzP3QO9j+OPfe/MjMzP3QO9j+OPfe/MjMzP3QO9j+OPfe/MjMzvxCojz+krPG/MjMzvxCojz+krPG/MjMzvxCojz+krPG/MjMzPxCojz+krPG/MjMzPxCojz+krPG/MjMzPxCojz+krPG/MjMzv3QO9j+krPG/MjMzv3QO9j+krPG/MjMzv3QO9j+krPG/MjMzP3QO9j+krPG/MjMzP3QO9j+krPG/MjMzP3QO9j+krPG//3///////78PgP9/////P////3////+//38PgP///z//f///////vwAA/3////+//3/vf////z//fwAA////v////3////+//38AAP///7/vf/9/////PwAA/3////+/7v///////7//f///////v////3////+//3///////78AAO7/////vwAA/3////+//38AAP///7///+7/////v////3////+/7v8AAP///7//fwAA////vwAA/3////+//3//f////z//f+7/////P+7/D4D///+//3//f////z/vf+7/////vxAA/3////8//3//f////z8PgBAA////v+7//3////8//3//f////z//fxAA////PxAA73////+//////////78PgO7/////v///D4D///+//////////78PgP//////vxAAD4D///+//////////78PgAAA////v+7/73////+//////////7/vfxAA////vwAAD4D///+/")
}]
blend_shape_mode = 0
shadow_mesh = SubResource("ArrayMesh_rlt7f")

[sub_resource type="BoxShape3D" id="BoxShape3D_ox0qy"]
size = Vector3(4, 4, 0.8)

[node name="WallWindowSlide" type="MeshInstance3D"]
mesh = SubResource("ArrayMesh_qg0hx")
skeleton = NodePath("")

[node name="StaticBody3D" type="StaticBody3D" parent="."]
collision_layer = 2

[node name="CollisionShape3D" type="CollisionShape3D" parent="StaticBody3D" groups=["VisibleGroup0"]]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -0.00026238, 2, -1.99972)
shape = SubResource("BoxShape3D_ox0qy")
