extends "res://networking/Server/ServerState.gd"

# Base class for all Prophaunt server states
class_name ProphauntServerState

# Prophaunt-specific server data
var current_round = 1
var rounds_played_on_map = 0
var round_timer = 0.0
var round_start_time = 0
var map_rotation_index = 0


func _ready():
	super()


func assign_teams():
	"""Assign players to Props and Haunters teams"""
	server.props_team.clear()
	server.haunters_team.clear()
	#props_alive.clear()
	
	var all_players = []
	for key in server.players_data.keys():
		if not server.is_dc(key):
			all_players.append(key)
	
	# Shuffle players
	all_players.shuffle()
	
	# Assign teams (roughly 2/3 props, 1/3 haunters)
	var total_players = all_players.size()
	@warning_ignore("integer_division")
	var haunters_count = max(1, total_players / 3)
	
	for i in range(total_players):
		if i < haunters_count and server.is_bot(all_players[i]):
			print(all_players[i], " is Haunter")
			server.haunters_team.append(all_players[i])
			server.players_data[all_players[i]]["prophaunt_team"] = Constants.ProphauntTeam.HAUNTERS
		else:
			print(all_players[i], " is Prop")
			server.props_team.append(all_players[i])
			server.players_data[all_players[i]]["prophaunt_team"] = Constants.ProphauntTeam.PROPS
		
		# Initialize prophaunt-specific player data
		server.players_data[all_players[i]]["prophaunt_hp"] = Constants.PROPHAUNT_PROP_DEFAULT_HP
		server.players_data[all_players[i]]["prophaunt_state"] = Constants.ProphauntPlayerState.ALIVE
		server.players_data[all_players[i]]["prophaunt_hex_cooldown"] = 0.0
		server.players_data[all_players[i]]["prophaunt_sound_cooldown"] = Constants.PROPHAUNT_PROP_SOUND_COOLDOWN


func check_round_end_conditions():
	"""Check if round should end and return winner team"""
	# Check if all props are dead
	#if props_alive.size() == 0:
		#return Constants.ProphauntTeam.HAUNTERS
	
	# Check if time is up
	if round_timer <= 0:
		return Constants.ProphauntTeam.PROPS
	
	return -1  # Round continues


func eliminate_prop(_player_id):
	"""Eliminate a prop player"""
	#if player_id in props_alive:
		#props_alive.erase(player_id)
		#server.players_data[player_id]["prophaunt_state"] = Constants.ProphauntPlayerState.DEAD
#
		## Update ProphauntPlayer component
		#var player_manager = ProphauntPlayerManager.get_instance()
		#var prophaunt_player = player_manager.get_prophaunt_player(player_id)
		#if prophaunt_player:
			#prophaunt_player.die()
#
		## Notify all players
		#for key in server.players_data.keys():
			#if not server.is_bot(key) and not server.is_dc(key):
				#multiplayer.rpc(key, ClientRPC, "prophaunt_player_eliminated", [player_id])


func damage_prop(player_id, damage):
	"""Damage a prop player"""
	#if player_id not in props_alive:
		#return false
	
	server.players_data[player_id]["prophaunt_hp"] -= damage
	
	if server.players_data[player_id]["prophaunt_hp"] <= 0:
		eliminate_prop(player_id)
		return true
	
	# Notify all players of damage
	for key in server.players_data.keys():
		if not server.is_bot(key) and not server.is_dc(key):
			multiplayer.rpc(key, ClientRPC, "prophaunt_player_damaged", [player_id, server.players_data[player_id]["prophaunt_hp"]])
	
	return false


func hex_haunter(haunter_id, duration):
	"""Apply hex effect to a haunter"""
	server.players_data[haunter_id]["prophaunt_state"] = Constants.ProphauntPlayerState.HEXED
	server.players_data[haunter_id]["prophaunt_hex_duration"] = duration
	
	# Notify all players
	for key in server.players_data.keys():
		if not server.is_bot(key) and not server.is_dc(key):
			multiplayer.rpc(key, ClientRPC, "prophaunt_player_hexed", [haunter_id, duration])


func update_hex_effects(delta):
	"""Update hex effects on haunters"""
	for haunter_id in server.haunters_team:
		if server.players_data[haunter_id]["prophaunt_state"] == Constants.ProphauntPlayerState.HEXED:
			server.players_data[haunter_id]["prophaunt_hex_duration"] -= delta
			if server.players_data[haunter_id]["prophaunt_hex_duration"] <= 0:
				server.players_data[haunter_id]["prophaunt_state"] = Constants.ProphauntPlayerState.ALIVE
				# Notify unhex
				for key in server.players_data.keys():
					if not server.is_bot(key) and not server.is_dc(key):
						multiplayer.rpc(key, ClientRPC, "prophaunt_player_unhexed", [haunter_id])


func update_cooldowns(delta):
	"""Update various cooldowns"""
	for player_id in server.players_data.keys():
		if server.is_bot(player_id) or server.is_dc(player_id):
			continue
		
		# Update hex cooldown for props
		if server.players_data[player_id]["prophaunt_team"] == Constants.ProphauntTeam.PROPS:
			if server.players_data[player_id]["prophaunt_hex_cooldown"] > 0:
				server.players_data[player_id]["prophaunt_hex_cooldown"] -= delta
			
			# Update sound cooldown for props
			if server.players_data[player_id]["prophaunt_sound_cooldown"] > 0:
				server.players_data[player_id]["prophaunt_sound_cooldown"] -= delta
			#elif player_id in props_alive:
				# Time to make sound
				#play_prop_detection_sound(player_id)
				#server.players_data[player_id]["prophaunt_sound_cooldown"] = Constants.PROPHAUNT_PROP_SOUND_COOLDOWN


func play_prop_detection_sound(prop_id):
	"""Play 3D sound from prop location to help haunters find them"""
	if server.players[prop_id]:
		var position = server.players[prop_id].global_position
		var audio_system = ProphauntAudioSystem.new()
		audio_system.play_prop_detection_sound(prop_id, position)
