[gd_scene load_steps=4 format=3 uid="uid://bhj1pj3dbrwmw"]

[ext_resource type="PackedScene" uid="uid://cy82bsxfj1ddv" path="res://prophaunt/scenes/PropObject.tscn" id="1_f2j7a"]
[ext_resource type="PackedScene" uid="uid://lxk62qmq6f3u" path="res://prophaunt/maps/Source/ArenaProp/weapon_rack_wall.tscn" id="2_5tgo4"]

[sub_resource type="CapsuleShape3D" id="CapsuleShape3D_5q4rx"]
radius = 0.268081
height = 2.48338

[node name="WeaponRackProp" instance=ExtResource("1_f2j7a")]

[node name="WeaponRackWall" parent="Meshes" index="0" instance=ExtResource("2_5tgo4")]

[node name="CollisionShape3D" type="CollisionShape3D" parent="StaticBody3D" index="0"]
transform = Transform3D(-4.37114e-08, -1, 0, 1, -4.37114e-08, 0, 0, 0, 1, 0, 0.946154, -0.232383)
shape = SubResource("CapsuleShape3D_5q4rx")
