[gd_scene load_steps=4 format=4 uid="uid://wxixfpp5qwyt"]

[ext_resource type="Material" uid="uid://b6gd8jjb1kopc" path="res://prophaunt/Mat/Colormap2.tres" id="1_qraxb"]

[sub_resource type="ArrayMesh" id="ArrayMesh_q0w1e"]
_surfaces = [{
"aabb": AABB(-0.678195, 1.4113e-06, 0.00308988, 1.35639, 1.95633, 0.649973),
"format": 34359742465,
"index_count": 192,
"index_data": PackedByteArray("AQACAAAAAgABAAMABQAGAAQABgAFAAcACQAKAAgACgAJAAsADAAGAA0ADAANAAAAAAANAAEADgAJAA8ADwAQAA4AEwAUABIAFAATABUAFwAJAAgAEwAXAAgAFAAWAAMAAwAWAAIAGQAFABgABgAUAA0AEgAUABkAAAAWAAwAFgAAAAIAGgAIAAoAGgAVABMAFQAaABsAFwAMABYAEQAbABAAGwARABUACgAbABoAGwAKAAsAGwALABAAEAALAA4ACAAfABcAHwAIAB4AHgAIABMAHgATABIAGAAfAB0AHwAYABcAHQAZABgABgAMAAQACQAOAAsAEAAPABEAFAAVABEAFAARAA8ADwAWABQACQAWAA8ACQAXABYAEgAXABMAEgAYABcAGAASABkABQAZAAcAGQAGAAcABgAZABQACAAaABMAFwAEAAwAGAAEABcABAAYAAUADQADAAEAAwANABQAHQAeABwAHgAdAB8AHAASABkAEgAcAB4AGQAdABwA"),
"name": "Material",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 32,
"vertex_data": PackedByteArray("qtKJviRp+j9Qh0o7rNKJPiRp+j9ehEo7qdKJviRp+j9ZQuc9rtKJPiRp+j9CQuc9Mp4tv8aIvjURh0o7MZ4tv99rvTVXQuc9Mp4tP8aIvjWof0o7NJ4tP99rvTUdQuc9acACvzzfJD5RQuc9acACvyun2z9hQuc9acACvz3fJD6Elm09acACvyun2z+jlm09Mp4tv9va6j9kiUo7Mp4tP9va6j/6gUo7xsF5vkjN5T+Nlm09xcF5vkjN5T9XQuc9v8F5PkjN5T9ilm09wMF5PkjN5T9CQuc9NJ4tPzzfJD4eQuc9acACPzzfJD4mQuc9NJ4tP9va6j8vQuc9acACPyun2z81Quc9MZ4tv9va6j9qQuc9MZ4tvzzfJD5ZQuc9MZ4tvzFfuD1YQuc9NJ4tPzFfuD0dQuc9acACPz3fJD4qlm09acACPyun2z9Jlm09Np4tPytfuD0bLyc/L54tvytfuD0iLyc/Np4tPzrfJD4bLyc/L54tvzrfJD4iLyc/")
}]
blend_shape_mode = 0

[sub_resource type="ArrayMesh" id="ArrayMesh_p6u4y"]
resource_name = "BathroomMirror_bathroomMirror"
_surfaces = [{
"aabb": AABB(-0.678195, 1.4113e-06, 0.00308988, 1.35639, 1.95633, 0.649973),
"attribute_data": PackedByteArray("zoRaP1TY6j7OhFo/VNjqPs6EWj9U2Oo+zoRaP1TY6j7OhFo/ZNjqPs6EWj9U2Oo+zoRaP1TY6j7OhFo/VNjqPs6EWj9U2Oo+zoRaP2TY6j7OhFo/VNjqPs6EWj9U2Oo+zoRaP1TY6j7OhFo/VNjqPs6EWj9U2Oo+zoRaP1TY6j7OhFo/VNjqPs6EWj9U2Oo+zoRaP1TY6j7OhFo/VNjqPs6EWj9U2Oo+zoRaP1TY6j7OhFo/VNjqPs6EWj9U2Oo+zoRaP1TY6j7OhFo/VNjqPs6EWj9U2Oo+zoRaP1TY6j7OhFo/VNjqPvlIDj0m9V4/zoRaP1TY6j7OhFo/VNjqPufHDj2Mg0s/zoRaP1TY6j7OhFo/VNjqPs6EWj9U2Oo+zoRaP1TY6j7OhFo/VNjqPs6EWj9U2Oo+zoRaP1TY6j7OhFo/VNjqPvYwRD0ch0o/zoRaP1TY6j7OhFo/VNjqPs6EWj9U2Oo+F+SSPZqJSj/OhFo/VNjqPs6EWj9U2Oo+zoRaP1TY6j7OhFo/VNjqPs6EWj9U2Oo+zoRaP1TY6j7OhFo/VNjqPs6EWj9U2Oo+zoRaP1TY6j7OhFo/VNjqPs6EWj9U2Oo+zoRaP1TY6j7OhFo/VNjqPs6EWj9U2Oo+zoRaP0TY6j7OhFo/VNjqPs6EWj9U2Oo+zoRaP1TY6j7OhFo/VNjqPs6EWj9U2Oo+zoRaP1TY6j7OhFo/VNjqPs6EWj9U2Oo+zoRaP1TY6j7OhFo/VNjqPs6EWj9U2Oo+zoRaP1TY6j7OhFo/VNjqPs6EWj9U2Oo+rlKtPVz6Xj/OhFo/VNjqPs6EWj9U2Oo+JJKtPcKISz/OhFo/RNjqPs6EWj9U2Oo+zoRaP1TY6j7OhFo/ZNjqPs6EWj9U2Oo+zoRaP1TY6j7OhFo/ZNjqPs6EWj9U2Oo+zoRaP1TY6j7OhFo/ZNjqPs6EWj9U2Oo+zoRaP1TY6j7OhFo/ZNjqPs6EWj9U2Oo+"),
"format": 34359742487,
"index_count": 192,
"index_data": PackedByteArray("AwAGAAEABgADAAgADgARAAsAEQAOABQAHAAfABkAHwAcACIAIwAQACYAIwAmAAAAAAAmAAIAKgAbACwALAAuACoANAA4ADEAOAA0ADsAQQAaABYANgBDABgAOAA+AAcABwA+AAUASAANAEUAEgA6ACgAMQA4AEgAAQA/ACQAPwABAAYATAAXAB4ATQA9ADcAPQBNAFAARAAlAEAAMABPAC4ATwAwADwAHQBOAEsATgAdACAATgAgAC0ALQAgACkAFwBbAEIAWwAXAFgAWAAXADUAWAA1ADIARwBcAFYAXABHAEQAVQBJAEYAEAAjAAoAGwAqACEALgAsADAAOAA7AC8AOAAvACsAKwA+ADgAGgA+ACsAGgBBAD4AMgBCADUAMQBFAEEARQAxAEgADQBIABMASgASABUAEgBKADoAFwBMADUARAAMACUARwAMAEQADABHAA8AJwAJAAQACQAnADkAVABXAFEAVwBUAFoAUwAzAEoAMwBTAFkASQBVAFIA"),
"material": ExtResource("1_qraxb"),
"name": "Material",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 93,
"vertex_data": PackedByteArray("qtKJviRp+j9Qh0o7qtKJviRp+j9Qh0o7rNKJPiRp+j9ehEo7rNKJPiRp+j9ehEo7rNKJPiRp+j9ehEo7qdKJviRp+j9ZQuc9qdKJviRp+j9ZQuc9rtKJPiRp+j9CQuc9rtKJPiRp+j9CQuc9rtKJPiRp+j9CQuc9Mp4tv8aIvjURh0o7Mp4tv8aIvjURh0o7Mp4tv8aIvjURh0o7MZ4tv99rvTVXQuc9MZ4tv99rvTVXQuc9MZ4tv99rvTVXQuc9Mp4tP8aIvjWof0o7Mp4tP8aIvjWof0o7Mp4tP8aIvjWof0o7NJ4tP99rvTUdQuc9NJ4tP99rvTUdQuc9NJ4tP99rvTUdQuc9acACvzzfJD5RQuc9acACvzzfJD5RQuc9acACvzzfJD5RQuc9acACvzzfJD5RQuc9acACvyun2z9hQuc9acACvyun2z9hQuc9acACvyun2z9hQuc9acACvz3fJD6Elm09acACvz3fJD6Elm09acACvz3fJD6Elm09acACvyun2z+jlm09acACvyun2z+jlm09acACvyun2z+jlm09Mp4tv9va6j9kiUo7Mp4tv9va6j9kiUo7Mp4tv9va6j9kiUo7Mp4tP9va6j/6gUo7Mp4tP9va6j/6gUo7Mp4tP9va6j/6gUo7xsF5vkjN5T+Nlm09xsF5vkjN5T+Nlm09xcF5vkjN5T9XQuc9xcF5vkjN5T9XQuc9v8F5PkjN5T9ilm09v8F5PkjN5T9ilm09wMF5PkjN5T9CQuc9wMF5PkjN5T9CQuc9NJ4tPzzfJD4eQuc9NJ4tPzzfJD4eQuc9NJ4tPzzfJD4eQuc9acACPzzfJD4mQuc9acACPzzfJD4mQuc9acACPzzfJD4mQuc9acACPzzfJD4mQuc9NJ4tP9va6j8vQuc9NJ4tP9va6j8vQuc9NJ4tP9va6j8vQuc9acACPyun2z81Quc9acACPyun2z81Quc9acACPyun2z81Quc9MZ4tv9va6j9qQuc9MZ4tv9va6j9qQuc9MZ4tv9va6j9qQuc9MZ4tvzzfJD5ZQuc9MZ4tvzzfJD5ZQuc9MZ4tvzzfJD5ZQuc9MZ4tvzzfJD5ZQuc9MZ4tvzFfuD1YQuc9MZ4tvzFfuD1YQuc9MZ4tvzFfuD1YQuc9NJ4tPzFfuD0dQuc9NJ4tPzFfuD0dQuc9NJ4tPzFfuD0dQuc9acACPz3fJD4qlm09acACPz3fJD4qlm09acACPz3fJD4qlm09acACPyun2z9Jlm09acACPyun2z9Jlm09acACPyun2z9Jlm09Np4tPytfuD0bLyc/Np4tPytfuD0bLyc/Np4tPytfuD0bLyc/L54tvytfuD0iLyc/L54tvytfuD0iLyc/L54tvytfuD0iLyc/Np4tPzrfJD4bLyc/Np4tPzrfJD4bLyc/Np4tPzrfJD4bLyc/L54tvzrfJD4iLyc/L54tvzrfJD4iLyc/L54tvzrfJD4iLyc//////////7+/b7/v////v/////////+/P5C/7////78/kL/v////v/9//3////8/v2+/7////7//f/9/////Pz+Qv+////+/P5C/7////7//////////v/9/AAD///+/AAD/f////7//f/9/////P/9/AAD///+/AAD/f////7//////////v/9/AAD///+/////f////7//f/9/////P/9/AAD///+/////f////7//f/9/////P/9///////+//38AAP///7////9/////v/9//3////8/UJ1RHf///7////9/////v/9//3/K/xnA/3///////7////9/////v/9//3/K/xnAUJ1RHf///7////9/////v/////////+/rmKt4v///78AAP9/////v/////////+/UJ2t4v///7////9/////v/9//3/K/xnAP5A/EP///7//f/9/////Pz+QPxD///+//3//f8r/GcC/bz8Q////v/9//3////8/v28/EP///7//f/9/////P/9///////+/////f////7//f/9/////P/9///////+//38AAP///78AAP9/////v/9//3////8/UJ2t4v///7////9/////v/9//3////8/rmJRHf///78AAP9/////v/9//3////8/rmKt4v///78AAP9/////v/9//3////8//3///////7//fwAA////vwAA/3////+//3//f////z//fwAA////vwAA/3////+//3//f////z//fwAA////v////3////+//3//f8r/GcD/f///////vwAA/3////+//3//f8r/GcCuYlEd////vwAA/3////+//3//f////z//fwAA////v////3////+//3//f////z//fwAA////vwAA/3////+//3//f////z//f///////v////3////+//3//f////z//f///////vwAA/3////+/")
}]
blend_shape_mode = 0
shadow_mesh = SubResource("ArrayMesh_q0w1e")

[node name="bathroomMirror" type="MeshInstance3D"]
mesh = SubResource("ArrayMesh_p6u4y")
skeleton = NodePath("")
