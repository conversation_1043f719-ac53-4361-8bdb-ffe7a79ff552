[gd_scene load_steps=12 format=3 uid="uid://bjy67sp23iyn3"]

[ext_resource type="Script" path="res://networking/server.gd" id="1_xkxe4"]
[ext_resource type="Script" path="res://networking/Server/Lobby.gd" id="2_1050v"]
[ext_resource type="Script" path="res://networking/Server/Loading.gd" id="2_q2bww"]
[ext_resource type="Script" path="res://networking/Server/CountDown.gd" id="2_s155b"]
[ext_resource type="Script" path="res://networking/Server/InGame.gd" id="5_q5yys"]
[ext_resource type="Script" path="res://networking/Server/Results.gd" id="6_7oig6"]
[ext_resource type="Script" path="res://networking/Server/FreeRidePingChecker.gd" id="7_qdole"]
[ext_resource type="Script" path="res://networking/ServerSyncV2.gd" id="8_p5xxc"]
[ext_resource type="Script" path="res://prophaunt/scripts/ProphauntLobby.gd" id="9_prop1"]
[ext_resource type="Script" path="res://prophaunt/scripts/ProphauntInGame.gd" id="10_prop2"]
[ext_resource type="Script" path="res://prophaunt/scripts/ProphauntResults.gd" id="11_prop3"]

[node name="Server" type="Control"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
mouse_filter = 2
script = ExtResource("1_xkxe4")

[node name="Loading" type="Node" parent="."]
unique_name_in_owner = true
script = ExtResource("2_q2bww")

[node name="Lobby" type="Node" parent="."]
unique_name_in_owner = true
script = ExtResource("2_1050v")

[node name="CountDown" type="Node" parent="."]
unique_name_in_owner = true
script = ExtResource("2_s155b")

[node name="InGame" type="Node" parent="."]
unique_name_in_owner = true
script = ExtResource("5_q5yys")

[node name="Results" type="Node" parent="."]
unique_name_in_owner = true
script = ExtResource("6_7oig6")

[node name="FreeridePingChecker" type="Node" parent="."]
script = ExtResource("7_qdole")

[node name="ServerSyncV2" type="Node" parent="."]
script = ExtResource("8_p5xxc")

[node name="ProphauntLobby" type="Node" parent="."]
unique_name_in_owner = true
script = ExtResource("9_prop1")

[node name="ProphauntInGame" type="Node" parent="."]
unique_name_in_owner = true
script = ExtResource("10_prop2")

[node name="ProphauntResults" type="Node" parent="."]
unique_name_in_owner = true
script = ExtResource("11_prop3")
