[gd_scene load_steps=12 format=4 uid="uid://cn1c31s7u8y15"]

[ext_resource type="PackedScene" uid="uid://cy82bsxfj1ddv" path="res://prophaunt/scenes/PropObject.tscn" id="1_744fn"]
[ext_resource type="Texture2D" uid="uid://cq8xh43fvjb7s" path="res://Scenes/FreeRide/Assets/Buildings/Restaurant/restaurant.png" id="2_4vwye"]
[ext_resource type="Material" uid="uid://bay0yvspay8sg" path="res://assets/characters/Mat/NextPassMat.tres" id="3_e3bwi"]

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_s3oqj"]
cull_mode = 1
albedo_color = Color(0, 0, 0, 1)
grow = true
grow_amount = 0.02

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_7n6n0"]
resource_name = "restaurant"
next_pass = SubResource("StandardMaterial3D_s3oqj")
cull_mode = 2
diffuse_mode = 3
vertex_color_use_as_albedo = true
albedo_texture = ExtResource("2_4vwye")
roughness = 0.14
backlight_enabled = true
backlight = Color(0.435294, 0.0980392, 0.0509804, 1)

[sub_resource type="ArrayMesh" id="ArrayMesh_a533n"]
_surfaces = [{
"aabb": AABB(-1, -1, -0.799998, 2, 2.00002, 0.8),
"format": 34359742465,
"index_count": 396,
"index_data": PackedByteArray("BQAHABAAAQAEAAMAAgABAAMAAgAAAAEACAACAAMACAAJAAIABgABAAAABgAFAAEABgACAAkABgAAAAIABwAFAAgABgAIAAUABgAJAAgABAABAAwADAABAAsABQANAAEABQARAA0ADQALAAEADQAKAAsAOwBHAD8AOwBEAEcACwAPAA4ACwAKAA8ABQAQAA4ADwAFAA4ADwARAAUAFwAiACAAEwAWABUAGgAUABUAGgAbABQAFAATABUAFAASABMAGAATABIAGAAXABMAGAAUABsAGAASABQAGQAVABYAGQAaABUAGAAaABcAGAAbABoAFgATAB4AIgAWAB4AIgAZABYAHgATAB0AFwAfABMAFwAjAB8AHwAdABMAHwAcAB0AIQAdABwAIQAgAB0AIgAdACAAIgAeAB0AIQAfACMAIQAcAB8AGQAiABcAIQAXACAAIQAjABcAGQAXABoAKQA0ADIAJQAoACcALAAmACcALAAtACYAJgAlACcAJgAkACUAKgAlACQAKgApACUAKgAmAC0AKgAkACYAKwAnACgAKwAsACcAKgAsACkAKgAtACwAKAAlADAANAAoADAANAArACgAMAAlAC8AKQAxACUAKQA1ADEAMQAvACUAMQAuAC8AMwAvAC4AMwAyAC8ANAAvADIANAAwAC8AMwAxADUAMwAuADEAKwA0ACkAMwApADIAMwA1ACkAKwApACwAFgAoABkAFgArACgANwA5ADgANwA2ADkACgARAA8ACgANABEAEAALAA4AEAAMAAsAAwAHAAgAAwAEAAcARQBJAEIARQBLAEkAPgBAAEoAPgBBAEAARgA+AEoARgA6AD4AQQA6ADwAQQA+ADoAPABGAD0APAA6AEYAPwBKAEAARABGAEMARQBDAEYARQBCAEMAQAA7AD8AQAA9ADsARgA7AD0ASQBKAEgASQBLAEoARgBEADsASgBHAEgARwBKAD8AQwBJAEgAQwBCAEkARQBKAEsARQBGAEoAQAA8AD0AQABBADwASABEAEMASABHAEQAEAAEAAwAEAAHAAQA"),
"name": "restaurant",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 76,
"vertex_data": PackedByteArray("ZAR4PxEAgD8uqr2+AACAP/vOdz+Y+8y+ZAR4PxEAgD8Ej++8AACAP/vOdz+9NwY2AACAPwAAQD+9NwY2AACAv/vOdz9eFM2+ZAR4vxEAgD8uqr2+AACAvwAAQD+9NwY2AACAv/vOdz+9NwY2ZAR4vxEAgD8Ej++8ZAR4PxEAgD+/10S/AACAP/vOdz+rzEy/AACAPwAAQD+rzEy/ZAR4PxEAgD9cjdy+AACAv/vOdz+rzEy/ZAR4vxEAgD+/10S/AACAvwAAQD+rzEy/ZAR4vxEAgD9cjdy+MzNzPwZkN78uqr2+zcxsPwAAQL9EUM2+MzNzPwZkN78Ej++8zcxsPwAAQL+9NwY2AABAPwAAQL+9NwY23sxsPwAAQD/XE82+RDNzP/VjNz8uqr2+EQBAPwAAQD+9NwY23sxsPwAAQD+9NwY2RDNzP/VjNz8Ej++8MzNzPwZkN7+/10S/zcxsPwAAQL+rzEy/AABAPwAAQL+rzEy/MzNzPwZkN79cjdy+3sxsPwAAQD+rzEy/RDNzP/VjNz+/10S/EQBAPwAAQD+rzEy/RDNzP/VjNz9cjdy+MzNzvwZkNz8uqr2+zcxsvwAAQD9EUM2+MzNzvwZkNz8Ej++8zcxsvwAAQD+9NwY2AABAvwAAQD+9NwY23sxsvwAAQL/XE82+RDNzv/VjN78uqr2+EQBAvwAAQL+9NwY23sxsvwAAQL+9NwY2RDNzv/VjN78Ej++8MzNzvwZkNz+/10S/zcxsvwAAQD+rzEy/AABAvwAAQD+rzEy/MzNzvwZkNz9cjdy+3sxsvwAAQL+rzEy/RDNzv/VjN7+/10S/EQBAvwAAQL+rzEy/RDNzv/VjN79cjdy+EQBAPwAAQD/NzMy9AABAPwAAQL/NzMy9EQBAvwAAQL/NzMy9AABAvwAAQD/NzMy9UwR4PxkAgL9cjdy+AACAPwAAQL+rzEy/UwR4PxEAgL+/10S/AACAP/vOd7+rzEy/dQR4vwgAgL9cjdy+AACAvwAAQL+rzEy/AACAv+rOd7+rzEy/dQR4vwgAgL+/10S/UwR4PxkAgL8Ej++8AACAP/vOd7+9NwY2AACAPwAAQL+9NwY2UwR4PxEAgL8uqr2+AACAP/vOd79eFM2+AACAvwAAQL+9NwY2AACAv+rOd7+9NwY2dQR4vwgAgL8Ej++8AACAv+rOd7+Y+8y+dQR4vwgAgL8uqr2+")
}]
blend_shape_mode = 0

[sub_resource type="ArrayMesh" id="ArrayMesh_psvhb"]
resource_name = "FoodHolder_crate_Cube_177"
lightmap_size_hint = Vector2i(134, 130)
_surfaces = [{
"aabb": AABB(-1, -1, -0.799998, 2, 2.00001, 0.8),
"attribute_data": PackedByteArray("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"),
"format": ***********,
"index_count": 396,
"index_data": PackedByteArray("AAABAAIAAwAEAAUABgAHAAgABgAJAAcACgALAAwACgANAAsADgAPABAADgARAA8AEgATABQAEgAVABMAAQAAABYAFwAYABkAFwAaABgABAADABsAGwADABwAHQAeAB8AHQAgAB4AIQAiACMAIQAkACIAJQAmACcAJQAoACYAKQAqACsAKQAsACoAAAACAC0ALgAvADAALgAxAC8AMgAzADQANQA2ADcAOAA5ADoAOAA7ADkAPAA9AD4APAA/AD0AQABBAEIAQABDAEEARABFAEYARABHAEUASABJAEoASABLAEkATABNAE4ATABPAE0ANgA1AFAAUQBSAFMAUQBUAFIAUAA1AFUAVgBXAFgAVgBZAFcAWgBbAFwAWgBdAFsAXgBfAGAAXgBhAF8AYgBjAGQAYgBlAGMAZgBnAGgAZgBpAGcAagAzADIAawBsAG0AawBuAGwAagAyAG8AcABxAHIAcwB0AHUAdgB3AHgAdgB5AHcAegB7AHwAegB9AHsAfgB/AIAAfgCBAH8AggCDAIQAggCFAIMAhgCHAIgAhgCJAIcAigCLAIwAigCNAIsAdABzAI4AjwCQAJEAjwCSAJAAjgBzAJMAlACVAJYAlACXAJUAmACZAJoAmACbAJkAnACdAJ4AnACfAJ0AoAChAKIAoACjAKEApAClAKYApACnAKUAqABxAHAAqQCqAKsAqQCsAKoAqABwAK0ArgCvALAArgCxAK8AsgCzALQAsgC1ALMAtgC3ALgAtgC5ALcAugC7ALwAugC9ALsAvgC/AMAAvgDBAL8AwgDDAMQAwgDFAMMAxgDHAMgAxgDJAMcAygDLAMwAygDNAMsAzgDPANAAzgDRAM8A0gDTANQA0gDVANMA1gDXANgA2QDaANsA3ADdAN4A3ADfAN0A4ADhAOIA4ADjAOEA2gDkAOUA5gDnAOgA5gDpAOcA2gDZAOQA1wDqAOsA6gDXANYA7ADtAO4A7ADvAO0A8ADxAPIA8ADzAPEA9AD1APYA9AD3APUA+AD5APoA+AD7APkA/AD9AP4A/AD/AP0A"),
"material": SubResource("StandardMaterial3D_7n6n0"),
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 256,
"vertex_data": PackedByteArray("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")
}]
blend_shape_mode = 0
shadow_mesh = SubResource("ArrayMesh_a533n")

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_upn87"]
resource_name = "restaurant"
next_pass = ExtResource("3_e3bwi")
cull_mode = 2
diffuse_mode = 3
specular_mode = 2
vertex_color_use_as_albedo = true
albedo_texture = ExtResource("2_4vwye")
roughness = 0.14
backlight_enabled = true
backlight = Color(0.447059, 0.054902, 0.129412, 1)

[sub_resource type="ArrayMesh" id="ArrayMesh_lxuda"]
_surfaces = [{
"aabb": AABB(-0.406289, -0.657308, -0.343106, 0.665336, 1.19531, 0.537969),
"format": 34359742465,
"index_count": 972,
"index_data": PackedByteArray("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"),
"lods": [0.0741526, PackedByteArray("AgAdABsAAgAfAB0AGQACABsAGQAbAC4AAgAHAB8AGQAuACwABwAhAB8AEAAZACwAEAAsACoAEAAVABkAGQAVAAIAFQAAAAIAAgAAAAcAEAAAABUABwAiACEAIgAjACEABwANACIAqgAjACIAqgAkACMArQAkAKoArQAlACQAsQAlAK0AsQAmACUAJwAmALEAJwAoACYAKgAoACcAEQAqACcAEQAQACoAEQAnALAAEQCwAKUAngClALAAngCwAK4AmACeAK4AmACuAKsADQCYAKsADQCrACIAEQASABAApAASABEApACoABIAnwCoAKQAnwChAKgAmQChAJ8AmQCbAKEADQCbAJkADQAOAJsApwAQABIABwAOAA0ApwAPABAADwAAABAAogAPAKcAnAAPAKIABwAMAA4ADgAMAJwAnAAMAA8ABwAAAAwADAAAAA8AJgAkACUAJgAqACQAKAAqACYAIwAkACEAIQAkAJcAlwAkACoAIQCXAB8AHwCXAB0AGwAdAJcAGwCXAC4ALgCXACoALgAqACwAMwBHADcAPwBHADMAPwBPAEcATwA0AEcATwA5ADQARwA0AEAANABIAEAANABBAEgANAA1AEEATgA1ADQATgA7ADUAOQBOADQANwBHAEsARwBAAEsASgA3AEsASgBLAEIAMgBCAEsASQBCADIAMgBLAEAAQQBJADIASABBADIASAAyAEAANQBJAEEAOwBJADUAOwBNAEkATQA2AEkATQA9ADYASQA2AEIANgBKAEIANgA3AEoANgAzADcATAAzADYATAA/ADMAPQBMADYAVABoAFgAYABoAFQAYABwAGgAcABVAGgAcABaAFUAaABVAGEAVQBpAGEAVQBiAGkAVQBWAGIAbwBWAFUAbwBcAFYAWgBvAFUAWABoAGwAaABhAGwAawBYAGwAawBsAGMAagBjAFMAUwBjAGwAYgBqAFMAUwBsAGEAaQBiAFMAaQBTAGEAVgBqAGIAXABqAFYAXABuAGoAbgBXAGoAbgBeAFcAagBXAGMAVwBrAGMAVwBYAGsAVwBUAFgAbQBUAFcAbQBgAFQAXgBtAFcAdQCJAIgAdQB5AIkAgQB1AIgAiACJAIIAgQCIAJEAiACCAHYAkQCIAHYAkQB2AHsAdgCCAIoAdgCKAIMAdgCDAHcAkAB2AHcAewB2AJAAkAB3AH0AeQBxAIkAjABxAHkAiQBxAIIAggBxAI0AigCCAI0AigCNAIMAjACEAHEAjQBxAIQAgwCNAIsAiwCNAIQAdwCDAIsAfQB3AIsAfQCLAI8AjwCLAHgAjwB4AH8AiwCEAHgAeACEAIwAfwB4AI4AjgB4AHUAjgB1AIEAeACMAHkAeAB5AHUAmAANAJoAmACaAKAAmACgAJ4AngCgAKYAngCmAKUApgARAKUAmgANAJkAmgCZAJ8AmgCfAKAAoACfAKQAoACkAKYApAARAKYAmwAOAJ0AowCbAJ0AowChAJsAqQChAKMAqQCoAKEAqQASAKgAnQAOAJwAogCdAJwAogCjAJ0ApwCjAKIApwCpAKMApwASAKkAqgAiAKwArwCqAKwArwCtAKoAsgCtAK8AsgCxAK0AsgAnALEArAAiAKsArgCsAKsArgCvAKwAsACvAK4AsACyAK8AsAAnALIA"), 0.0969474, PackedByteArray("JgAqACQAGwAkACoAIwAkABsApwAVABIApwAMABUAogAMAKcADgAMAKIADgAVAAwAFQAOABsADgAjABsADgAiACMADgANACIAqgAjACIAqgAkACMAsQAkAKoAsQAmACQAJwAmALEAKgAmACcAEQAqACcAEQAnAK4AEQCuAKUAmAClAK4AmACuAKsADQCYAKsADQCrACIADQAOAKgADQCoAJ8AnwCoAKQApACoABIApAASABEAEQASABUAEQAVACoAFQAbACoASQA3AEgASQBKADcAPQBMAEoATAA3AEoATAA/ADcAOwBNAEkATQBKAEkATQA9AEoATgA7AEkAOQBOAEgATgBJAEgAPwBPADcATwBIADcATwA5AEgAagBYAGkAagBrAFgAXgBtAGsAbgBeAGsAbQBgAFgAbQBYAGsAXABuAGoAbgBrAGoAWgBvAGkAbwBqAGkAbwBcAGoAYABwAFgAcABpAFgAcABaAGkAigCJAIMAgwCJAIwAfwCMAI4AjgCMAIEAfQCDAI8AjwCDAIwAjwCMAH8AewCKAJAAkACKAIMAkACDAH0AkQCKAHsAkQCJAIoAgQCJAJEAgQCMAIkAmAANAJoAmACaAKAAmACgAKYAmACmAKUApgARAKUAmgANAJ8AmgCfAKAAoACfAKQAoACkAKYApAARAKYAqAAOAJ0AowCoAJ0AqQCoAKMAqQASAKgAnQAOAKIAogCjAJ0ApwCjAKIApwCpAKMApwASAKkAqgAiAKwArwCqAKwAsgCqAK8AsgCxAKoAsgAnALEArAAiAKsArgCsAKsArgCvAKwArgCyAK8ArgAnALIA"), 0.225509, PackedByteArray("ogAOABIAGwASAA4AnwASABEAnwCoABIADQCoAJ8ADQAOAKgADgANACIADQCuACIADQClAK4AEQCuAKUAEQAnAK4AEQAbACcAEQASABsADgAiABsAsQAbACIAJwAbALEATABJAD0ATAA/AEkATQA9AEkAOwBNAEkAOwBJAE4APwBPAEkATwBOAEkATwA5AE4AbQBqAF4AbgBeAGoAbQBgAGoAXABqAG8AXABuAGoAYABwAGoAcABvAGoAcABaAG8AfQCJAI8AjwCJAI4AjwCOAH8AkACJAH0AkAB7AIkAkQCJAHsAgQCJAJEAgQCOAIkApQANAKYApgARAKUApgANAJ8AnwARAKYAqAAOAKkAqQASAKgAqQAOAKIAogASAKkAsQAiALIAsgAnALEAsgAiAK4ArgAnALIA")],
"name": "restaurant",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 179,
"vertex_data": PackedByteArray("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")
}]
blend_shape_mode = 0

[sub_resource type="ArrayMesh" id="ArrayMesh_4ouo8"]
resource_name = "Carrots_crate_carrots_Cube_001"
lightmap_size_hint = Vector2i(58, 62)
_surfaces = [{
"aabb": AABB(-0.406289, -0.657308, -0.343106, 0.665336, 1.1953, 0.537969),
"attribute_data": PackedByteArray("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"),
"format": ***********,
"index_count": 972,
"index_data": PackedByteArray("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"),
"material": SubResource("StandardMaterial3D_upn87"),
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 367,
"vertex_data": PackedByteArray("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")
}]
blend_shape_mode = 0
shadow_mesh = SubResource("ArrayMesh_lxuda")

[sub_resource type="BoxShape3D" id="BoxShape3D_fn0n5"]
size = Vector3(2.07758, 1.177, 2.0271)

[node name="CarrotBasketProp" instance=ExtResource("1_744fn")]

[node name="FoodHolder" type="MeshInstance3D" parent="Meshes" index="0" groups=["VisibleGroupInterior"]]
transform = Transform3D(1, 0, 0, 0, 0, -1, 0, 1, 0, 0, 0, 0)
mesh = SubResource("ArrayMesh_psvhb")
skeleton = NodePath("")

[node name="Carrots" type="MeshInstance3D" parent="Meshes" index="1" groups=["VisibleGroupInterior"]]
transform = Transform3D(0.594837, 0.803846, 0, 0, 0, -1, -0.803846, 0.594837, 0, 0.182332, 0.353015, -0.353216)
mesh = SubResource("ArrayMesh_4ouo8")
skeleton = NodePath("")

[node name="Carrots2" type="MeshInstance3D" parent="Meshes" index="2" groups=["VisibleGroupInterior"]]
transform = Transform3D(0.901756, 0.344076, -0.261624, -0.10081, -0.421165, -0.901364, -0.420325, 0.839185, -0.345102, -0.325636, 0.440952, -0.0875334)
mesh = SubResource("ArrayMesh_4ouo8")
skeleton = NodePath("")

[node name="Carrots3" type="MeshInstance3D" parent="Meshes" index="3" groups=["VisibleGroupInterior"]]
transform = Transform3D(0.962814, 0.18695, -0.195035, -0.100809, -0.421165, -0.901364, -0.250652, 0.887508, -0.386657, 0.196066, 0.440952, -0.0875334)
mesh = SubResource("ArrayMesh_4ouo8")
skeleton = NodePath("")

[node name="Carrots4" type="MeshInstance3D" parent="Meshes" index="4" groups=["VisibleGroupInterior"]]
transform = Transform3D(-0.118053, 0.78509, -0.608027, -0.0757581, -0.617645, -0.7828, -0.990113, -0.0463492, 0.132392, 0.333663, 0.544232, 0.345369)
mesh = SubResource("ArrayMesh_4ouo8")
skeleton = NodePath("")

[node name="Carrots5" type="MeshInstance3D" parent="Meshes" index="5" groups=["VisibleGroupInterior"]]
transform = Transform3D(0.715761, -0.659967, 0.22832, -0.10081, -0.421165, -0.901365, 0.691031, 0.622145, -0.367984, -0.320487, 0.544232, 0.345369)
mesh = SubResource("ArrayMesh_4ouo8")
skeleton = NodePath("")

[node name="CollisionShape3D" type="CollisionShape3D" parent="StaticBody3D" index="0"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0.000152588, 0.57605, 0.00622559)
shape = SubResource("BoxShape3D_fn0n5")
