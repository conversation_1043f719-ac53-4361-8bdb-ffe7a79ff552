[gd_scene load_steps=4 format=3 uid="uid://b72c0t1el8bq0"]

[ext_resource type="PackedScene" uid="uid://cy82bsxfj1ddv" path="res://prophaunt/scenes/PropObject.tscn" id="1_cxlql"]
[ext_resource type="PackedScene" uid="uid://dphyyqtcioi46" path="res://prophaunt/maps/Source/ArenaProp/rostam_statue.tscn" id="2_clq0m"]

[sub_resource type="CapsuleShape3D" id="CapsuleShape3D_5q4rx"]
radius = 0.721067
height = 4.01372

[node name="RostamStatueProp" instance=ExtResource("1_cxlql")]

[node name="RostamStatue" parent="Meshes" index="0" instance=ExtResource("2_clq0m")]

[node name="CollisionShape3D" type="CollisionShape3D" parent="StaticBody3D" index="0"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 1.8, -0.010301)
shape = SubResource("CapsuleShape3D_5q4rx")
