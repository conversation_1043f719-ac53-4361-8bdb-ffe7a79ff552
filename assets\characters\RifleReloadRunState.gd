class_name RifleReloadRunState
extends Node

@onready var character: Character = $"../.."

var counter = 0
var total_time = 2.0

func _ready() -> void:
	pass

func run(delta):
	if character.controls != Constants.Controls.Player or Constants.is_server:
		return
	
	var input_dir
	counter += delta
	if counter >= total_time:
		# Reload animation finished, return to rifle run or idle based on input
		input_dir = character.player_client_input(delta)
		if input_dir != Vector2(0, 0):
			character.rifleRunState.start_state()
		else:
			character.rifleIdleState.start_state()
		on_reload_finished()
		return
	
	var just_jumped = character.player_client_jump()
	input_dir = character.player_client_input(delta)
	
	# Allow jumping during reload
	if just_jumped:
		character.state = character.State.RIFLE_JUMP
		return
	
	# If player stops moving, switch to stationary reload
	if input_dir == Vector2(0, 0):
		character.rifleReloadState.start_state()
		character.rifleReloadState.counter = counter
		return
	
	character.handle_rifle_mode(delta, true)


func start_state():
	if character.state == character.State.RIFLE_RELOAD_RUN:
		return
	
	character.state = character.State.RIFLE_RELOAD_RUN
	counter = 0
	
	# Get reload animation length
	#var animation_name = character.RIFLE_RELOAD_RUN_ANIMATION
	#if character.animation_player.has_animation(animation_name):
		#total_time = character.animation_player.get_animation(animation_name).length
	#else:
		#total_time = 2.5  # Default reload while running time (slightly longer)
	
	# Animation will be handled in handle_animation function

func end_state():
	counter = 0


func on_reload_finished():
	pass
	 #This can be connected to reload logic (e.g., refill ammo)
	#print("Reload while running finished")
	# You can emit a signal here or call reload logic
