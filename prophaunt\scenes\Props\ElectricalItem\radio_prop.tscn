[gd_scene load_steps=4 format=3 uid="uid://770i1g0h44xe"]

[ext_resource type="PackedScene" uid="uid://cy82bsxfj1ddv" path="res://prophaunt/scenes/PropObject.tscn" id="1_vbcin"]
[ext_resource type="PackedScene" uid="uid://c2hal0dgaw2k2" path="res://prophaunt/maps/Source/ElectricalItem/radio.tscn" id="2_avwxj"]

[sub_resource type="BoxShape3D" id="BoxShape3D_bx0gf"]
size = Vector3(0.728946, 0.524662, 0.221999)

[node name="RadioProp" instance=ExtResource("1_vbcin")]

[node name="radio" parent="Meshes" index="0" instance=ExtResource("2_avwxj")]

[node name="CollisionShape3D" type="CollisionShape3D" parent="StaticBody3D" index="0"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0.0113864, 0.260287, -0.00301552)
shape = SubResource("BoxShape3D_bx0gf")
