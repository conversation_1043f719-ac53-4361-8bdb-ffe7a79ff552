[gd_scene load_steps=4 format=3 uid="uid://oif8x8k0d7lf"]

[ext_resource type="PackedScene" uid="uid://cy82bsxfj1ddv" path="res://prophaunt/scenes/PropObject.tscn" id="1_23x26"]
[ext_resource type="PackedScene" uid="uid://be3u7bnw325rf" path="res://prophaunt/maps/Source/Chairs/chair_desk.tscn" id="2_i1kki"]

[sub_resource type="CapsuleShape3D" id="CapsuleShape3D_cqu0k"]
radius = 0.408674
height = 1.71564

[node name="ChairDeskProp" instance=ExtResource("1_23x26")]

[node name="ChairDesk" parent="Meshes" index="0" instance=ExtResource("2_i1kki")]

[node name="CollisionShape3D" type="CollisionShape3D" parent="StaticBody3D" index="0"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0.865317, 0)
shape = SubResource("CapsuleShape3D_cqu0k")
