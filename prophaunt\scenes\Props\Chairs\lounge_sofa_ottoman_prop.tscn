[gd_scene load_steps=4 format=3 uid="uid://ldn3ulf878pg"]

[ext_resource type="PackedScene" uid="uid://cy82bsxfj1ddv" path="res://prophaunt/scenes/PropObject.tscn" id="1_odwqv"]
[ext_resource type="PackedScene" uid="uid://dgbfuhpwekpsh" path="res://prophaunt/maps/Source/Furniture/lounge_sofa_ottoman.tscn" id="2_gqaq5"]

[sub_resource type="BoxShape3D" id="BoxShape3D_f8ox4"]
size = Vector3(1.79391, 0.952453, 1.83861)

[node name="LoungeSofaOttomanProp" instance=ExtResource("1_odwqv")]

[node name="LoungeSofaOttoman" parent="Meshes" index="0" instance=ExtResource("2_gqaq5")]

[node name="CollisionShape3D" type="CollisionShape3D" parent="StaticBody3D" index="0"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -0.00769898, 0.476227, 0.00704199)
shape = SubResource("BoxShape3D_f8ox4")
