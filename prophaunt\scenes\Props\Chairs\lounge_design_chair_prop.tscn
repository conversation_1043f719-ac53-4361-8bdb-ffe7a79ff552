[gd_scene load_steps=4 format=3 uid="uid://dq0y18vr4klri"]

[ext_resource type="PackedScene" uid="uid://cy82bsxfj1ddv" path="res://prophaunt/scenes/PropObject.tscn" id="1_ckw1w"]
[ext_resource type="PackedScene" uid="uid://o2opl0c10tw7" path="res://prophaunt/maps/Source/Furniture/lounge_design_chair.tscn" id="2_rtf3i"]

[sub_resource type="BoxShape3D" id="BoxShape3D_f8ox4"]
size = Vector3(2.95688, 1.58685, 1.61058)

[node name="LoungeDesignChairProp" instance=ExtResource("1_ckw1w")]

[node name="LoungeDesignChair" parent="Meshes" index="0" instance=ExtResource("2_rtf3i")]

[node name="CollisionShape3D" type="CollisionShape3D" parent="StaticBody3D" index="0"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0.00347289, 0.793426, 0.0429307)
shape = SubResource("BoxShape3D_f8ox4")
