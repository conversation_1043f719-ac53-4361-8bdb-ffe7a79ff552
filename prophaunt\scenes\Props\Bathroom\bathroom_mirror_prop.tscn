[gd_scene load_steps=4 format=3 uid="uid://b3qu2lx8kspna"]

[ext_resource type="PackedScene" uid="uid://cy82bsxfj1ddv" path="res://prophaunt/scenes/PropObject.tscn" id="1_kg8kn"]
[ext_resource type="PackedScene" uid="uid://wxixfpp5qwyt" path="res://prophaunt/maps/Source/Bathroom/bathroom_mirror.tscn" id="2_is5nn"]

[sub_resource type="BoxShape3D" id="BoxShape3D_f8ox4"]
size = Vector3(1.36793, 1.97318, 0.667179)

[node name="BathroomCabinetProp" instance=ExtResource("1_kg8kn")]

[node name="bathroomMirror" parent="Meshes" index="0" instance=ExtResource("2_is5nn")]

[node name="CollisionShape3D" type="CollisionShape3D" parent="StaticBody3D" index="0"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -0.00188733, 0.986592, 0.319975)
shape = SubResource("BoxShape3D_f8ox4")
