[gd_scene load_steps=5 format=3 uid="uid://b6ast582o83jc"]

[ext_resource type="PackedScene" uid="uid://cy82bsxfj1ddv" path="res://prophaunt/scenes/PropObject.tscn" id="1_u0h1l"]
[ext_resource type="PackedScene" uid="uid://crsygjh44810o" path="res://prophaunt/maps/Source/Table/table_round.tscn" id="2_fjbyx"]

[sub_resource type="BoxShape3D" id="BoxShape3D_yjq6c"]
size = Vector3(0.336639, 1.28235, 0.348114)

[sub_resource type="ConcavePolygonShape3D" id="ConcavePolygonShape3D_f558j"]
data = PackedVector3Array(0, 0.1, 1.6, 1.3856, 0.1, 0.8, 0, -0.1, 1.6, 1.3856, 0.1, 0.8, 1.3856, -0.1, 0.8, 0, -0.1, 1.6, 1.3856, 0.1, 0.8, 1.3856, 0.1, -0.8, 1.3856, -0.1, 0.8, 1.3856, 0.1, -0.8, 1.3856, -0.1, -0.8, 1.3856, -0.1, 0.8, 1.3856, 0.1, -0.8, 0, 0.1, -1.6, 1.3856, -0.1, -0.8, 0, 0.1, -1.6, 0, -0.1, -1.6, 1.3856, -0.1, -0.8, 0, 0.1, -1.6, -1.3856, 0.1, -0.8, 0, -0.1, -1.6, -1.3856, 0.1, -0.8, -1.3856, -0.1, -0.8, 0, -0.1, -1.6, -1.3856, 0.1, -0.8, -1.3856, 0.1, 0.8, -1.3856, -0.1, -0.8, -1.3856, 0.1, 0.8, -1.3856, -0.1, 0.8, -1.3856, -0.1, -0.8, -1.3856, 0.1, 0.8, 0, 0.1, 1.6, -1.3856, -0.1, 0.8, 0, 0.1, 1.6, 0, -0.1, 1.6, -1.3856, -0.1, 0.8, 0, 0.1, 0, 1.3856, 0.1, 0.8, 0, 0.1, 1.6, 0, 0.1, 0, 1.3856, 0.1, -0.8, 1.3856, 0.1, 0.8, 0, 0.1, 0, 0, 0.1, -1.6, 1.3856, 0.1, -0.8, 0, 0.1, 0, -1.3856, 0.1, -0.8, 0, 0.1, -1.6, 0, 0.1, 0, -1.3856, 0.1, 0.8, -1.3856, 0.1, -0.8, 0, 0.1, 0, 0, 0.1, 1.6, -1.3856, 0.1, 0.8, 0, -0.1, 0, 0, -0.1, 1.6, 1.3856, -0.1, 0.8, 0, -0.1, 0, 1.3856, -0.1, 0.8, 1.3856, -0.1, -0.8, 0, -0.1, 0, 1.3856, -0.1, -0.8, 0, -0.1, -1.6, 0, -0.1, 0, 0, -0.1, -1.6, -1.3856, -0.1, -0.8, 0, -0.1, 0, -1.3856, -0.1, -0.8, -1.3856, -0.1, 0.8, 0, -0.1, 0, -1.3856, -0.1, 0.8, 0, -0.1, 1.6)

[node name="TableRoundProp" instance=ExtResource("1_u0h1l")]

[node name="TableRound" parent="Meshes" index="0" instance=ExtResource("2_fjbyx")]

[node name="CollisionShape3D2" type="CollisionShape3D" parent="StaticBody3D" index="0"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -0.000778183, 0.638245, 0.00273133)
shape = SubResource("BoxShape3D_yjq6c")

[node name="CollisionShape3D" type="CollisionShape3D" parent="StaticBody3D" index="1"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 1.36246, 0)
shape = SubResource("ConcavePolygonShape3D_f558j")
