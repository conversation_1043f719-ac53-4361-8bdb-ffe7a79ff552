[gd_scene load_steps=4 format=3 uid="uid://b70fl1pkeooov"]

[ext_resource type="PackedScene" uid="uid://cy82bsxfj1ddv" path="res://prophaunt/scenes/PropObject.tscn" id="1_pl51n"]
[ext_resource type="PackedScene" uid="uid://c46qo5vv2mixl" path="res://prophaunt/maps/Source/Bathroom/bathroom_cabinet.tscn" id="2_iad68"]

[sub_resource type="BoxShape3D" id="BoxShape3D_f8ox4"]
size = Vector3(1.15654, 1.95366, 0.747763)

[node name="BathroomCabinetProp" instance=ExtResource("1_pl51n")]

[node name="bathroomCabinet" parent="Meshes" index="0" instance=ExtResource("2_iad68")]

[node name="CollisionShape3D" type="CollisionShape3D" parent="StaticBody3D" index="0"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0.00203395, 0.976831, 0.0797378)
shape = SubResource("BoxShape3D_f8ox4")
