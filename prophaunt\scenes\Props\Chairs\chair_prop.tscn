[gd_scene load_steps=4 format=3 uid="uid://bphfjxdfe0pd0"]

[ext_resource type="PackedScene" uid="uid://cy82bsxfj1ddv" path="res://prophaunt/scenes/PropObject.tscn" id="1_p3wca"]
[ext_resource type="PackedScene" uid="uid://jkq232ok6y1x" path="res://prophaunt/maps/Source/Chairs/chair.tscn" id="2_khp06"]

[sub_resource type="BoxShape3D" id="BoxShape3D_f8ox4"]
size = Vector3(0.80333, 1.87952, 0.812775)

[node name="ChairProp" instance=ExtResource("1_p3wca")]

[node name="Chair" parent="Meshes" index="0" instance=ExtResource("2_khp06")]

[node name="CollisionShape3D" type="CollisionShape3D" parent="StaticBody3D" index="0"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0.00229645, 0.939758, 0.00508115)
shape = SubResource("BoxShape3D_f8ox4")
