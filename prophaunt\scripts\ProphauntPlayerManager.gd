extends Node
class_name ProphauntPlayerManager

# Manages Prophaunt-specific player functionality
# Singleton that handles all Prophaunt players in the game

var prophaunt_players: Dictionary = {}  # player_id -> ProphauntPlayer
var team_assignments: Dictionary = {}   # player_id -> team

signal player_team_assigned(player_id: int, team: Constants.ProphauntTeam)
signal player_eliminated(player_id: int)
signal player_disguised(player_id: int, disguise: String)
signal player_hexed(player_id: int, duration: float)

func _ready():
	# Connect to multiplayer signals if needed
	pass

func initialize_prophaunt_player(player_id: int, character: Character, team: Constants.ProphauntTeam):
	"""Initialize a player for Prophaunt mode"""
	
	if character.prophaunt_player != null:
		return

	# Create ProphauntPlayer component
	var prophaunt_player = ProphauntPlayer.new()
	prophaunt_player.name = "ProphauntPlayer_" + str(player_id)
	
	# Add to character
	character.add_child(prophaunt_player)
	prophaunt_player.initialize(character, team)
	character.prophaunt_player = prophaunt_player
	
	# Store references
	prophaunt_players[player_id] = prophaunt_player
	team_assignments[player_id] = team
	
	# Connect signals
	prophaunt_player.team_assigned.connect(_on_player_team_assigned.bind(player_id))
	prophaunt_player.hp_changed.connect(_on_player_hp_changed.bind(player_id))
	prophaunt_player.state_changed.connect(_on_player_state_changed.bind(player_id))
	prophaunt_player.disguise_changed.connect(_on_player_disguise_changed.bind(player_id))
	
	player_team_assigned.emit(player_id, team)
	
	if team == Constants.ProphauntTeam.HAUNTERS:
		character.rifleIdleState.start_state()
	else:
		character.set_state_to_idle(false)
		character.slide_enabled = false
	
	#print("Initialized Prophaunt player ", player_id, " as ", "Haunter" if team == Constants.ProphauntTeam.HAUNTERS else "Prop")


func remove_prophaunt_player(player_id: int):
	"""Remove a player from Prophaunt mode"""
	if player_id in prophaunt_players:
		var prophaunt_player = prophaunt_players[player_id]
		if is_instance_valid(prophaunt_player):
			prophaunt_player.queue_free()
		
		prophaunt_players.erase(player_id)
		team_assignments.erase(player_id)

func get_prophaunt_player(player_id: int) -> ProphauntPlayer:
	"""Get the ProphauntPlayer component for a player"""
	return prophaunt_players.get(player_id, null)

func get_player_team(player_id: int) -> Constants.ProphauntTeam:
	"""Get the team assignment for a player"""
	return team_assignments.get(player_id, -1)

func get_players_by_team(team: Constants.ProphauntTeam) -> Array:
	"""Get all players on a specific team"""
	var team_players = []
	for player_id in team_assignments.keys():
		if team_assignments[player_id] == team:
			team_players.append(player_id)
	return team_players

func get_alive_props() -> Array:
	"""Get all alive prop players"""
	var alive_props = []
	var prop_players = get_players_by_team(Constants.ProphauntTeam.PROPS)
	
	for player_id in prop_players:
		var prophaunt_player = get_prophaunt_player(player_id)
		if prophaunt_player and prophaunt_player.player_state == Constants.ProphauntPlayerState.ALIVE:
			alive_props.append(player_id)
	
	return alive_props

func damage_player(player_id: int, damage: int):
	"""Damage a player"""
	var prophaunt_player = get_prophaunt_player(player_id)
	if prophaunt_player:
		prophaunt_player.take_damage(damage)

func hex_player(player_id: int, duration: float):
	"""Apply hex to a player"""
	var prophaunt_player = get_prophaunt_player(player_id)
	if prophaunt_player:
		prophaunt_player.apply_hex(duration)
		player_hexed.emit(player_id, duration)

func change_player_disguise(player_id: int, disguise: PropObject):
	"""Change a player's disguise"""
	var prophaunt_player = get_prophaunt_player(player_id)
	if prophaunt_player:
		prophaunt_player.apply_disguise(disguise)


func reset_all_players():
	"""Reset all players for a new round"""
	for player_id in prophaunt_players.keys():
		var prophaunt_player = prophaunt_players[player_id]
		if prophaunt_player:
			prophaunt_player.hp = Constants.PROPHAUNT_PROP_DEFAULT_HP
			prophaunt_player.player_state = Constants.ProphauntPlayerState.ALIVE
			prophaunt_player.hex_cooldown = 0.0
			prophaunt_player.sound_cooldown = Constants.PROPHAUNT_PROP_SOUND_COOLDOWN


func clear_all_players():
	"""Clear all Prophaunt players"""
	for player_id in prophaunt_players.keys():
		remove_prophaunt_player(player_id)

# Signal handlers
func _on_player_team_assigned(player_id: int, team: Constants.ProphauntTeam):
	"""Called when a player is assigned to a team"""
	print("Player ", player_id, " assigned to team: ", "Haunters" if team == Constants.ProphauntTeam.HAUNTERS else "Props")

func _on_player_hp_changed(player_id: int, new_hp: int):
	"""Called when a player's HP changes"""
	print("Player ", player_id, " HP changed to: ", new_hp)
	
	if new_hp <= 0:
		player_eliminated.emit(player_id)

func _on_player_state_changed(player_id: int, new_state: Constants.ProphauntPlayerState):
	"""Called when a player's state changes"""
	var state_name = ""
	match new_state:
		Constants.ProphauntPlayerState.ALIVE:
			state_name = "Alive"
		Constants.ProphauntPlayerState.DEAD:
			state_name = "Dead"
		Constants.ProphauntPlayerState.HEXED:
			state_name = "Hexed"
	
	print("Player ", player_id, " state changed to: ", state_name)

func _on_player_disguise_changed(player_id: int, disguise: String):
	"""Called when a player changes disguise"""
	#print("Player ", player_id, " disguised as: ", disguise)
	player_disguised.emit(player_id, disguise)

# Utility functions for game logic
func are_all_props_dead() -> bool:
	"""Check if all props are dead"""
	var alive_props = get_alive_props()
	return alive_props.size() == 0

func get_team_count(team: Constants.ProphauntTeam) -> int:
	"""Get the number of players on a team"""
	return get_players_by_team(team).size()

func get_alive_prop_count() -> int:
	"""Get the number of alive props"""
	return get_alive_props().size()

func get_total_prop_count() -> int:
	"""Get the total number of props"""
	return get_team_count(Constants.ProphauntTeam.PROPS)

func get_haunter_count() -> int:
	"""Get the number of haunters"""
	return get_team_count(Constants.ProphauntTeam.HAUNTERS)

# Input handling
func _input(_event):
	"""Handle global Prophaunt input"""
	if Constants.game_mode != Constants.GameMode.Prophaunt:
		return
	
	# Handle input for the local player
	var local_player_id = multiplayer.get_unique_id()
	var prophaunt_player = get_prophaunt_player(local_player_id)
	
	if prophaunt_player:
		prophaunt_player.handle_prophaunt_input()

# Static instance for singleton access
static var instance: ProphauntPlayerManager

static func get_instance() -> ProphauntPlayerManager:
	if not instance:
		instance = ProphauntPlayerManager.new()
		instance.name = "ProphauntPlayerManager"
	return instance
