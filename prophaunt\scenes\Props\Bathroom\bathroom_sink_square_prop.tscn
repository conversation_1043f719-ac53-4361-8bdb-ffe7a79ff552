[gd_scene load_steps=4 format=3 uid="uid://dv1qh7fdw6ydc"]

[ext_resource type="PackedScene" uid="uid://cy82bsxfj1ddv" path="res://prophaunt/scenes/PropObject.tscn" id="1_jyei4"]
[ext_resource type="PackedScene" uid="uid://kq8eyd7mckof" path="res://prophaunt/maps/Source/Bathroom/bathroom_sink_square.tscn" id="2_pijps"]

[sub_resource type="BoxShape3D" id="BoxShape3D_f8ox4"]
size = Vector3(1.14426, 1.51393, 0.813279)

[node name="BathroomSinkSquareProp" instance=ExtResource("1_jyei4")]

[node name="BathroomSinkSquare" parent="Meshes" index="0" instance=ExtResource("2_pijps")]

[node name="CollisionShape3D" type="CollisionShape3D" parent="StaticBody3D" index="0"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -0.00205611, 0.756965, 0.215873)
shape = SubResource("BoxShape3D_f8ox4")
