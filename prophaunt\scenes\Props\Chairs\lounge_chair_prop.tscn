[gd_scene load_steps=4 format=3 uid="uid://bmm7khjgjwuv8"]

[ext_resource type="PackedScene" uid="uid://cy82bsxfj1ddv" path="res://prophaunt/scenes/PropObject.tscn" id="1_xwdcv"]
[ext_resource type="PackedScene" uid="uid://u2mxl05bleh" path="res://prophaunt/maps/Source/Furniture/lounge_chair.tscn" id="2_bou0c"]

[sub_resource type="BoxShape3D" id="BoxShape3D_f8ox4"]
size = Vector3(1.9689, 1.85068, 1.58287)

[node name="LoungeChairProp" instance=ExtResource("1_xwdcv")]

[node name="LoungeChair" parent="Meshes" index="0" instance=ExtResource("2_bou0c")]

[node name="CollisionShape3D" type="CollisionShape3D" parent="StaticBody3D" index="0"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 2.44975e-05, 0.925339, -0.0280532)
shape = SubResource("BoxShape3D_f8ox4")
