[gd_scene load_steps=5 format=4 uid="uid://crb6mpy8sn7o7"]

[ext_resource type="Material" uid="uid://dkmcjc7wc3ws6" path="res://prophaunt/Mat/GrassPath.tres" id="1_wsijx"]

[sub_resource type="ArrayMesh" id="ArrayMesh_1dsbf"]
_surfaces = [{
"aabb": AABB(-1, -0.1, -1, 2, 0.1, 2),
"format": 34896613377,
"index_count": 204,
"index_data": PackedByteArray("AgAAAAEAAQADAAIAAQAEAAMABAAFAAMABAAGAAUABgAHAAUABgAIAAcACAAJAAcACAAKAAkACgALAAkADgAMAA0ADQAPAA4ADQAQAA8AEAARAA8AEAASABEAEAATABIAEAAUABMAFAAVABMAFAAWABUAFgAXABUAFgAYABcAGAAZABcAHAAaABsAGwAdABwAHQAeABwAHQAfAB4AHwAgAB4AHwAhACAAJAAiACMAIwAlACQAJQAmACQAJQAjACcAJQAnACgAKAApACUAEAAFAAcABwAUABAADQADAAUABQAQAA0ADAACAAMAAwANAAwAFAAHAAkACQAWABQAFgAJAAsACwAYABYAKQAOAA8ADwAlACkAEgAkACYAJgARABIAHwAdABcAFwAZAB8AGgATABUAFQAbABoAGwAVABcAFwAdABsAJQAPABEAEQAmACUAKgAoACcAJwAiACoAJwAjACIALAArAAQABAAAACwABAABAAAAKwAGAAQAKwAIAAYAKwAKAAgALQAcAB4AHgAgAC0AIAAhAC0A"),
"lods": [0.129165, PackedByteArray("AgAAACsAKwAAACwAKwAKAAIACgALAAIACwAYABAAEAACAAsAAgAQAAwADgAMABAAEAAPAA4AEAASAA8AEAATABIAEAAXABMAEAAYABcAGAAZABcAGgAfABwAHwAhABwALQAcACEAFwAfABoAFwAZAB8AGgATABcAIgApACQAKQAiACgAKgAoACIAKQAPACQAEgAkAA8AKQAOAA8A")],
"name": "Material",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 46,
"vertex_data": PackedByteArray("+R7+////AAD5Hv7//98AAJM4/v///wAAkzj+///fAAAaD/7//58AALQo/v//nwAAGg/+//9fAAC0KP7//18AAPke/v//HwAAkzj+//8fAAD5Hv7/AAAAAJM4/v8AAAAAckgAAP//AABySAAA/98AAIy3AAD//wAAjLcAAP/fAACTOAAA/58AAP/fAACMtwAA//8AAIy3AAD//wAAckgAAJM4AAD/XwAA/98AAHJIAABySAAA/x8AAIy3AAD/HwAAckgAAAAAAACMtwAAAAAAAP///v+TOAAA/9/+/5M4AAD///7/+R4AAGvH/v//HwAAgvD+//keAABrx/7/AAAAAAXh/v98DwAABeH+/wAAAAD///7/BeEAAILw/v8F4QAA///+/2vHAABrx/7//98AAP/f/v9rxwAABeH+/4LwAAAF4f7///8AAGvH/v///wAA///+////AAAAAP//AAAAAAAA/////wAA///+/wAAAAA=")
}]
blend_shape_mode = 0

[sub_resource type="ArrayMesh" id="ArrayMesh_mng3x"]
resource_name = "GroundPathSplit_GroundPathSplit"
_surfaces = [{
"aabb": AABB(-1, -0.1, -1, 2, 0.1, 2),
"attribute_data": PackedByteArray("m5hPgpGYuJE4jFaCOIxWglSMt5FUjLeRVIy3kSigubCck6awnJOmsJyTprAroALQrZMM0K2TDNCtkwzQlJgQ72CME+9gjBPvYIwT74aYff4ljGL+JYxi/tRanYFOgneC1FoqkZGC/JGRgvyR2SSdgSv7rIXZJCqRvvctt773LbeKYkKwuYnYsLmJ2LBRvHv4MhHRpFG8e/i4hoT+pgHRpFb7hP6mAczaimJbz7aJ2c+2idnPz+Gs+zIRzNrP4az71Fpz7pmC0+6ZgtPu2SRz7gLDkNsCw5Db1Fr//VaCOP7ZJP/99sDPwSb9QO4m/UDuX+T961/k/etf5P3rav4Q2ejSRNno0kTZ6NJE2THy5thg0YHAYNGBwNfltMzM5WHApoJRs/ua9bK1g+jbtYPo28TYnLPE2JyzxNics+q1KdnqtSnZ6rUp2dOyk5oqs1CCoNt8g6DbfINQglCCf6eF/n+nUoKD/mHAOIxWgiWMYv6+9y23AsOQ2yb9QO61g+jb"),
"format": 34896613399,
"index_count": 204,
"index_data": PackedByteArray("AgAAAAEAAQAEAAIAAQAHAAQABwAIAAQABwALAAgACwAMAAgACwAPAAwADwAQAAwADwATABAAEwAUABAAGwAWABgAGAAdABsAGAAgAB0AIAAkAB0AIAAnACQAIAApACcAIAAqACkAKgAuACkAKgAwAC4AMAAzAC4AMAA2ADMANgA4ADMAPwA7AD0APQBAAD8AQABDAD8AQABEAEMARABGAEMARABHAEYASwBIAEkASQBMAEsATABQAEsATABJAFIATABSAFMAUwBUAEwAIgAKAA4ADgAsACIAGQAFAAkACQAhABkAFwADAAYABgAaABcAKwANABEAEQAxACsAMgASABUAFQA3ADIAVQAcAB8AHwBOAFUAJgBKAE8ATwAjACYARQBCADUANQA5AEUAOgAoAC0ALQA8ADoAPgAvADQANABBAD4ATQAeACUAJQBRAE0AVgBTAFIAUgBIAFYAUgBJAEgAWABXAAcABwAAAFgABwABAAAAVwALAAcAVwAPAAsAVwATAA8AWQA/AEMAQwBGAFkARgBHAFkA"),
"lods": [0.129165, PackedByteArray("WgAAAFcAVwAAAFgAVwATAFoAEwBbAFoAWwA3ACEAIQBaABQAWgAhABcAGwAWACAAIAAdABsAIAAnAB0AIAApACcAIAAzACkAIAA2ADMANgA4ADMAXgBEAD8ARABHAD8AWQA/AEcAXQBEAF4AXQA5AEQAXgAoAF0ASABUAF8AVABIAFMAVgBTAEgAVABcAF8AJgBfAFwAVAAcAFwA")],
"material": ExtResource("1_wsijx"),
"name": "Material",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 96,
"vertex_data": PackedByteArray("+R7+////+//5Hv7//9+u/5M4/v////f/kzj+////QuyTOP7//9/3/5M4/v//34Ltkzj+///fA+waD/7//5+f/7Qo/v//n9v/tCj+//+fqO20KP7//58V7BoP/v//Xy//tCj+//9f6f+0KP7//1/I67Qo/v//X/vs+R7+//8f8/+TOP7//x/0/5M4/v//H+jrkzj+//8fWe35Hv7/AADm/5M4/v8AAL7/kzj+/wAAMe1ySAAA/////3JIAAD//x7sckgAAP/f//9ySAAA/9+M7XJIAAD/39/rjLcAAP////+MtwAA//+16oy3AAD/3///jLcAAP/fnvCMtwAA/9/h6ZM4AAD/n///kzgAAP+fsu2TOAAA/59N7P/fAACMt9n9/98AAIy3////3wAAjLdd7///AACMt6f+//8AAIy3/////wAAckiapP//AABySP//kzgAAP9f//+TOAAA/1/P65M4AAD/XzPt/98AAHJIjKT/3wAAckj////fAABySEitckgAAP8f//9ySAAA/x/v63JIAAD/H0PtjLcAAP8f//+MtwAA/x+wrYy3AAD/HxbDckgAAAAA//9ySAAAAAAb7Yy3AAAAAP//jLcAAAAAjcP///7/kziSpP///v+TOBLA/9/+/5M4haT/3/7/kzgCwP/f/v+TOGKt///+//keAsBrx/7//x//v2vH/v//H+yta8f+//8f1cKC8P7/+R7/v2vH/v8AAP+/a8f+/wAAScMF4f7/fA//vwXh/v8AAP+////+/wXhrP+C8P7/BeGR/////v9rxxv////+/2vHPP9rx/7//9+3/2vH/v//30zwa8f+///faun/3/7/a8dN/v/f/v9rx0n+/9/+/2vHq+4F4f7/gvCh/wXh/v///9//a8f+////Hv9rx/7///896v///v///+X/AAD//wAA9v8AAP/////n/////v8AAP+/kzj+////R/2TOP7/AAD//4y3AAD/3xzujLcAAP8fvrr///7/kzgZuv///v9rx///AoD9v1iA0r8DwAcArjbkEgeA+7+POsUT3javEmiAyr8mgOu/bTrqE9A2vxJxwOQAC8AXAGEyXRIiNn0TBcAMAAqA+b9MMnQS2zXKExqA8b9GgNu/+TWpE/9//7/JNsYS/3//v4Y6zxP5NpIS/3//v2tu4bf/f/+/tmtgxiJvibj/f/+/ZDr0E6U27RLYrBUF/3//v8ttVsiWqzkD/3//v+/5Qm3/f/+//3//v1wyYhL4NasThfoRb/9//79U4TK+/3//v0cyeRLsNbgT/3//v2XiZr6R4KS1/3//vwo2lxP/f/+/S+JotUP6Rm74+vN13PoecOb9zXub4T++5/3Qeyf/T3774oO+lt/GtWD/wn6h/0R/UuGKtc7/F4CY/zKALcBbADvAeQDbqiYCasDWACfATwA9bN/Gim/puCOsCgTuwN8B/m53yTPAZwAigO2/9oCDv9JuQLgNwBwAA8AJABmA8r/X/69/T2tkFAVslhZHc5G8F+b1sN7+p3W4VuH5")
}]
blend_shape_mode = 0
shadow_mesh = SubResource("ArrayMesh_1dsbf")

[sub_resource type="ConcavePolygonShape3D" id="ConcavePolygonShape3D_upijb"]
data = PackedVector3Array(-0.558, 0, 1, -0.758, 0, 1, -0.758, 0, 0.75, -0.758, 0, 0.75, -0.558, 0, 0.75, -0.558, 0, 1, -0.758, 0, 0.75, -0.882, 0, 0.25, -0.558, 0, 0.75, -0.882, 0, 0.25, -0.682, 0, 0.25, -0.558, 0, 0.75, -0.882, 0, 0.25, -0.882, 0, -0.25, -0.682, 0, 0.25, -0.882, 0, -0.25, -0.682, 0, -0.25, -0.682, 0, 0.25, -0.882, 0, -0.25, -0.758, 0, -0.75, -0.682, 0, -0.25, -0.758, 0, -0.75, -0.558, 0, -0.75, -0.682, 0, -0.25, -0.758, 0, -0.75, -0.758, 0, -1, -0.558, 0, -0.75, -0.758, 0, -1, -0.558, 0, -1, -0.558, 0, -0.75, 0.434, -0.1, 1, -0.434, -0.1, 1, -0.434, -0.1, 0.75, -0.434, -0.1, 0.75, 0.434, -0.1, 0.75, 0.434, -0.1, 1, -0.434, -0.1, 0.75, -0.558, -0.1, 0.25, 0.434, -0.1, 0.75, -0.558, -0.1, 0.25, 0.75, -0.1, 0.434, 0.434, -0.1, 0.75, -0.558, -0.1, 0.25, 1, -0.1, 0.434, 0.75, -0.1, 0.434, -0.558, -0.1, 0.25, 1, -0.1, -0.434, 1, -0.1, 0.434, -0.558, -0.1, 0.25, -0.558, -0.1, -0.25, 1, -0.1, -0.434, -0.558, -0.1, -0.25, 0.75, -0.1, -0.434, 1, -0.1, -0.434, -0.558, -0.1, -0.25, -0.434, -0.1, -0.75, 0.75, -0.1, -0.434, -0.434, -0.1, -0.75, 0.434, -0.1, -0.75, 0.75, -0.1, -0.434, -0.434, -0.1, -0.75, -0.434, -0.1, -1, 0.434, -0.1, -0.75, -0.434, -0.1, -1, 0.434, -0.1, -1, 0.434, -0.1, -0.75, 1, 0, -0.758, 1, 0, -0.558, 0.75, 0, -0.558, 0.75, 0, -0.558, 0.558, 0, -0.75, 1, 0, -0.758, 0.558, 0, -0.75, 0.879, 0, -0.758, 1, 0, -0.758, 0.558, 0, -0.75, 0.558, 0, -1, 0.879, 0, -0.758, 0.558, 0, -1, 0.758, 0, -0.879, 0.879, 0, -0.758, 0.558, 0, -1, 0.758, 0, -1, 0.758, 0, -0.879, 1, 0, 0.558, 1, 0, 0.758, 0.879, 0, 0.758, 0.879, 0, 0.758, 0.558, 0, 0.75, 1, 0, 0.558, 0.558, 0, 0.75, 0.75, 0, 0.558, 1, 0, 0.558, 0.558, 0, 0.75, 0.879, 0, 0.758, 0.758, 0, 0.879, 0.558, 0, 0.75, 0.758, 0, 0.879, 0.758, 0, 1, 0.758, 0, 1, 0.558, 0, 1, 0.558, 0, 0.75, -0.558, -0.1, 0.25, -0.682, 0, 0.25, -0.682, 0, -0.25, -0.682, 0, -0.25, -0.558, -0.1, -0.25, -0.558, -0.1, 0.25, -0.434, -0.1, 0.75, -0.558, 0, 0.75, -0.682, 0, 0.25, -0.682, 0, 0.25, -0.558, -0.1, 0.25, -0.434, -0.1, 0.75, -0.434, -0.1, 1, -0.558, 0, 1, -0.558, 0, 0.75, -0.558, 0, 0.75, -0.434, -0.1, 0.75, -0.434, -0.1, 1, -0.558, -0.1, -0.25, -0.682, 0, -0.25, -0.558, 0, -0.75, -0.558, 0, -0.75, -0.434, -0.1, -0.75, -0.558, -0.1, -0.25, -0.434, -0.1, -0.75, -0.558, 0, -0.75, -0.558, 0, -1, -0.558, 0, -1, -0.434, -0.1, -1, -0.434, -0.1, -0.75, 0.558, 0, 1, 0.434, -0.1, 1, 0.434, -0.1, 0.75, 0.434, -0.1, 0.75, 0.558, 0, 0.75, 0.558, 0, 1, 1, -0.1, 0.434, 1, 0, 0.558, 0.75, 0, 0.558, 0.75, 0, 0.558, 0.75, -0.1, 0.434, 1, -0.1, 0.434, 0.558, 0, -1, 0.558, 0, -0.75, 0.434, -0.1, -0.75, 0.434, -0.1, -0.75, 0.434, -0.1, -1, 0.558, 0, -1, 1, 0, -0.558, 1, -0.1, -0.434, 0.75, -0.1, -0.434, 0.75, -0.1, -0.434, 0.75, 0, -0.558, 1, 0, -0.558, 0.75, 0, -0.558, 0.75, -0.1, -0.434, 0.434, -0.1, -0.75, 0.434, -0.1, -0.75, 0.558, 0, -0.75, 0.75, 0, -0.558, 0.558, 0, 0.75, 0.434, -0.1, 0.75, 0.75, -0.1, 0.434, 0.75, -0.1, 0.434, 0.75, 0, 0.558, 0.558, 0, 0.75, 1, 0, 1, 0.758, 0, 1, 0.758, 0, 0.879, 0.758, 0, 0.879, 1, 0, 0.758, 1, 0, 1, 0.758, 0, 0.879, 0.879, 0, 0.758, 1, 0, 0.758, -1, 0, 1, -1, 0, -1, -0.882, 0, 0.25, -0.882, 0, 0.25, -0.758, 0, 1, -1, 0, 1, -0.882, 0, 0.25, -0.758, 0, 0.75, -0.758, 0, 1, -1, 0, -1, -0.882, 0, -0.25, -0.882, 0, 0.25, -1, 0, -1, -0.758, 0, -0.75, -0.882, 0, -0.25, -1, 0, -1, -0.758, 0, -1, -0.758, 0, -0.75, 1, 0, -1, 1, 0, -0.758, 0.879, 0, -0.758, 0.879, 0, -0.758, 0.758, 0, -0.879, 1, 0, -1, 0.758, 0, -0.879, 0.758, 0, -1, 1, 0, -1)

[node name="GroundPathSplit" type="MeshInstance3D"]
mesh = SubResource("ArrayMesh_mng3x")
skeleton = NodePath("")

[node name="StaticBody3D" type="StaticBody3D" parent="."]

[node name="CollisionShape3D" type="CollisionShape3D" parent="StaticBody3D"]
shape = SubResource("ConcavePolygonShape3D_upijb")
