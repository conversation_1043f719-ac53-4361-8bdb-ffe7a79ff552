[gd_scene load_steps=4 format=3 uid="uid://brb86o57alhkm"]

[ext_resource type="Script" path="res://prophaunt/scenes/PropObject.gd" id="1_8u5fl"]
[ext_resource type="PackedScene" uid="uid://drjl37abvjmxq" path="res://prophaunt/maps/Source/Item/book.tscn" id="2_d5pj6"]

[sub_resource type="BoxShape3D" id="BoxShape3D_qqs5e"]
size = Vector3(0.0723529, 0.295124, 0.253082)

[node name="BookProp" type="Node3D" node_paths=PackedStringArray("export_mesh") groups=["PropObject"]]
script = ExtResource("1_8u5fl")
export_mesh = NodePath("Meshes")

[node name="Meshes" type="Node3D" parent="."]

[node name="Book" parent="Meshes" instance=ExtResource("2_d5pj6")]

[node name="StaticBody3D" type="StaticBody3D" parent="."]
collision_layer = 2

[node name="CollisionShape3D" type="CollisionShape3D" parent="StaticBody3D"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -0.00013806, 0.144937, -0.00221248)
shape = SubResource("BoxShape3D_qqs5e")
