[gd_scene load_steps=4 format=4 uid="uid://e0s3xtsa6rkw"]

[ext_resource type="Material" uid="uid://b6gd8jjb1kopc" path="res://prophaunt/Mat/Colormap2.tres" id="1_1k278"]

[sub_resource type="ArrayMesh" id="ArrayMesh_fj4hh"]
_surfaces = [{
"aabb": AABB(-1.57, -6.75954e-08, -0.92, 3.14, 0.0200001, 1.84),
"format": 34359742465,
"index_count": 84,
"index_data": PackedByteArray("AQACAAAAAgABAAMAAgAEAAAABAACAAUACAAJAAoACwAEAAUACwAFAAoACAACAAMAAgAIAAUADQAOAAwADgANAA8ADAAGAAsABgAMAAkACQAMAA4ACQAOAA8ACwANAAwADQALAAoADQAKAA8ADwAKAAkABgAHAAQABwAGAAgACAAGAAkABAALAAYACgAFAAgAAQAEAAcABAABAAAAAQAIAAMACAABAAcA"),
"lods": [0.960776, PackedByteArray("AQACAAAAAgABAAMAAgAEAAAABAACAAUACAAJAAoACgAFAAgACAALAAkACwAFAAoABwALAAgACwAEAAUACwAHAAQACAACAAMAAgAIAAUACwAPAAkADwAKAAkADwALAAoAAQAEAAcABAABAAAAAQAIAAMACAABAAcA")],
"name": "Material",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 16,
"vertex_data": PackedByteArray("wfXIP/oomTMehWs/wfXIP/sokbMghWu/xfXIv/oomTMehWs/xfXIv/sokbMghWu/wfXIPzDXozwehWs/xfXIvzDXozwehWs/4t2rP+/WozxiVTG/wfXIP+bWozwghWu/xfXIv+bWozwghWu/3d2rv+/WozxiVTG/3d2rvyfXozxgVTE/4t2rPyfXozxgVTE/69OYP/PWozwPyAO/69OYPyPXozwLyAM/69OYv/PWozwPyAO/69OYvyPXozwLyAM/")
}]
blend_shape_mode = 0

[sub_resource type="ArrayMesh" id="ArrayMesh_mm8rw"]
resource_name = "RugRectangle_rugRectangle"
_surfaces = [{
"aabb": AABB(-1.57, -6.75954e-08, -0.92, 3.14, 0.0200001, 1.84),
"attribute_data": PackedByteArray("MGHrPqo0hT4wYes+qjSFPjBh6z6qNIU+MGHrPqo0hT4wYes+qjSFPjBh6z6qNIU+MGHrPqo0hT4wYes+qjSFPjBh6z6qNIU+MGHrPqo0hT4wYes+qjSFPjBh6z6qNIU+MGHrPqo0hT4wYes+qjSFPjBh6z6qNIU+MGHrPqo0hT4wYes+qjSFPjBh6z6qNIU+ScHkPojjlD4wYes+qjSFPjBh6z6qNIU+MGHrPqo0hT4wYes+qjSFPjBh6z6qNIU+MGHrPqo0hT4wYes+qjSFPknB5D6I45Q+MGHrPqo0hT5JweQ+iOOUPjBh6z6qNIU+ScHkPojjlD4wYes+qjSFPknB5D6I45Q+MGHrPqo0hT5JweQ+iOOUPjBh6z6qNIU+ScHkPojjlD4wYes+qjSFPknB5D6I45Q+MGHrPqo0hT4="),
"format": 34359742487,
"index_count": 84,
"index_data": PackedByteArray("BAAHAAEABwAEAAoABgAMAAAADAAGAA8AGAAbAB0AHwANABAAHwAQAB0AGQAIAAsACAAZABEAIwAlACEAJQAjACcAIAASAB4AEgAgABoAGgAgACQAGgAkACYAHgAiACAAIgAeABwAIgAcACYAJgAcABoAEwAVAA0AFQATABgAGAATABsADQAfABMAHQAQABgABQAOABYADgAFAAIAAwAXAAkAFwADABQA"),
"lods": [0.960776, PackedByteArray("BAAHAAEABwAEAAoABgAMAAAADAAGAA8AGAAbAB0AHQAQABgAGAAfABsAHwAQAB0AFQAfABgAHwANABAAHwAVAA0AGQAIAAsACAAZABEAHgAmABoAJgAcABoAJgAeABwABQAOABYADgAFAAIAAwAXAAkAFwADABQA")],
"material": ExtResource("1_1k278"),
"name": "Material",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 40,
"vertex_data": PackedByteArray("wfXIP/oomTMehWs/wfXIP/oomTMehWs/wfXIP/oomTMehWs/wfXIP/sokbMghWu/wfXIP/sokbMghWu/wfXIP/sokbMghWu/xfXIv/oomTMehWs/xfXIv/oomTMehWs/xfXIv/oomTMehWs/xfXIv/sokbMghWu/xfXIv/sokbMghWu/xfXIv/sokbMghWu/wfXIPzDXozwehWs/wfXIPzDXozwehWs/wfXIPzDXozwehWs/xfXIvzDXozwehWs/xfXIvzDXozwehWs/xfXIvzDXozwehWs/4t2rP+/WozxiVTG/4t2rP+/WozxiVTG/wfXIP+bWozwghWu/wfXIP+bWozwghWu/wfXIP+bWozwghWu/xfXIv+bWozwghWu/xfXIv+bWozwghWu/xfXIv+bWozwghWu/3d2rv+/WozxiVTG/3d2rv+/WozxiVTG/3d2rvyfXozxgVTE/3d2rvyfXozxgVTE/4t2rPyfXozxgVTE/4t2rPyfXozxgVTE/69OYP/PWozwPyAO/69OYP/PWozwPyAO/69OYPyPXozwLyAM/69OYPyPXozwLyAM/69OYv/PWozwPyAO/69OYv/PWozwPyAO/69OYvyPXozwLyAM/69OYvyPXozwLyAM//3//f////z//fwAA////v////3////+//////////7//fwAA////v////3////+//3//f////z//fwAA////vwAA/3////+//////////7//fwAA////vwAA/3////+//3//f////z//f///////v////3////+//3//f////z//f///////vwAA/3////+//3///////7//f///////v/////////+//3///////7////9/////v/////////+//3///////78AAP9/////v/9///////+//3///////7//f///////v/9///////+//3///////7//f///////v/9///////+//3///////7//f///////v/9///////+//3///////7//f///////v/9///////+//3///////78=")
}]
blend_shape_mode = 0
shadow_mesh = SubResource("ArrayMesh_fj4hh")

[node name="rugRectangle" type="MeshInstance3D"]
mesh = SubResource("ArrayMesh_mm8rw")
skeleton = NodePath("")
