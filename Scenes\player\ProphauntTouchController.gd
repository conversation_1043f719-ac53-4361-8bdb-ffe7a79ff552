extends TouchController
class_name ProphauntTouchController

@onready var haunter_parent: Control = $HaunterParent
@onready var prop_parent: Control = $PropParent
@onready var haunter_aim: TextureRect = $HaunterParent/aim


signal ability_used(ability_index: int)


func _ready() -> void:
	super()


func set_haunter():
	haunter_parent.visible = true
	prop_parent.visible = false


func set_prop():
	haunter_parent.visible = false
	prop_parent.visible = true


func _on_grenade_button_gui_input(event: InputEvent) -> void:
	if event is InputEventScreenTouch:
		if event.pressed:
			ability_used.emit(1)


var shoot_pressed = false
func _on_haunter_shoot_button_gui_input(event: InputEvent) -> void:
	if event is InputEventScreenTouch:
		if event.pressed:
			shoot_pressed = true
			ability_used.emit(0)
		else:
			shoot_pressed = false
	if event is InputEventScreenDrag:
		if shoot_pressed:
			ability_used.emit(0)
	
	_on_full_screen_gui_input(event)
