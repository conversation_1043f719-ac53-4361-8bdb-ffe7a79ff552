extends TouchController
class_name ProphauntTouchController

@onready var haunter_parent: Control = $HaunterParent
@onready var prop_parent: Control = $PropParent
@onready var haunter_aim: TextureRect = $HaunterParent/aim
@onready var grenade_ammo: Label = $HaunterParent/GrenadeButton/GrenadeAmmo
@onready var prop_normal_cursor: TextureRect = $PropParent/NormalCursor
@onready var prop_cursor: TextureRect = $PropParent/PropCursor


signal ability_used(ability_index: int)


func _ready() -> void:
	super()


func set_haunter():
	haunter_parent.visible = true
	prop_parent.visible = false
	update_grenade_ammo()


func set_prop():
	haunter_parent.visible = false
	prop_parent.visible = true


func update_grenade_ammo():
	grenade_ammo.text = str(Selector.my_prophaunt_item_data["grenades"])


func _on_grenade_button_gui_input(event: InputEvent) -> void:
	if event is InputEventScreenTouch:
		if event.pressed:
			ability_used.emit(1)


var shoot_pressed = false
func _on_haunter_shoot_button_gui_input(event: InputEvent) -> void:
	if event is InputEventScreenTouch:
		if event.pressed:
			shoot_pressed = true
			ability_used.emit(0)
		else:
			shoot_pressed = false
	if event is InputEventScreenDrag:
		if shoot_pressed:
			ability_used.emit(0)
	
	_on_full_screen_gui_input(event)


func _on_change_prop_button_gui_input(event: InputEvent) -> void:
	pass # Replace with function body.
