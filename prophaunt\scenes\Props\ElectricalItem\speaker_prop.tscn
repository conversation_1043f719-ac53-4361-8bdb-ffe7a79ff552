[gd_scene load_steps=4 format=3 uid="uid://ldeb5ncnj6c4"]

[ext_resource type="PackedScene" uid="uid://cy82bsxfj1ddv" path="res://prophaunt/scenes/PropObject.tscn" id="1_rxvre"]
[ext_resource type="PackedScene" uid="uid://br0rttvm2vi2d" path="res://prophaunt/maps/Source/ElectricalItem/speaker.tscn" id="2_qwcao"]

[sub_resource type="BoxShape3D" id="BoxShape3D_bx0gf"]
size = Vector3(0.465637, 1.91181, 0.451489)

[node name="SpeakerProp" instance=ExtResource("1_rxvre")]

[node name="Speaker" parent="Meshes" index="0" instance=ExtResource("2_qwcao")]

[node name="CollisionShape3D" type="CollisionShape3D" parent="StaticBody3D" index="0"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -3.05474e-05, 0.953862, -0.000134245)
shape = SubResource("BoxShape3D_bx0gf")
