[gd_scene load_steps=5 format=4 uid="uid://djes8je4neb1s"]

[ext_resource type="Material" uid="uid://lhnii45g7kv1" path="res://prophaunt/Mat/DarkLightBrown.tres" id="1_5qfm0"]

[sub_resource type="ArrayMesh" id="ArrayMesh_vdw7g"]
_surfaces = [{
"aabb": AABB(-1, -0.1, -1, 2, 0.1, 2),
"format": 34896613377,
"index_count": 132,
"index_data": PackedByteArray("CQAEAAYACQAIAAQAFwAKABIAFwAQAAoADwALAAEADwARAAsAEgALABMAEgAKAAsADQAHAAUADQAOAAcADwAAAAwADwABAAAAFQAGABQAFQAJAAYAEAAAAAoAEAAMAAAAFgADABMAFgAOAAMACgABAAsACgAAAAEAFQAOABYAFQAHAA4ABgAMABAABgAEAAwACAAMAAQACAAPAAwAAgAOAA0AAgADAA4ACAARAA8ACAAJABEAFAAQABcAFAAGABAABQAXAA0ABQAUABcACQAWABEACQAVABYAEQATAAsAEQAWABMABwAUAAUABwAVABQAAgATAAMAAgASABMADQASAAIADQAXABIA"),
"name": "Wall.001",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 24,
"vertex_data": PackedByteArray("///+/wAAAAD//wAAAAAAAP///////wAA//8AAP//AAAyM/7/AAAAAAAA/////wAAAAD+/zIzAAAAAAAA//8AADIzAAAAAAAAAAAAADIzAAD///7/MjMAAP//AAAyMwAA/3/+/wAAAAD/f/////8AAP9/AAD//wAA/38AAAAAAAD/f/7/MjMAAP9/AAAyMwAA///+//9/AAD//wAA/38AAAAA/v//fwAAAAAAAP9/AAD/fwAA/38AAP9//v//fwAA")
}]
blend_shape_mode = 0

[sub_resource type="ArrayMesh" id="ArrayMesh_dqfjr"]
resource_name = "FloorCornerQuarterUV_FloorCornerQuarter"
_surfaces = [{
"aabb": AABB(-1, -0.1, -1, 2, 0.1, 2),
"attribute_data": PackedByteArray("Z3WZwQlwmcHGemKhCXCZwQlwmcFndWKhcvgs+UGDscJy+CiNC/Ms+UGDr4wL8yiNcvjE2AvzwqJy+MTYCXCZwacE6IvGekr3cd0ojXL4N7qV8e6MZ3WZwacESvdndUr32P3E2HHdxNjY/cTYCb0qwwvzKsPY/Te6fd/ujFg66IuPWpnBxnroi8Z6mcFYOkr3j1qZwWd16ItndZnBZ3VK93L4LPkJcOiLC/Mqwwlw6Ity+CrDpwSZwUO5scJndeiLC/Mqw6cEmcFBg7HCCXBK99j9LPkJcEr3cd0s+Vg6mcGPWuiLcd0qw1g6mcGPWkr3Cb0s+QvzLPmPWuiLQYOz+MZ6YqFy+CrDj1pK90O5r4xndWKhC/Mqw1g66IsJvSiNxnqZwZXxgvlYOkr3cd0qw2d1mcF934L5WDqZwY9amcFDubHCcd0s+Vg6mcGPWpnBQ7mz+Am9KsM="),
"format": 34896613399,
"index_count": 132,
"index_data": PackedByteArray("HQAOABMAHQAaAA4AUwAfAD4AUwA3AB8ANQAkAAQANQA7ACQAQAAmAEQAQAAiACYAKwAVAA8AKwAvABUAMwAAACcAMwADAAAATQAUAEkATQAeABQAOAABACAAOAApAAEAUAAKAEMAUAAyAAoAIQAFACUAIQACAAUASgAxAE4ASgAWADEAEgAqADkAEgANACoAGAAoAAwAGAA0ACgABgAwACwABgAJADAAGQA9ADYAGQAcAD0ARwA5AFUARwASADkAEABSAC0AEABGAFIAGwBRADwAGwBLAFEAOgBCACMAOgBPAEIAFwBIABEAFwBMAEgACABFAAsACABBAEUALgA/AAcALgBUAD8A"),
"material": ExtResource("1_5qfm0"),
"name": "Wall.001",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 86,
"vertex_data": PackedByteArray("///+/wAA//////7/AABU1f///v8AAFTV//8AAAAA/////wAAAABU1f//AAAAAFTV/////////7///////////////////1TV//8AAP///7///wAA//9U1f//AAD//1TVMjP+/wAA//8yM/7/AABU1TIz/v8AAKrpAAD//////78AAP////9U1QAA/////1TVAAD+/zIzVNUAAP7/MjOq6QAA/v8yM1TVAAAAAP///78AAAAA//9U1QAAAAD//1TVMjMAAAAA//8yMwAAAABU1TIzAAAAAKrpAAAAADIzVNUAAAAAMjNU1QAAAAAyM6rpAAAAADIzVNX///7/MjNU1f///v8yM1TV///+/zIzVNX///7/MjNU1f//AAAyM1TV//8AADIzVNX//wAAMjNU1f//AAAyM1TV/3/+/wAA////f/7/AAD///9//v8AAFTV/3/+/wAAVNX/f///////v/9///////+//3//////VNX/f/////////9/AAD///+//38AAP///7//fwAA//9U1f9/AAD//1TV/38AAAAA////fwAAAAD///9/AAAAAFTV/38AAAAAVNX/f/7/MjNU1f9//v8yM1TV/3/+/zIzVNX/fwAAMjNU1f9/AAAyM1TV/38AADIzVNX/fwAAMjNU1f///v//f1TV///+//9///////7//39U1f///v//f1TV//8AAP9/VNX//wAA/39U1f//AAD/f1TV//8AAP9/VNUAAP7//39U1QAA/v//f1TVAAD+//9/VNUAAP7//39U1QAAAAD/f1TVAAAAAP9/VNUAAAAA/39U1QAAAAD/f1TV/38AAP9/VNX/fwAA/39U1f9/AAD/f1TV/38AAP9/VNX/f/7//39U1f9//v//f1TV/3/+//9/////f/7//39U1f+//79U1aoqqiqqKv+//79UVVRVqiqqKv//////f/+/qiqqKv////9UVVRVqiqqKv8//79U1aoq+koEtf9//39U1aoqVNVU1VTVqir6SgS1VNVU1f9//39UVVRVVNVU1f8//7+qKlTV+koEtaoqVNWqKlTV+koEtVTVVNWqqqqqVNWqKqoqqiqqKqoqqipU1VRVVFWqKqoqqiqqKv+//7//P/+/VNWqKlTVqir/f/9//////1TVqir/f/+//3//f/////9UVVRVVFVUVf+//7//P/+/VFVUVaoqVNWqqqqqVNWqKlTVqiqqKlTVVFVUVaoqVNWqKlTVqqqqqv9//7+qKqoqqiqqKqoqVNVUVVRVqiqqKqoqqipU1aoqVNWqKlTVVNVU1VTVVFVUVaoqVNVU1VTVVNVU1VRVVFWqKlTVVFVUVaoqVNVU1aoqqqqqqv9//79U1aoq")
}]
blend_shape_mode = 0
shadow_mesh = SubResource("ArrayMesh_vdw7g")

[sub_resource type="BoxShape3D" id="BoxShape3D_uv5jq"]
size = Vector3(2, 0.1, 2)

[node name="FloorCornerQuarter" type="MeshInstance3D"]
mesh = SubResource("ArrayMesh_dqfjr")
skeleton = NodePath("")

[node name="StaticBody3D" type="StaticBody3D" parent="." groups=["VisibleGroup0"]]
collision_layer = 2

[node name="CollisionShape3D" type="CollisionShape3D" parent="StaticBody3D"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 2.98023e-08, -0.05, 0)
shape = SubResource("BoxShape3D_uv5jq")
