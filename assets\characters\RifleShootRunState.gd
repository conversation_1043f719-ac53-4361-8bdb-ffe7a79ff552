class_name RifleShootRunState
extends Node

@onready var character: Character = $"../.."

var total_time = Constants.PROPHAUNT_RIFLE_COOLDOWN
var counter = 0

func _ready() -> void:
	pass


func run(delta):
	if character.controls != Constants.Controls.Player or Constants.is_server:
		return
	
	counter += delta
	if counter >= total_time:
		end_state()
		return
	
	#print("rilfeshootrun")
	
	var input_dir = character.player_client_input(delta)
	if input_dir == Vector2(0, 0):
		character.rifleShootState.start_state()
		character.rifleShootState.counter = counter
		return


	character.handle_rifle_mode(delta, true)


func start_state():
	if character.state == character.State.RIFLE_SHOOT_RUN:
		return
	
	character.state = character.State.RIFLE_SHOOT_RUN
	counter = 0
	total_time = Constants.PROPHAUNT_RIFLE_COOLDOWN
	# Animation will be handled in handle_animation function


func end_state():
	character.on_rifle_shoot_finished()
