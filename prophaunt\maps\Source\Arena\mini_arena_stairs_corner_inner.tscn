[gd_scene load_steps=5 format=4 uid="uid://oa5oa01xd8x"]

[ext_resource type="Material" uid="uid://b60k1mvxam058" path="res://prophaunt/Mat/ArenaColorMap.tres" id="1_o0iku"]

[sub_resource type="ArrayMesh" id="ArrayMesh_heh6d"]
_surfaces = [{
"aabb": AABB(-1, -7.54979e-08, -1, 2, 1, 2),
"format": 34359742465,
"index_count": 216,
"index_data": PackedByteArray("AAABAAIAAwACAAEAAgADAAQABQACAAQABgAEAAMABwAIAAkACgAJAAgACwAJAAoADAANAA4ADwAOAA0AEAARABIAEwASABEAFAAVABYACgAWABUAFwAKABUACwAKABcAGAAXABUAGQAXABgADwANAAEAAwABAA0AEAADAA0AEgADABAAFAAQAA0AFgAQABQAFAANABUADAAVAA0ADAAOABUAGAAVAA4AGgAbABwAHQAcABsAFwAdABsAHgAdABcAGQAeABcAHwAgACEAIgAhACAAIwAkACUAJgAlACQAIwAlACAAIgAgACUAJAAfACYAIQAmAB8AJgAhACUAIgAlACEAJwAAAAIAKAAdAB4AAgAFACcAHAAdACgAEAAWABEACgARABYACAARAAoAAAAnAAEAAQAnAA8ADgAPACcABQAOACcABAAOAAUAGgAOAAQAHAAOABoAKAAOABwADgAoABgAGAAoABkAHgAZACgABgAbAAQAGgAEABsAAwASAAYAEwAGABIABwAGABMABgAHABcAGwAGABcAFwAHAAsACQALAAcAEwARAAcACAAHABEA"),
"name": "colormap",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 41,
"vertex_data": PackedByteArray("zMxMPwAAgD+uqqq+AACAPwAAgD+uqqq+mpkZP8zMTD+uqqq+AACAP6uqKj+tqqq+7+5uvgAAgD+uqqq+zMzMPgAAgD+uqqq+7+5uvquqKj+tqqq+q6qqPquqKj/c3d0+q6qqPq2qqj7c3d0+q6qqPq2qKj/LzEw/q6qqPq6qqj4AAIA/q6qqPq2qKj/+/38/AACAv2khorMAAIC/AACAP2khorMAAIC/AACAv/7/fz8AAIC/AACAP/7/fz8AAIC/AACAP62qqj6qqqo+3d3dPq2qqj6qqqo+AACAP6uqKj+qqqo+3d3dPquqKj+qqqo+AACAP2khojMAAIA/AACAv2khojMAAIA/AACAP66qqj4AAIA/q6qqvq2qKj/+/38/AACAvwAAgD/+/38/q6qqvgAAgD/+/38/q6qqvgAAgD/07m6+q6qqvquqKj/z7m6+q6qqvgAAgD/HzEw+q6qqvszMTD/KzMw+q6qqvgAAgD+ZmRk/rV+uPa2qKj+PfEE/1LDgva2qKj9cSV4/nWE/PRIROT/m2T4/j5vLvRIROT+Bc1Q/nYtjvq2qKj9LZyw/5xrgvK2qKj9/mg8/LjQ8vhIROT/0CS8/+zcavRIROT9bcBk/mpkZP/7/fz+JiAi/h4gIvwAAgD/KzMw+")
}]
blend_shape_mode = 0

[sub_resource type="ArrayMesh" id="ArrayMesh_nps5j"]
resource_name = "MiniArenaStairsCornerInner_MiniArenaStairsCornerInner"
_surfaces = [{
"aabb": AABB(-1, -7.54979e-08, -1, 2, 1, 2),
"attribute_data": PackedByteArray("AAB4P2ZmRj8AAMA9ZmZGPwAAwD1mZkY/AAB4P2ZmRj8AAMA9ZmZGPwAAeD9mZkY/AAB4P9ejUD8AAMA916NQPwAAeD93d1c/AADAPXd3Vz8AAHg/d3dXPwAAeD9mZkY/AADAPWZmRj8AAMA9ZmZGPwAAeD9mZkY/AADAPWZmRj8AAMA9ZmZGPwAAeD93d1c/AADAPXd3Vz8AAMA9d3dXPwAAwD13d1c/AADAPXd3Vz8AAHg/d3dXPwAAwD2IiGg/AADAPYiIaD8AAHg/iIhoPwAAeD93d1c/AADAPXd3Vz8AAHg/iIhoPwAAwD2IiGg/AAB4P4iIaD8AAHg/d3dXPwAAwD13d1c/AAB4P3d3Vz8AAHg/mpl5PwAAeD+amXk/AAB4P5qZeT8AAHg/mpl5PwAAeD+amXk/AAB4P5qZeT8AAHg/ZmZGPwAAwD1mZkY/AAB4P2ZmRj8AAHg/ZmZGPwAAwD1mZkY/AAB4P2ZmRj8AAHg/iIhoPwAAwD2IiGg/AAB4P4iIaD8AAHg/iIhoPwAAwD2IiGg/AADAPYiIaD8AAHg/d3dXPwAAwD13d1c/AAB4P3d3Vz8AAHg/d3dXPwAAwD13d1c/AADAPXd3Vz8AAHg/mpl5PwAAeD+amXk/AAB4P5qZeT8AAHg/mpl5PwAAeD+amXk/AAB4P5qZeT8AAHg/iIhoPwAAwD2IiGg/AAB4P4iIaD8AAHg/d3dXPwAAwD13d1c/AAB4P3d3Vz8AAHg/ZmZGPwAAwD1mZkY/AAB4P2ZmRj8AAHg/ZmZGPwAAwD1mZkY/AAB4P2ZmRj8AAMA9ZmZGPwAAwD1mZkY/AAB4P2ZmRj8AAMA9d3dXPwAAwD13d1c/AAB4P3d3Vz8AAMA9ZmZGPwAAwD1mZkY/AAB4P2ZmRj8AAMA916NQPwAAeD/Xo1A/AADAPWZmRj8AAMA9ZmZGPwAAeD9mZkY/AAB4P83MbD8AAHg/zcxsPwAAeD8zM1M/AAB4PzMzUz8AAHg/MzNTPwAAeD8zM1M/AAB4P83MbD8AAHg/zcxsPwAAeD8zM1M/AAB4PzMzUz8AAHg/MzNTPwAAeD8zM1M/AADAPWZmRj8AAMA9ZmZGPwAAwD1mZkY/AADAPWZmRj8="),
"format": ***********,
"index_count": 216,
"index_data": PackedByteArray("AAADAAYACAAGAAMABgAIAAsADgAGAAsAEQALAAgAFgAZABoAHgAaABkAIQAaAB4AIgAlACgAKwAoACUALgAxADQANwA0ADEAOgA9AEAAHABAAD0AQwAcAD0AHwAcAEMARgBDAD0ASQBDAEYALQAnAAUACgAFACcAMAAKACcANgAKADAAPAAwACcAQgAwADwAOwAmAD4AIwA+ACYAJAAqAD8ASAA/ACoATgBRAFQAVgBUAFEARQBWAFEAWQBWAEUASwBZAEUAWgBbAF0AXwBdAFsAYABhAGMAZQBjAGEAYABjAFsAXwBbAGMAYQBaAGUAXQBlAFoAZABcAGIAXgBiAFwAZgACAAcAaQBVAFgABwAQAGYAUwBVAGkALwBBADIAHQAyAEEAFwAyAB0AAQBnAAQABABnACwAKQAsAGcADwApAGcADAApAA8ATAApAAwAUgApAEwAaAApAFIAKQBoAEcARwBoAEoAVwBKAGgAEwBQAA0ATQANAFAACQA1ABIAOAASADUAFAASADgAEgAUAEQATwASAEQARAAUACAAGwAgABQAOQAzABUAGAAVADMA"),
"material": ExtResource("1_o0iku"),
"name": "colormap",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 106,
"vertex_data": PackedByteArray("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")
}]
blend_shape_mode = 0
shadow_mesh = SubResource("ArrayMesh_heh6d")

[sub_resource type="ConcavePolygonShape3D" id="ConcavePolygonShape3D_hnoeh"]
data = PackedVector3Array(0.1788, 0.4485, 0.0121, -0.1553, 0.4485, -0.1541, 0.1788, 0.4485, -0.0732, -0.1553, 0.4485, -0.1541, 0.1481, 0.4485, -0.4494, 0.146, 0.4485, -0.1393, -0.1553, 0.4485, -0.1541, -0.1533, 0.4485, -0.4514, 0.1481, 0.4485, -0.4494, -0.1454, 0.2865, 0.4557, 0.1462, 0.0009, 0.9596, -0.1622, 0.0009, 0.8978, -0.1454, 0.2865, 0.4557, 0.0549, 0.2943, 0.4512, 0.1462, 0.0009, 0.9596, 0.146, 0.4485, -0.1393, 0.1788, 0.4485, -0.0732, -0.1553, 0.4485, -0.1541, 0.0549, 0.2943, 0.4512, 0.4568, 0.0009, 0.8978, 0.1462, 0.0009, 0.9596, 0.0549, 0.2943, 0.4512, 0.2136, 0.2943, 0.4085, 0.4568, 0.0009, 0.8978, 0.2136, 0.2943, 0.4085, 0.7202, 0.0009, 0.7218, 0.4568, 0.0009, 0.8978, 0.2136, 0.2943, 0.4085, 0.3439, 0.2943, 0.3083, 0.7202, 0.0009, 0.7218, 0.3439, 0.2943, 0.3083, 0.8962, 0.0009, 0.4585, 0.7202, 0.0009, 0.7218, 0.3439, 0.2943, 0.3083, 0.4259, 0.2943, 0.1659, 0.8962, 0.0009, 0.4585, 0.4259, 0.2943, 0.1659, 0.958, 0.0009, 0.1478, 0.8962, 0.0009, 0.4585, 0.4259, 0.2943, 0.1659, 0.4472, 0.2943, 0.0029, 0.958, 0.0009, 0.1478, 0.4472, 0.2943, 0.0029, 0.8962, 0.0009, -0.1628, 0.958, 0.0009, 0.1478, 0.4472, 0.2943, 0.0029, 0.4455, 0.2932, -0.1434, 0.8962, 0.0009, -0.1628, 0.1461, 0.4485, 0.091, -0.1553, 0.4485, -0.1541, 0.1788, 0.4485, 0.0121, 0.0858, 0.4485, 0.1514, -0.1553, 0.4485, -0.1541, 0.1461, 0.4485, 0.091, 0.0069, 0.4485, 0.184, -0.1553, 0.4485, -0.1541, 0.0858, 0.4485, 0.1514, -0.1432, 0.4485, 0.1836, -0.1553, 0.4485, -0.1541, 0.0069, 0.4485, 0.184, -0.1553, 0.4485, -0.1541, -0.4439, 0.4485, -0.4533, -0.1533, 0.4485, -0.4514, -0.1553, 0.4485, -0.1541, -0.4458, 0.4485, -0.156, -0.4439, 0.4485, -0.4533, -0.1553, 0.4485, -0.1541, -0.4481, 0.4485, 0.1816, -0.4458, 0.4485, -0.156, -0.1553, 0.4485, -0.1541, -0.1432, 0.4485, 0.1836, -0.4481, 0.4485, 0.1816, -0.4458, 0.2865, -0.156, -0.4439, -0.0003, -0.4533, -0.4439, 0.2865, -0.4533, -0.4458, 0.2865, -0.156, -0.4458, -0.0003, -0.156, -0.4439, -0.0003, -0.4533, -0.4481, 0.2865, 0.1816, -0.4458, -0.0003, -0.156, -0.4458, 0.2865, -0.156, -0.4481, 0.2865, 0.1816, -0.4481, -0.0003, 0.1816, -0.4458, -0.0003, -0.156, -0.1533, 0.2865, -0.4514, 0.1481, -0.0003, -0.4494, 0.1481, 0.2865, -0.4494, -0.1533, 0.2865, -0.4514, -0.1533, -0.0003, -0.4514, 0.1481, -0.0003, -0.4494, -0.4439, 0.2865, -0.4533, -0.1533, -0.0003, -0.4514, -0.1533, 0.2865, -0.4514, -0.4439, 0.2865, -0.4533, -0.4439, -0.0003, -0.4533, -0.1533, -0.0003, -0.4514, 0.1788, 0.4485, -0.0732, 0.4455, 0.2932, -0.1434, 0.4472, 0.2943, 0.0029, 0.1788, 0.4485, -0.0732, 0.146, 0.4485, -0.1393, 0.4455, 0.2932, -0.1434, 0.1788, 0.4485, 0.0121, 0.4472, 0.2943, 0.0029, 0.4259, 0.2943, 0.1659, 0.1788, 0.4485, 0.0121, 0.1788, 0.4485, -0.0732, 0.4472, 0.2943, 0.0029, 0.1461, 0.4485, 0.091, 0.4259, 0.2943, 0.1659, 0.3439, 0.2943, 0.3083, 0.1461, 0.4485, 0.091, 0.1788, 0.4485, 0.0121, 0.4259, 0.2943, 0.1659, 0.0858, 0.4485, 0.1514, 0.3439, 0.2943, 0.3083, 0.2136, 0.2943, 0.4085, 0.0858, 0.4485, 0.1514, 0.1461, 0.4485, 0.091, 0.3439, 0.2943, 0.3083, 0.0069, 0.4485, 0.184, 0.2136, 0.2943, 0.4085, 0.0549, 0.2943, 0.4512, 0.0069, 0.4485, 0.184, 0.0858, 0.4485, 0.1514, 0.2136, 0.2943, 0.4085, -0.1432, 0.4485, 0.1836, 0.0549, 0.2943, 0.4512, -0.1454, 0.2865, 0.4557, -0.1432, 0.4485, 0.1836, 0.0069, 0.4485, 0.184, 0.0549, 0.2943, 0.4512, -0.1454, 0.2865, 0.4557, -0.1449, 0.4485, 0.4432, -0.1432, 0.4485, 0.1836, -0.4498, 0.4485, 0.4437, -0.1432, 0.4485, 0.1836, -0.1449, 0.4485, 0.4432, -0.4498, 0.4485, 0.4437, -0.4481, 0.4485, 0.1816, -0.1432, 0.4485, 0.1836, -0.4498, 0.2865, 0.4437, -0.4481, 0.4485, 0.1816, -0.4498, 0.4485, 0.4437, -0.4498, 0.2865, 0.4437, -0.4481, 0.2865, 0.1816, -0.4481, 0.4485, 0.1816, -0.4498, -0.0003, 0.4437, -0.4481, 0.2865, 0.1816, -0.4498, 0.2865, 0.4437, -0.4498, -0.0003, 0.4437, -0.4481, -0.0003, 0.1816, -0.4481, 0.2865, 0.1816, -0.4439, 0.4485, -0.4533, -0.1533, 0.2865, -0.4514, -0.1533, 0.4485, -0.4514, -0.4439, 0.4485, -0.4533, -0.4439, 0.2865, -0.4533, -0.1533, 0.2865, -0.4514, -0.1533, 0.4485, -0.4514, 0.1481, 0.2865, -0.4494, 0.1481, 0.4485, -0.4494, -0.1533, 0.4485, -0.4514, -0.1533, 0.2865, -0.4514, 0.1481, 0.2865, -0.4494, -0.4481, 0.4485, 0.1816, -0.4458, 0.2865, -0.156, -0.4458, 0.4485, -0.156, -0.4481, 0.4485, 0.1816, -0.4481, 0.2865, 0.1816, -0.4458, 0.2865, -0.156, -0.4458, 0.4485, -0.156, -0.4439, 0.2865, -0.4533, -0.4439, 0.4485, -0.4533, -0.4458, 0.4485, -0.156, -0.4458, 0.2865, -0.156, -0.4439, 0.2865, -0.4533, -0.1454, 0.2865, 0.4557, -0.4498, 0.4485, 0.4437, -0.1449, 0.4485, 0.4432, -0.1454, 0.2865, 0.4557, -0.4498, 0.2865, 0.4437, -0.4498, 0.4485, 0.4437, -0.1454, 0.2865, 0.4557, -0.4498, -0.0003, 0.4437, -0.4498, 0.2865, 0.4437, -0.1454, 0.2865, 0.4557, -0.1454, -0.0003, 0.4557, -0.4498, -0.0003, 0.4437, -0.1454, 0.2865, 0.4557, -0.1622, 0.0009, 0.8978, -0.1454, -0.0003, 0.4557, 0.4455, 0.2932, -0.1434, 0.146, 0.4485, -0.1393, 0.4455, 0.4485, -0.1434, 0.1481, 0.4485, -0.4494, 0.4455, 0.4485, -0.1434, 0.146, 0.4485, -0.1393, 0.1481, 0.4485, -0.4494, 0.4475, 0.4485, -0.4474, 0.4455, 0.4485, -0.1434, 0.4475, 0.2932, -0.4474, 0.4455, 0.4485, -0.1434, 0.4475, 0.4485, -0.4474, 0.4475, 0.2932, -0.4474, 0.4455, 0.2932, -0.1434, 0.4455, 0.4485, -0.1434, 0.4475, 0.2932, -0.4474, 0.4455, 0.0009, -0.1434, 0.4455, 0.2932, -0.1434, 0.4475, 0.2932, -0.4474, 0.4475, 0.0009, -0.4474, 0.4455, 0.0009, -0.1434, 0.8962, 0.0009, -0.1628, 0.4455, 0.2932, -0.1434, 0.4455, 0.0009, -0.1434, 0.1481, 0.2865, -0.4494, 0.4475, 0.4485, -0.4474, 0.1481, 0.4485, -0.4494, 0.1481, 0.2865, -0.4494, 0.4475, 0.2932, -0.4474, 0.4475, 0.4485, -0.4474, 0.1481, 0.2865, -0.4494, 0.4475, 0.0009, -0.4474, 0.4475, 0.2932, -0.4474, 0.1481, 0.2865, -0.4494, 0.1481, -0.0003, -0.4494, 0.4475, 0.0009, -0.4474)

[node name="MiniArenaStairsCornerInner" type="MeshInstance3D"]
mesh = SubResource("ArrayMesh_nps5j")
skeleton = NodePath("")

[node name="StaticBody3D" type="StaticBody3D" parent="."]
transform = Transform3D(2.27387, 0, 0.0149806, 0, 2.27392, 0, -0.0149806, 0, 2.27387, 0, 0, 0)
collision_layer = 2

[node name="CollisionShape3D" type="CollisionShape3D" parent="StaticBody3D"]
shape = SubResource("ConcavePolygonShape3D_hnoeh")
