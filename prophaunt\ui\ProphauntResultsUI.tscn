[gd_scene load_steps=3 format=3 uid="uid://bh9results1"]

[ext_resource type="Script" path="res://prophaunt/scripts/ProphauntResultsUI.gd" id="1_results_script"]

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_1"]
bg_color = Color(0, 0, 0, 0.8)
corner_radius_top_left = 10
corner_radius_top_right = 10
corner_radius_bottom_right = 10
corner_radius_bottom_left = 10

[node name="ProphauntResultsUI" type="Control"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
script = ExtResource("1_results_script")

[node name="Background" type="ColorRect" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
color = Color(0, 0, 0, 0.7)

[node name="CenterContainer" type="CenterContainer" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0

[node name="MainPanel" type="Panel" parent="CenterContainer"]
layout_mode = 2
custom_minimum_size = Vector2(800, 600)
theme_override_styles/panel = SubResource("StyleBoxFlat_1")

[node name="MainContainer" type="VBoxContainer" parent="CenterContainer/MainPanel"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 20.0
offset_top = 20.0
offset_right = -20.0
offset_bottom = -20.0

[node name="Header" type="VBoxContainer" parent="CenterContainer/MainPanel/MainContainer"]
layout_mode = 2

[node name="TitleLabel" type="Label" parent="CenterContainer/MainPanel/MainContainer/Header"]
layout_mode = 2
text = "ROUND RESULTS"
horizontal_alignment = 1

[node name="WinnerLabel" type="Label" parent="CenterContainer/MainPanel/MainContainer/Header"]
layout_mode = 2
text = "PROPS WIN!"
horizontal_alignment = 1

[node name="RoundInfoLabel" type="Label" parent="CenterContainer/MainPanel/MainContainer/Header"]
layout_mode = 2
text = "Round 1 - Duration: 02:45"
horizontal_alignment = 1

[node name="HSeparator" type="HSeparator" parent="CenterContainer/MainPanel/MainContainer"]
layout_mode = 2

[node name="StatsContainer" type="HBoxContainer" parent="CenterContainer/MainPanel/MainContainer"]
layout_mode = 2
size_flags_vertical = 3

[node name="TeamStats" type="VBoxContainer" parent="CenterContainer/MainPanel/MainContainer/StatsContainer"]
layout_mode = 2
size_flags_horizontal = 3

[node name="TeamStatsLabel" type="Label" parent="CenterContainer/MainPanel/MainContainer/StatsContainer/TeamStats"]
layout_mode = 2
text = "TEAM STATISTICS"
horizontal_alignment = 1

[node name="TeamStatsPanel" type="Panel" parent="CenterContainer/MainPanel/MainContainer/StatsContainer/TeamStats"]
layout_mode = 2
size_flags_vertical = 3

[node name="TeamStatsContainer" type="VBoxContainer" parent="CenterContainer/MainPanel/MainContainer/StatsContainer/TeamStats/TeamStatsPanel"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 10.0
offset_top = 10.0
offset_right = -10.0
offset_bottom = -10.0

[node name="PropsStatsLabel" type="Label" parent="CenterContainer/MainPanel/MainContainer/StatsContainer/TeamStats/TeamStatsPanel/TeamStatsContainer"]
layout_mode = 2
text = "PROPS TEAM"
horizontal_alignment = 1

[node name="PropsSurvived" type="Label" parent="CenterContainer/MainPanel/MainContainer/StatsContainer/TeamStats/TeamStatsPanel/TeamStatsContainer"]
layout_mode = 2
text = "Survived: 3/5"

[node name="PropsSurvivalRate" type="Label" parent="CenterContainer/MainPanel/MainContainer/StatsContainer/TeamStats/TeamStatsPanel/TeamStatsContainer"]
layout_mode = 2
text = "Survival Rate: 60%"

[node name="HSeparator2" type="HSeparator" parent="CenterContainer/MainPanel/MainContainer/StatsContainer/TeamStats/TeamStatsPanel/TeamStatsContainer"]
layout_mode = 2

[node name="HauntersStatsLabel" type="Label" parent="CenterContainer/MainPanel/MainContainer/StatsContainer/TeamStats/TeamStatsPanel/TeamStatsContainer"]
layout_mode = 2
text = "HAUNTERS TEAM"
horizontal_alignment = 1

[node name="HauntersEliminations" type="Label" parent="CenterContainer/MainPanel/MainContainer/StatsContainer/TeamStats/TeamStatsPanel/TeamStatsContainer"]
layout_mode = 2
text = "Eliminations: 2"

[node name="HauntersAccuracy" type="Label" parent="CenterContainer/MainPanel/MainContainer/StatsContainer/TeamStats/TeamStatsPanel/TeamStatsContainer"]
layout_mode = 2
text = "Accuracy: 45%"

[node name="VSeparator" type="VSeparator" parent="CenterContainer/MainPanel/MainContainer/StatsContainer"]
layout_mode = 2

[node name="Leaderboard" type="VBoxContainer" parent="CenterContainer/MainPanel/MainContainer/StatsContainer"]
layout_mode = 2
size_flags_horizontal = 3

[node name="LeaderboardLabel" type="Label" parent="CenterContainer/MainPanel/MainContainer/StatsContainer/Leaderboard"]
layout_mode = 2
text = "PLAYER LEADERBOARD"
horizontal_alignment = 1

[node name="LeaderboardPanel" type="Panel" parent="CenterContainer/MainPanel/MainContainer/StatsContainer/Leaderboard"]
layout_mode = 2
size_flags_vertical = 3

[node name="LeaderboardScroll" type="ScrollContainer" parent="CenterContainer/MainPanel/MainContainer/StatsContainer/Leaderboard/LeaderboardPanel"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 10.0
offset_top = 10.0
offset_right = -10.0
offset_bottom = -10.0

[node name="LeaderboardList" type="VBoxContainer" parent="CenterContainer/MainPanel/MainContainer/StatsContainer/Leaderboard/LeaderboardPanel/LeaderboardScroll"]
layout_mode = 2
size_flags_horizontal = 3

[node name="HSeparator3" type="HSeparator" parent="CenterContainer/MainPanel/MainContainer"]
layout_mode = 2

[node name="RoundDetails" type="VBoxContainer" parent="CenterContainer/MainPanel/MainContainer"]
layout_mode = 2

[node name="RoundDetailsLabel" type="Label" parent="CenterContainer/MainPanel/MainContainer/RoundDetails"]
layout_mode = 2
text = "ROUND DETAILS"
horizontal_alignment = 1

[node name="RoundDetailsContainer" type="HBoxContainer" parent="CenterContainer/MainPanel/MainContainer/RoundDetails"]
layout_mode = 2

[node name="DetailsLeft" type="VBoxContainer" parent="CenterContainer/MainPanel/MainContainer/RoundDetails/RoundDetailsContainer"]
layout_mode = 2
size_flags_horizontal = 3

[node name="ShotsLabel" type="Label" parent="CenterContainer/MainPanel/MainContainer/RoundDetails/RoundDetailsContainer/DetailsLeft"]
layout_mode = 2
text = "Shots Fired: 0"

[node name="GrenadesLabel" type="Label" parent="CenterContainer/MainPanel/MainContainer/RoundDetails/RoundDetailsContainer/DetailsLeft"]
layout_mode = 2
text = "Grenades Thrown: 0"

[node name="DetailsRight" type="VBoxContainer" parent="CenterContainer/MainPanel/MainContainer/RoundDetails/RoundDetailsContainer"]
layout_mode = 2
size_flags_horizontal = 3

[node name="HexesLabel" type="Label" parent="CenterContainer/MainPanel/MainContainer/RoundDetails/RoundDetailsContainer/DetailsRight"]
layout_mode = 2
text = "Hexes Cast: 0"

[node name="DisguisesLabel" type="Label" parent="CenterContainer/MainPanel/MainContainer/RoundDetails/RoundDetailsContainer/DetailsRight"]
layout_mode = 2
text = "Disguise Changes: 0"

[node name="HSeparator4" type="HSeparator" parent="CenterContainer/MainPanel/MainContainer"]
layout_mode = 2

[node name="ButtonContainer" type="HBoxContainer" parent="CenterContainer/MainPanel/MainContainer"]
layout_mode = 2

[node name="NextRoundButton" type="Button" parent="CenterContainer/MainPanel/MainContainer/ButtonContainer"]
layout_mode = 2
size_flags_horizontal = 3
text = "NEXT ROUND"

[node name="LeaveGameButton" type="Button" parent="CenterContainer/MainPanel/MainContainer/ButtonContainer"]
layout_mode = 2
size_flags_horizontal = 3
text = "LEAVE GAME"

[node name="CountdownLabel" type="Label" parent="CenterContainer/MainPanel/MainContainer"]
layout_mode = 2
text = "Next round in: 15 seconds"
horizontal_alignment = 1

[connection signal="pressed" from="CenterContainer/MainPanel/MainContainer/ButtonContainer/NextRoundButton" to="." method="_on_next_round_pressed"]
[connection signal="pressed" from="CenterContainer/MainPanel/MainContainer/ButtonContainer/LeaveGameButton" to="." method="_on_leave_game_pressed"]
