[gd_scene load_steps=4 format=3 uid="uid://cerktj6el2ycl"]

[ext_resource type="PackedScene" uid="uid://cy82bsxfj1ddv" path="res://prophaunt/scenes/PropObject.tscn" id="1_yhma4"]
[ext_resource type="PackedScene" uid="uid://d0qhqcnlekftg" path="res://prophaunt/maps/Source/ElectricalItem/television_vintage.tscn" id="2_bc3yq"]

[sub_resource type="BoxShape3D" id="BoxShape3D_bx0gf"]
size = Vector3(1.64893, 1.08878, 1.07424)

[node name="TelevisionModernProp" instance=ExtResource("1_yhma4")]

[node name="televisionVintage" parent="Meshes" index="0" instance=ExtResource("2_bc3yq")]
transform = Transform3D(-1, 0, -8.74228e-08, 0, 1, 0, 8.74228e-08, 0, -1, 0, 0, 0)

[node name="CollisionShape3D" type="CollisionShape3D" parent="StaticBody3D" index="0"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -0.0563964, 0.554747, 0.154882)
shape = SubResource("BoxShape3D_bx0gf")
