[gd_scene load_steps=4 format=3 uid="uid://dcm2hbcw5yryc"]

[ext_resource type="PackedScene" uid="uid://cy82bsxfj1ddv" path="res://prophaunt/scenes/PropObject.tscn" id="1_1ko84"]
[ext_resource type="PackedScene" uid="uid://iqc81kn83ynm" path="res://prophaunt/maps/Source/Furniture/bed_double.tscn" id="2_qnlai"]

[sub_resource type="BoxShape3D" id="BoxShape3D_5biv6"]
size = Vector3(0.975098, 0.396057, 1.14136)

[node name="BedDoubleProp" instance=ExtResource("1_1ko84")]

[node name="BedDouble" parent="Meshes" index="0" instance=ExtResource("2_qnlai")]

[node name="CollisionShape3D" type="CollisionShape3D" parent="StaticBody3D" index="0"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -0.000244141, 0.191498, 0.0272217)
shape = SubResource("BoxShape3D_5biv6")
