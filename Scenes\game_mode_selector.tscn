[gd_scene load_steps=3 format=3 uid="uid://bqxvn8ywqp8ys"]

[ext_resource type="Script" path="res://Scenes/game_mode_selector.gd" id="1_x8k9p"]

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_1"]
bg_color = Color(0.2, 0.2, 0.2, 0.8)
corner_radius_top_left = 10
corner_radius_top_right = 10
corner_radius_bottom_right = 10
corner_radius_bottom_left = 10

[node name="GameModeSelector" type="Control"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
script = ExtResource("1_x8k9p")

[node name="Background" type="ColorRect" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
color = Color(0, 0, 0, 0.5)

[node name="CenterContainer" type="CenterContainer" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2

[node name="Panel" type="Panel" parent="CenterContainer"]
layout_mode = 2
theme_override_styles/panel = SubResource("StyleBoxFlat_1")

[node name="VBoxContainer" type="VBoxContainer" parent="CenterContainer/Panel"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 20.0
offset_top = 20.0
offset_right = -20.0
offset_bottom = -20.0
grow_horizontal = 2
grow_vertical = 2

[node name="Title" type="Label" parent="CenterContainer/Panel/VBoxContainer"]
layout_mode = 2
text = "SELECT GAME MODE"
horizontal_alignment = 1

[node name="HSeparator" type="HSeparator" parent="CenterContainer/Panel/VBoxContainer"]
layout_mode = 2

[node name="RaceButton" type="Button" parent="CenterContainer/Panel/VBoxContainer"]
layout_mode = 2
text = "RACE MODE"

[node name="FreeRideButton" type="Button" parent="CenterContainer/Panel/VBoxContainer"]
layout_mode = 2
text = "FREERIDE MODE"

[node name="ProphauntButton" type="Button" parent="CenterContainer/Panel/VBoxContainer"]
layout_mode = 2
text = "PROPHAUNT MODE"

[node name="HSeparator2" type="HSeparator" parent="CenterContainer/Panel/VBoxContainer"]
layout_mode = 2

[node name="BackButton" type="Button" parent="CenterContainer/Panel/VBoxContainer"]
layout_mode = 2
text = "BACK"

[connection signal="pressed" from="CenterContainer/Panel/VBoxContainer/RaceButton" to="." method="_on_race_button_pressed"]
[connection signal="pressed" from="CenterContainer/Panel/VBoxContainer/FreeRideButton" to="." method="_on_free_ride_button_pressed"]
[connection signal="pressed" from="CenterContainer/Panel/VBoxContainer/ProphauntButton" to="." method="_on_prophaunt_button_pressed"]
[connection signal="pressed" from="CenterContainer/Panel/VBoxContainer/BackButton" to="." method="_on_back_button_pressed"]
