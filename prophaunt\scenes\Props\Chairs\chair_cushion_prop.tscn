[gd_scene load_steps=4 format=3 uid="uid://bc4bwdyw61enn"]

[ext_resource type="PackedScene" uid="uid://cy82bsxfj1ddv" path="res://prophaunt/scenes/PropObject.tscn" id="1_d51vg"]
[ext_resource type="PackedScene" uid="uid://c1jd5xl8m7s2m" path="res://prophaunt/maps/Source/Chairs/chair_cushion.tscn" id="2_h3gy0"]

[sub_resource type="BoxShape3D" id="BoxShape3D_v6hhu"]
size = Vector3(0.817246, 1.83334, 0.812683)

[node name="BenchCushionProp" instance=ExtResource("1_d51vg")]

[node name="ChairCushion" parent="Meshes" index="0" instance=ExtResource("2_h3gy0")]

[node name="CollisionShape3D" type="CollisionShape3D" parent="StaticBody3D" index="0"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -0.0051499, 0.914841, 0.00973506)
shape = SubResource("BoxShape3D_v6hhu")
