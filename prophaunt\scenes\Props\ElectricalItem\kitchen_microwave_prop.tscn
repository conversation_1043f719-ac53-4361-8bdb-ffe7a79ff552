[gd_scene load_steps=4 format=3 uid="uid://6u6hki8qkoo7"]

[ext_resource type="PackedScene" uid="uid://cy82bsxfj1ddv" path="res://prophaunt/scenes/PropObject.tscn" id="1_xgvvn"]
[ext_resource type="PackedScene" uid="uid://bnxxn8tfiuhge" path="res://prophaunt/maps/Source/ElectricalItem/kitchen_microwave.tscn" id="2_tbuax"]

[sub_resource type="BoxShape3D" id="BoxShape3D_bx0gf"]
size = Vector3(1.18161, 0.749692, 0.929226)

[node name="KitchenMicrowaveProp" instance=ExtResource("1_xgvvn")]

[node name="KitchenMicrowave" parent="Meshes" index="0" instance=ExtResource("2_tbuax")]

[node name="CollisionShape3D" type="CollisionShape3D" parent="StaticBody3D" index="0"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0.109533, 0.385205, -0.104422)
shape = SubResource("BoxShape3D_bx0gf")
