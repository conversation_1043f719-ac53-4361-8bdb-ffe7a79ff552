[gd_scene load_steps=4 format=3 uid="uid://dqaugbiclfr56"]

[ext_resource type="Script" path="res://prophaunt/scenes/PropObject.gd" id="1_immab"]
[ext_resource type="PackedScene" uid="uid://cv8fbxq2cs11c" path="res://prophaunt/maps/Source/Item/box_closed.tscn" id="2_ocvmf"]

[sub_resource type="BoxShape3D" id="BoxShape3D_qqs5e"]
size = Vector3(0.42974, 0.561684, 0.427597)

[node name="BoxCloseProp" type="Node3D" node_paths=PackedStringArray("export_mesh") groups=["PropObject"]]
script = ExtResource("1_immab")
export_mesh = NodePath("Meshes")

[node name="Meshes" type="Node3D" parent="."]

[node name="BoxClosed" parent="Meshes" instance=ExtResource("2_ocvmf")]

[node name="StaticBody3D" type="StaticBody3D" parent="."]
collision_layer = 2

[node name="CollisionShape3D" type="CollisionShape3D" parent="StaticBody3D"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0.000594627, 0.278217, 8.38898e-05)
shape = SubResource("BoxShape3D_qqs5e")
