[gd_scene load_steps=4 format=3 uid="uid://dk4c2lsskuhtw"]

[ext_resource type="Script" path="res://prophaunt/scenes/PropObject.gd" id="1_vbiwf"]
[ext_resource type="PackedScene" uid="uid://bw8v7r2u8ams" path="res://prophaunt/maps/Source/Item/box_open.tscn" id="2_vle77"]

[sub_resource type="BoxShape3D" id="BoxShape3D_qqs5e"]
size = Vector3(0.437896, 0.447834, 0.431335)

[node name="BoxOpenProp" type="Node3D" node_paths=PackedStringArray("export_mesh") groups=["PropObject"]]
script = ExtResource("1_vbiwf")
export_mesh = NodePath("Meshes")

[node name="Meshes" type="Node3D" parent="."]

[node name="BoxOpen" parent="Meshes" instance=ExtResource("2_vle77")]

[node name="StaticBody3D" type="StaticBody3D" parent="."]
collision_layer = 2

[node name="CollisionShape3D" type="CollisionShape3D" parent="StaticBody3D"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0.00333628, 0.221292, -0.00265506)
shape = SubResource("BoxShape3D_qqs5e")
