[gd_scene load_steps=4 format=3 uid="uid://5rc0r1cd3f6a"]

[ext_resource type="PackedScene" uid="uid://cy82bsxfj1ddv" path="res://prophaunt/scenes/PropObject.tscn" id="1_6e5tl"]
[ext_resource type="PackedScene" uid="uid://b88x1sn7wfeyc" path="res://Scenes/FreeRide/Assets/NetworkNodes/MiniGames/PvP/BattleHeroes/Weapon/RostamMace.tscn" id="2_6cgyg"]

[sub_resource type="CapsuleShape3D" id="CapsuleShape3D_5q4rx"]
radius = 0.146802
height = 1.42397

[node name="RostamMaceProp" instance=ExtResource("1_6e5tl")]

[node name="RostamMace" parent="Meshes" index="0" instance=ExtResource("2_6cgyg")]

[node name="CollisionShape3D" type="CollisionShape3D" parent="StaticBody3D" index="0"]
transform = Transform3D(1, 0, 0, 0, -4.37114e-08, -1, 0, 1, -4.37114e-08, 0, 0, 0.429272)
shape = SubResource("CapsuleShape3D_5q4rx")
