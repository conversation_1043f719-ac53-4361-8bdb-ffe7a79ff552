[gd_scene load_steps=4 format=3 uid="uid://dr1slgq55fu3t"]

[ext_resource type="PackedScene" uid="uid://cy82bsxfj1ddv" path="res://prophaunt/scenes/PropObject.tscn" id="1_bssqq"]
[ext_resource type="PackedScene" uid="uid://c35maga2uxb2n" path="res://prophaunt/maps/Source/Bathroom/bathroom_cabinet_drawer.tscn" id="2_biv1k"]

[sub_resource type="BoxShape3D" id="BoxShape3D_f8ox4"]
size = Vector3(1.15654, 1.25691, 1.03716)

[node name="BathroomCabinetProp" instance=ExtResource("1_bssqq")]

[node name="bathroomCabinetDrawer" parent="Meshes" index="0" instance=ExtResource("2_biv1k")]

[node name="CollisionShape3D" type="CollisionShape3D" parent="StaticBody3D" index="0"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0.00203395, 0.628456, 0.106619)
shape = SubResource("BoxShape3D_f8ox4")
