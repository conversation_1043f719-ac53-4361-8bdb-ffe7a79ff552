[gd_scene load_steps=5 format=4 uid="uid://dsxepmaq5unbe"]

[ext_resource type="Material" uid="uid://dkmcjc7wc3ws6" path="res://prophaunt/Mat/GrassPath.tres" id="1_45fqm"]

[sub_resource type="ArrayMesh" id="ArrayMesh_bvfqq"]
_surfaces = [{
"aabb": AABB(-1, -0.1, -1, 2, 0.1, 2),
"format": 34896613377,
"index_count": 114,
"index_data": PackedByteArray("AgAAAAEAAQADAAIAAQAEAAMABAAFAAMACAAGAAcABwAJAAgACQAKAAgACgALAAgACgAMAAsADwANAA4ADgAQAA8ADgARABAAEQASABAAEQATABIAEwAUABIAEwAVABQAFQAWABQAFQAXABYAGAACAAMAAwAFABgADwAZAA0ADQAZABoADgANABoAEQAOABoAEwARABoAFQATABoAGgAbABUAGwAXABUACQAUABYAFgAKAAkABwASABQAFAAJAAcABgAQABIAEgAHAAYAAQALAAwADAAEAAEAAAAIAAsACwABAAAA"),
"lods": [0.151581, PackedByteArray("AgAAAAQAGAACAAQABAAFABgACwAEAAAABAALAAwAAAAIAAsACAAGAAcABwAKAAgACgALAAgACgAMAAsAGgAHABAABgAQAAcAEAAPABoADwAZABoAGgAbAAcABwAbABYAGwAXABYAFgAKAAcA")],
"name": "Material",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 28,
"vertex_data": PackedByteArray("kzj+/wAAAACkJv7/pCYAAPke/v8AAAAAKBX+/ygVAAAAAP7/kzgAAAAA/v/5HgAAjLcAAAAAAAD1ngAAxlsAAHJIAAAAAAAAxlsAAPWeAAAAAAAAjLcAAHsxAAB7MQAAAAAAAHJIAABF9/7/QUIAALLd/v//fwAABeH+/wAAAABrx/7/AAAAAAS1/v8EtQAAtKz+/7VjAAD/f/7/st0AALVj/v+0rAAAQUL+/0X3AAAAAP7/a8cAAAAA/v8F4QAAAAD+/wAAAAD///7/AAAAAP///////wAAAAD/////AAA=")
}]
blend_shape_mode = 0

[sub_resource type="ArrayMesh" id="ArrayMesh_i1cnb"]
resource_name = "GroundPathBend_GroundPathBend"
_surfaces = [{
"aabb": AABB(-1, -0.1, -1, 2, 0.1, 2),
"attribute_data": PackedByteArray("urDswLqw7MDqoOLe6qDi3uqg4t6hmw3AUpMN0ZWC4O2VguDtUIIa2WkYAP7m2FCCpQGS1vPMQa3zzEGtDj5b2PDAfMOlAQupL61fzS+tX81pGJ2BUIKU2hI1zr9ArDbqQKw26g4+QqfehIT+CfmBotHs+r967vSCTuLtgk7i7YJJ2XHZaNUgsmjVILJo1SCyCsA77Uqyx9VKssfVSrLH1dmi6/k8gynkPIMp5H2DGPBQgtS/MP33gjD9uPy7g4T+urDswPPMQa1QgpTa"),
"format": 34896613399,
"index_count": 114,
"index_data": PackedByteArray("BQAAAAIAAgAGAAUAAgAHAAYABwAJAAYADwAKAAwADAARAA8AEQAUAA8AFAAWAA8AFAAZABYAHQAbABwAHAAeAB0AHAAgAB4AIAAhAB4AIAAkACEAJAAlACEAJAAoACUAKAApACUAKAArACkALAAFAAYABgAJACwAHQAtABsAGwAtAC4AHAAbAC4AIAAcAC4AJAAgAC4AKAAkAC4ALgAvACgALwArACgAEgAmACoAKgAVABIADQAiACcAJwATAA0ACwAfACMAIwAOAAsAAwAXABoAGgAIAAMAAQAQABgAGAAEAAEA"),
"lods": [0.151581, PackedByteArray("BQAwAAcALAAFAAcABwAJACwAFwAHADAABwAXABoAMAAQABcADwAKAAwADAAUAA8AFAAWAA8AFAAZABYALgAxAB4ACwAeADEAHgAdAC4AHQAtAC4ALgAvADEAMQAvACkALwArACkAKQAyADEA")],
"material": ExtResource("1_45fqm"),
"name": "Material",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 51,
"vertex_data": PackedByteArray("kzj+/wAABMCTOP7/AAD2t6Qm/v+kJv+/pCb+/6QmSaekJv7/pCa8t/ke/v8AAADAKBX+/ygV/78AAP7/kzj/vwAA/v+TONqmAAD+//ke/7+MtwAAAACq6Yy3AAAAAHLL9Z4AAMZbqun1ngAAxluP1fWeAADGWw/MckgAAAAAqulySAAAAAAFuMZbAAD1nqrpxlsAAPWegNvGWwAA9Z4E1gAAAACMt6rpAAAAAIy3gdt7MQAAezGr6XsxAAB7MWenezEAAHsx0bcAAAAAckiq6QAAAABySAKnRff+/0FC/7+y3f7//3//vwXh/v8AAP+/a8f+/wAA/79rx/7/AABeywS1/v8Etf+/tKz+/7VjA8C0rP7/tWOB1bSs/v+1Y/XL/3/+/7Ld/7+1Y/7/tKwAwLVj/v+0rIDbtWP+/7Ss8dVBQv7/RfcAwAAA/v9rxwDAAAD+/2vHgNsAAP7/BeEAwAAA/v8AAP+////+/wAA/7//////////vwAA/////wDAkzj+/wAA2Lf1ngAAxlvIxQAAAACMt6PIW/25elPGmGD1/wSAqdSGbVfG2V++/n59Of9igIr+uYC+1W5rg/89gPrK9hVw59+x+sr2FbvrtqVs6W2x+sr1FVLGyGD6yvYVF/aekEvuyqT6yvYVLfaUkPnK9RVk1AxuVcYgYPrK9hVU1TtsYP9OgPv+gYDu/95/pP9JfzDn7bES/3WAcf3jemnr1KUZ6YCxWv7RgMH9HoEU9p+Q3+3xpJr9MYHH/JuBKfaVkMb9G4HH/49//P/6f2f+y4DV/RSB9eqlaBv8m52F/hOM")
}]
blend_shape_mode = 0
shadow_mesh = SubResource("ArrayMesh_bvfqq")

[sub_resource type="ConcavePolygonShape3D" id="ConcavePolygonShape3D_ykaed"]
data = PackedVector3Array(-0.758, 0, -1, -0.558, 0, -1, -0.6981, 0, -0.6981, -0.6981, 0, -0.6981, -0.8347, 0, -0.8347, -0.758, 0, -1, -0.6981, 0, -0.6981, -1, 0, -0.558, -0.8347, 0, -0.8347, -1, 0, -0.558, -1, 0, -0.758, -0.8347, 0, -0.8347, -0.434, -0.1, -1, 0.434, -0.1, -1, 0.2419, -0.1, -0.283, 0.2419, -0.1, -0.283, -0.283, -0.1, 0.2419, -0.434, -0.1, -1, -0.283, -0.1, 0.2419, -1, -0.1, 0.434, -0.434, -0.1, -1, -1, -0.1, 0.434, -0.6134, -0.1, -0.6134, -0.434, -0.1, -1, -1, -0.1, 0.434, -1, -0.1, -0.434, -0.6134, -0.1, -0.6134, 0.758, 0, -1, 0.9318, 0, -0.4824, 0.732, 0, 0, 0.732, 0, 0, 0.558, 0, -1, 0.758, 0, -1, 0.732, 0, 0, 0.4142, 0, 0.4142, 0.558, 0, -1, 0.4142, 0, 0.4142, 0.3493, 0, -0.221, 0.558, 0, -1, 0.4142, 0, 0.4142, 0, 0, 0.732, 0.3493, 0, -0.221, 0, 0, 0.732, -0.221, 0, 0.3493, 0.3493, 0, -0.221, 0, 0, 0.732, -0.4824, 0, 0.9318, -0.221, 0, 0.3493, -0.4824, 0, 0.9318, -1, 0, 0.558, -0.221, 0, 0.3493, -0.4824, 0, 0.9318, -1, 0, 0.758, -1, 0, 0.558, -1, 0, -1, -0.758, 0, -1, -0.8347, 0, -0.8347, -0.8347, 0, -0.8347, -1, 0, -0.758, -1, 0, -1, 0.758, 0, -1, 1, 0, -1, 0.9318, 0, -0.4824, 0.9318, 0, -0.4824, 1, 0, -1, 1, 0, 1, 0.732, 0, 0, 0.9318, 0, -0.4824, 1, 0, 1, 0.4142, 0, 0.4142, 0.732, 0, 0, 1, 0, 1, 0, 0, 0.732, 0.4142, 0, 0.4142, 1, 0, 1, -0.4824, 0, 0.9318, 0, 0, 0.732, 1, 0, 1, 1, 0, 1, -1, 0, 1, -0.4824, 0, 0.9318, -1, 0, 1, -1, 0, 0.758, -0.4824, 0, 0.9318, -0.283, -0.1, 0.2419, -0.221, 0, 0.3493, -1, 0, 0.558, -1, 0, 0.558, -1, -0.1, 0.434, -0.283, -0.1, 0.2419, 0.2419, -0.1, -0.283, 0.3493, 0, -0.221, -0.221, 0, 0.3493, -0.221, 0, 0.3493, -0.283, -0.1, 0.2419, 0.2419, -0.1, -0.283, 0.434, -0.1, -1, 0.558, 0, -1, 0.3493, 0, -0.221, 0.3493, 0, -0.221, 0.2419, -0.1, -0.283, 0.434, -0.1, -1, -0.6981, 0, -0.6981, -0.6134, -0.1, -0.6134, -1, -0.1, -0.434, -1, -0.1, -0.434, -1, 0, -0.558, -0.6981, 0, -0.6981, -0.558, 0, -1, -0.434, -0.1, -1, -0.6134, -0.1, -0.6134, -0.6134, -0.1, -0.6134, -0.6981, 0, -0.6981, -0.558, 0, -1)

[node name="GroundPathBend" type="MeshInstance3D"]
mesh = SubResource("ArrayMesh_i1cnb")
skeleton = NodePath("")

[node name="StaticBody3D2" type="StaticBody3D" parent="."]

[node name="CollisionShape3D" type="CollisionShape3D" parent="StaticBody3D2"]
shape = SubResource("ConcavePolygonShape3D_ykaed")
