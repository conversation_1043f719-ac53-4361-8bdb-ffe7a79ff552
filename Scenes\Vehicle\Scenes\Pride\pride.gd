extends MotorVehicle

@onready var tire_b1: MeshInstance3D = $Holder/Tire04
@onready var tire_b2: MeshInstance3D = $Holder/Tire03
@onready var tire_f1: MeshInstance3D = $Holder/Tire02
@onready var tire_f2: MeshInstance3D = $Holder/Tire01
@onready var f_left: Wheel = $FLeft
@onready var r_left: Wheel = $RLeft

func _physics_process(delta: float) -> void:
	super(delta)
	
	tire_f1.rotation.y = -wheel_orientation * f_right.rotation.x
	tire_f2.rotation.y = -wheel_orientation * f_right.rotation.x
	tire_b1.rotation.x = -wheel_orientation * r_right.rotation.x
	tire_b2.rotation.x = wheel_orientation * r_right.rotation.x
