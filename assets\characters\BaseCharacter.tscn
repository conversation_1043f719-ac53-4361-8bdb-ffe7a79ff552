[gd_scene load_steps=26 format=3 uid="uid://chudxjg8xmg7d"]

[ext_resource type="Script" path="res://Scenes/player/character.gd" id="1_16nw7"]
[ext_resource type="PackedScene" uid="uid://demfjgiula84y" path="res://Scenes/WorldItems/player_area.tscn" id="1_pj2lb"]
[ext_resource type="FontFile" uid="uid://djnxbn02v2032" path="res://Scenes/ui/assets/Font/Lalezar-Regular.ttf" id="3_t5rwj"]
[ext_resource type="PackedScene" uid="uid://bu3o704fe06dp" path="res://assets/characters/particles_trail.tscn" id="4_6o42b"]
[ext_resource type="Script" path="res://networking/ClientNetworkHistory.gd" id="5_ey1vd"]
[ext_resource type="Script" path="res://networking/ServerNetworkHistory.gd" id="6_jstcu"]
[ext_resource type="Texture2D" uid="uid://cyxvrrhi1qi27" path="res://Scenes/ui/assets/emotes/emoteC__.png" id="7_huykv"]
[ext_resource type="SpriteFrames" uid="uid://ckkfownbmjkya" path="res://Scenes/ui/assets/emotes/emote_touch_frames.tres" id="8_b5xbw"]
[ext_resource type="StyleBox" uid="uid://cu27huerhev1g" path="res://Scenes/ui/chat/left_chat_message_bg.tres" id="9_ew52j"]
[ext_resource type="Script" path="res://assets/characters/AnimationActionState.gd" id="10_ywer7"]
[ext_resource type="Script" path="res://assets/characters/DeadState.gd" id="11_2eadl"]
[ext_resource type="Script" path="res://assets/characters/MobileState.gd" id="11_knkpj"]
[ext_resource type="Script" path="res://assets/characters/FreezeState.gd" id="12_mgjbh"]
[ext_resource type="Script" path="res://assets/characters/FollowPathState.gd" id="14_colow"]
[ext_resource type="Script" path="res://assets/characters/FreezeDeathState.gd" id="14_e55ib"]
[ext_resource type="Script" path="res://assets/characters/FollowRampState.gd" id="15_g1v4t"]
[ext_resource type="Script" path="res://assets/characters/RifleIdleState.gd" id="16_rifle_idle"]
[ext_resource type="Script" path="res://assets/characters/RifleRunState.gd" id="17_rifle_run"]
[ext_resource type="Script" path="res://assets/characters/RifleReloadState.gd" id="18_rifle_reload"]
[ext_resource type="Script" path="res://assets/characters/RifleReloadRunState.gd" id="19_rifle_reload_run"]
[ext_resource type="Script" path="res://assets/characters/RifleJumpState.gd" id="20_rifle_jump"]
[ext_resource type="Script" path="res://assets/characters/RifleShootState.gd" id="21_rifle_shoot"]
[ext_resource type="Script" path="res://assets/characters/RifleShootRunState.gd" id="22_rifle_shoot_run"]

[sub_resource type="CapsuleShape3D" id="CapsuleShape3D_7k0sf"]
radius = 0.4
height = 1.5

[sub_resource type="ViewportTexture" id="ViewportTexture_b5o7w"]
viewport_path = NodePath("ChatMessage/SubViewport")

[node name="BaseCharacter" type="CharacterBody3D"]
transform = Transform3D(-1, 0, 8.74228e-08, 0, 1, 0, -8.74228e-08, 0, -1, 0, 0, 0)
disable_mode = 1
collision_mask = 183955
floor_max_angle = 0.959931
script = ExtResource("1_16nw7")

[node name="CollisionShape3D" type="CollisionShape3D" parent="."]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0.732077, -0.0581012)
shape = SubResource("CapsuleShape3D_7k0sf")

[node name="PlayerArea" parent="." instance=ExtResource("1_pj2lb")]

[node name="NameLabel" type="Label3D" parent="."]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 1.86652, 0)
billboard = 1
text = "Name"
font = ExtResource("3_t5rwj")
font_size = 60

[node name="TitleLabel" type="Label3D" parent="."]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 2.15129, 0)
billboard = 1
text = "Title"
font = ExtResource("3_t5rwj")
font_size = 60

[node name="MoveParticle" parent="." instance=ExtResource("4_6o42b")]
emitting = false
lifetime = 0.5

[node name="ClientNetworkHistory" type="Node" parent="."]
script = ExtResource("5_ey1vd")

[node name="ServerNetworkHistory" type="Node" parent="."]
script = ExtResource("6_jstcu")

[node name="Emote" type="Sprite3D" parent="."]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 2.81316, 0)
billboard = 1
double_sided = false
texture = ExtResource("7_huykv")

[node name="AnimatedSprite" type="AnimatedSprite3D" parent="Emote"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -0.000166657, 0.0770206, -0.0286239)
billboard = 1
double_sided = false
render_priority = 1
sprite_frames = ExtResource("8_b5xbw")
animation = &"71"

[node name="ChatMessage" type="Sprite3D" parent="."]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 3.58114, 0)
visible = false
billboard = 1
double_sided = false
render_priority = 1
texture = SubResource("ViewportTexture_b5o7w")

[node name="SubViewport" type="SubViewport" parent="ChatMessage"]
disable_3d = true
transparent_bg = true
size = Vector2i(420, 200)

[node name="Panel" type="Panel" parent="ChatMessage/SubViewport"]
custom_minimum_size = Vector2(420, 200)
offset_right = 420.0
offset_bottom = 200.0
mouse_filter = 2
theme_override_styles/panel = ExtResource("9_ew52j")

[node name="Label" type="Label" parent="ChatMessage/SubViewport/Panel"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 10.0
offset_top = 21.0
offset_right = -8.0
offset_bottom = 15.0
grow_horizontal = 2
grow_vertical = 2
theme_override_fonts/font = ExtResource("3_t5rwj")
theme_override_font_sizes/font_size = 25
text = "aslasjfklasjas
faskfdasdasdasdasdasdasdasdasdasdas
afkas
fkas
fkf"
horizontal_alignment = 1
vertical_alignment = 1
autowrap_mode = 3
clip_text = true

[node name="AttackPlaceHolder" type="Marker3D" parent="."]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -5.52381e-08, 1.08622, -0.63185)

[node name="ThrowPlaceHolder" type="Marker3D" parent="."]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -2.08092e-08, 0.136486, -0.73303)
gizmo_extents = 0.1

[node name="ThrowPlaceHolder2" type="Marker3D" parent="."]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0.247751, 0.614589, -0.707315)

[node name="FrontCameraPlaceHolder" type="Marker3D" parent="."]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0.310886, 1.09842, -0.63185)

[node name="SelfieCameraPlaceHolder" type="Marker3D" parent="."]
transform = Transform3D(-1, 1.77282e-08, 8.56064e-08, 0, 0.979223, -0.202787, -8.74228e-08, -0.202787, -0.979223, 0, 0.792, -2.998)

[node name="States" type="Node" parent="."]

[node name="AnimationAction" type="Node" parent="States"]
script = ExtResource("10_ywer7")

[node name="MobileState" type="Node" parent="States"]
script = ExtResource("11_knkpj")

[node name="DeadState" type="Node" parent="States"]
script = ExtResource("11_2eadl")

[node name="FreezeState" type="Node" parent="States"]
script = ExtResource("12_mgjbh")

[node name="FreezeDeathState" type="Node" parent="States"]
script = ExtResource("14_e55ib")

[node name="FollowPathState" type="Node" parent="States"]
script = ExtResource("14_colow")

[node name="FollowRampState" type="Node" parent="States"]
script = ExtResource("15_g1v4t")

[node name="RifleIdleState" type="Node" parent="States"]
script = ExtResource("16_rifle_idle")

[node name="RifleRunState" type="Node" parent="States"]
script = ExtResource("17_rifle_run")

[node name="RifleReloadState" type="Node" parent="States"]
script = ExtResource("18_rifle_reload")

[node name="RifleReloadRunState" type="Node" parent="States"]
script = ExtResource("19_rifle_reload_run")

[node name="RifleJumpState" type="Node" parent="States"]
script = ExtResource("20_rifle_jump")

[node name="RifleShootState" type="Node" parent="States"]
script = ExtResource("21_rifle_shoot")

[node name="RifleShootRunState" type="Node" parent="States"]
script = ExtResource("22_rifle_shoot_run")

[connection signal="ball_entered" from="PlayerArea" to="." method="_on_player_area_ball_entered"]
