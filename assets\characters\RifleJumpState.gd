class_name RifleJumpState
extends Node

@onready var character: Character = $"../.."

func _ready() -> void:
	pass

func run(delta):
	if character.controls != Constants.Controls.Player or Constants.is_server:
		return

	# Check for reload input while jumping (if you want to allow this)
	if Input.is_action_just_pressed("reload"):
		character.rifleReloadState.start_state()
		return
	
	
	# Movement and landing are handled in handle_rifle_mode
	character.handle_rifle_mode(delta, true)
	character.check_rifle_shoot(character.rifleShootState)


func start_state():
	if character.state == character.State.RIFLE_JUMP:
		return
	
	character.state = character.State.RIFLE_JUMP
	# Animation will be handled in handle_animation function

func end_state():
	# Clean up when leaving this state
	pass
