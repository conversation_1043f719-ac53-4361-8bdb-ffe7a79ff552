[gd_scene load_steps=4 format=3 uid="uid://cs1hadq4bt2fj"]

[ext_resource type="PackedScene" uid="uid://cy82bsxfj1ddv" path="res://prophaunt/scenes/PropObject.tscn" id="1_uegib"]
[ext_resource type="PackedScene" uid="uid://dc40cn5hlfkmr" path="res://prophaunt/maps/Source/ArenaProp/column_small.tscn" id="2_alkl4"]

[sub_resource type="CapsuleShape3D" id="CapsuleShape3D_5q4rx"]
radius = 0.70781
height = 2.80667

[node name="ColumnSmallProp" instance=ExtResource("1_uegib")]

[node name="ColumnSmall" parent="Meshes" index="0" instance=ExtResource("2_alkl4")]

[node name="CollisionShape3D" type="CollisionShape3D" parent="StaticBody3D" index="0"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 1.21174, -0.010301)
shape = SubResource("CapsuleShape3D_5q4rx")
