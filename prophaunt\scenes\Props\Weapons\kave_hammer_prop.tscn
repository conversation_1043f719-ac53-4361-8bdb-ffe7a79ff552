[gd_scene load_steps=4 format=3 uid="uid://b8vmj2usxt25c"]

[ext_resource type="PackedScene" uid="uid://cy82bsxfj1ddv" path="res://prophaunt/scenes/PropObject.tscn" id="1_u42b3"]
[ext_resource type="PackedScene" uid="uid://bdgsbi4paiebx" path="res://Scenes/FreeRide/Assets/NetworkNodes/MiniGames/PvP/BattleHeroes/Weapon/KaveHammer.tscn" id="2_jauak"]

[sub_resource type="CapsuleShape3D" id="CapsuleShape3D_5q4rx"]
radius = 0.271715
height = 1.42397

[node name="KaveHammerProp" instance=ExtResource("1_u42b3")]

[node name="KaveHammer" parent="Meshes" index="0" instance=ExtResource("2_jauak")]

[node name="CollisionShape3D" type="CollisionShape3D" parent="StaticBody3D" index="0"]
transform = Transform3D(1, 0, 0, 0, -4.37114e-08, -1, 0, 1, -4.37114e-08, 0, 0, 0.429272)
shape = SubResource("CapsuleShape3D_5q4rx")
