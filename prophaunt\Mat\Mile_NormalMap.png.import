[remap]

importer="texture"
type="CompressedTexture2D"
uid="uid://ytk52uxejk6p"
path.s3tc="res://.godot/imported/Mile_NormalMap.png-7a36919469f97f66df28af8852d3685f.s3tc.ctex"
path.etc2="res://.godot/imported/Mile_NormalMap.png-7a36919469f97f66df28af8852d3685f.etc2.ctex"
metadata={
"imported_formats": ["s3tc_bptc", "etc2_astc"],
"vram_texture": true
}
generator_parameters={
"md5": "3988106d9666ecc838059c7bd60ad4ce"
}

[deps]

source_file="res://prophaunt/Mat/Mile_NormalMap.png"
dest_files=["res://.godot/imported/Mile_NormalMap.png-7a36919469f97f66df28af8852d3685f.s3tc.ctex", "res://.godot/imported/Mile_NormalMap.png-7a36919469f97f66df28af8852d3685f.etc2.ctex"]

[params]

compress/mode=2
compress/high_quality=false
compress/lossy_quality=0.7
compress/hdr_compression=1
compress/normal_map=1
compress/channel_pack=0
mipmaps/generate=true
mipmaps/limit=-1
roughness/mode=1
roughness/src_normal="res://prophaunt/maps/Office/Source/Office/BoxcaseWide_NormalMap.png"
process/fix_alpha_border=true
process/premult_alpha=false
process/normal_map_invert_y=false
process/hdr_as_srgb=false
process/hdr_clamp_exposure=false
process/size_limit=0
detect_3d/compress_to=0
