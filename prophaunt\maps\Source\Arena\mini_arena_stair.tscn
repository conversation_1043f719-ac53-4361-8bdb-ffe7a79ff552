[gd_scene load_steps=5 format=4 uid="uid://b0totytwug7as"]

[ext_resource type="Material" uid="uid://b60k1mvxam058" path="res://prophaunt/Mat/ArenaColorMap.tres" id="1_qspma"]

[sub_resource type="ArrayMesh" id="ArrayMesh_l83wn"]
_surfaces = [{
"aabb": AABB(-1, -7.54979e-08, -1, 2, 1, 2),
"format": 34359742465,
"index_count": 132,
"index_data": PackedByteArray("AAABAAIAAwACAAEABAAFAAYABwAGAAUAAAAHAAUAAgAHAAAACAAAAAUACQAAAAgACAAKAAkACwAJAAoADAAJAAsACgANAAsADgALAA0ADwAFABAABAAQAAUABQAPAAgACgAIAA8ADQAKAAEACgAPAAEAEAABAA8AEQABABAAAQARAAMAEQAQABIAEwAGABQABgAHABQAEQAUAAcAEQASABQAFQAUABIAFgAMAAsAFwAUABUAEwAUABcACwAOABYAEwAXAAYABgAXAAQAEAAEABcAFQAQABcAEgAQABUAAAAJAAEAFgABAAkADAAWAAkADgABABYADQABAA4ABwACABEAAwARAAIA"),
"name": "colormap",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 24,
"vertex_data": PackedByteArray("AACAP6yqqj6qqqo+AACAv6yqqj6qqqo+AACAP6uqKj+pqqo+AACAv6uqKj+pqqo+AACAP///fz8BAIC/AACAP2khorMAAIC/AACAPwAAgD+uqqq+AACAP6uqKj+tqqq+AACAP2khojMAAIA/AACAP66qqj4AAIA/AACAv2khojMAAIA/zMzMvoyICD4AAIA/zMxMvq6qqj4AAIA/AACAv66qqj4AAIA/mpkZv66qqj4AAIA/AACAv2khorMAAIC/AACAv///fz8BAIC/AACAv6uqKj+tqqq+AACAvwAAgD+uqqq+zMxMPwAAgD+uqqq+mpkZP8zMTD+tqqq+zMzMPgAAgD+uqqq+zMzMvq2qqj7MzEw/mpkZP///fz+IiAi/")
}]
blend_shape_mode = 0

[sub_resource type="ArrayMesh" id="ArrayMesh_3aon4"]
resource_name = "MiniArenaStair_MiniArenaStair"
_surfaces = [{
"aabb": AABB(-1, -7.54979e-08, -1, 2, 1, 2),
"attribute_data": PackedByteArray("AAB4P4iIaD8AAMA9iIhoPwAAeD+IiGg/AAB4P4iIaD8AAMA9iIhoPwAAeD+IiGg/AAB4P3d3Vz8AAMA9d3dXPwAAeD93d1c/AAB4P3d3Vz8AAMA9d3dXPwAAeD93d1c/AAB4P2ZmRj8AAMA9ZmZGPwAAeD9mZkY/AAB4P5qZeT8AAHg/mpl5PwAAeD+amXk/AAB4P2ZmRj8AAMA9ZmZGPwAAeD9mZkY/AAB4P3d3Vz8AAMA9d3dXPwAAeD93d1c/AAB4P5qZeT8AAHg/mpl5PwAAeD+amXk/AAB4P4iIaD8AAMA9iIhoPwAAeD+IiGg/AAB4P5qZeT8AAHg/mpl5PwAAeD+amXk/AAB4P/nFcj8AAMA9+cVyPwAAeD+IiGg/AADAPYiIaD8AAMA9iIhoPwAAeD+IiGg/AADAPYiIaD8AAHg/iIhoPwAAeD+IiGg/AADAPYiIaD8AAMA9iIhoPwAAeD+amXk/AAB4P5qZeT8AAHg/mpl5PwAAeD9mZkY/AADAPWZmRj8AAHg/ZmZGPwAAeD93d1c/AADAPXd3Vz8AAHg/d3dXPwAAeD9mZkY/AADAPWZmRj8AAHg/ZmZGPwAAeD9mZkY/AADAPWZmRj8AAMA9ZmZGPwAAeD/Xo1A/AADAPdejUD8AAHg/ZmZGPwAAwD1mZkY/AADAPWZmRj8AAMA9iIhoPwAAwD2IiGg/AADAPWZmRj8AAMA9ZmZGPw=="),
"format": 34359742487,
"index_count": 132,
"index_data": PackedByteArray("AAADAAYACQAGAAMADgARABQAFwAUABEAAgAXABEACAAXAAIAGgACABEAHQACABoAGAAeABsAIQAbAB4AIwAbACEAHgAmACEAKQAhACYALAAPAC8ADAAvAA8AEAAtABkAHwAZAC0AKAAgAAUAIAAuAAUAMQAFAC4ANAAFADEABQA0AAsANAAxADcAOAASADsAEgAVADsAMgA7ABUAMgA1ADsAPQA7ADUAQAAlACIAQgA8AD8AOgA8AEIAIgArAEAAOQBDABMAEwBDAA0AMAANAEMAPgAwAEMANgAwAD4AAQAcAAQAQQAEABwAJABBABwAKgAEAEEAJwAEACoAFgAHADMACgAzAAcA"),
"material": ExtResource("1_qspma"),
"name": "colormap",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 68,
"vertex_data": PackedByteArray("AACAP6yqqj6qqqo+AACAP6yqqj6qqqo+AACAP6yqqj6qqqo+AACAv6yqqj6qqqo+AACAv6yqqj6qqqo+AACAv6yqqj6qqqo+AACAP6uqKj+pqqo+AACAP6uqKj+pqqo+AACAP6uqKj+pqqo+AACAv6uqKj+pqqo+AACAv6uqKj+pqqo+AACAv6uqKj+pqqo+AACAP///fz8BAIC/AACAP///fz8BAIC/AACAP///fz8BAIC/AACAP2khorMAAIC/AACAP2khorMAAIC/AACAP2khorMAAIC/AACAPwAAgD+uqqq+AACAPwAAgD+uqqq+AACAPwAAgD+uqqq+AACAP6uqKj+tqqq+AACAP6uqKj+tqqq+AACAP6uqKj+tqqq+AACAP2khojMAAIA/AACAP2khojMAAIA/AACAP2khojMAAIA/AACAP66qqj4AAIA/AACAP66qqj4AAIA/AACAP66qqj4AAIA/AACAv2khojMAAIA/AACAv2khojMAAIA/AACAv2khojMAAIA/zMzMvoyICD4AAIA/zMzMvoyICD4AAIA/zMxMvq6qqj4AAIA/zMxMvq6qqj4AAIA/zMxMvq6qqj4AAIA/AACAv66qqj4AAIA/AACAv66qqj4AAIA/AACAv66qqj4AAIA/mpkZv66qqj4AAIA/mpkZv66qqj4AAIA/mpkZv66qqj4AAIA/AACAv2khorMAAIC/AACAv2khorMAAIC/AACAv2khorMAAIC/AACAv///fz8BAIC/AACAv///fz8BAIC/AACAv///fz8BAIC/AACAv6uqKj+tqqq+AACAv6uqKj+tqqq+AACAv6uqKj+tqqq+AACAvwAAgD+uqqq+AACAvwAAgD+uqqq+AACAvwAAgD+uqqq+zMxMPwAAgD+uqqq+zMxMPwAAgD+uqqq+zMxMPwAAgD+uqqq+mpkZP8zMTD+tqqq+mpkZP8zMTD+tqqq+zMzMPgAAgD+uqqq+zMzMPgAAgD+uqqq+zMzMPgAAgD+uqqq+zMzMvq2qqj7MzEw/zMzMvq2qqj7MzEw/mpkZP///fz+IiAi/mpkZP///fz+IiAi//3//f////z//f///////v////3////+//3//f////z//f///////vwAA/3////+//3//f////z//f///////v////3////+//3//f////z//f///////vwAA/3////+//////////7//f///////v////3////+//////////7//fwAA////v////3////+//3//f////z//f///////v////3////+//3//f////z//f///////v////3////+//3//f////z//fwAA////v////3////+//3//f////z//f///////v////3////+//3//f////z//fwAA////vwAA/3////+//3//f////z//f/+/////P/9//3////8//3///////79UVaqq////P/9//3////8//3///////78AAP9/////v/9//3////8//3///////7+qqqqq////P/////////+//38AAP///78AAP9/////v/////////+//3///////78AAAmA////v/9//3////8//3///////79EIt3u////v/9//3////8//3///////78AAP9/////v/9//3////8//3///////79UVaqq////P/9//3////8//3//v////z//f/9/////P/9///////+/qqqqqv///z//f/+/////P/9///////+//3//v////z//f///////vw==")
}]
blend_shape_mode = 0
shadow_mesh = SubResource("ArrayMesh_l83wn")

[sub_resource type="ConcavePolygonShape3D" id="ConcavePolygonShape3D_pv2hq"]
data = PackedVector3Array(-1, 0, -0.3317, -1, 1, -1, -1, 1, -0.3317, -1, 0, -0.3317, -1, 0, -1, -1, 1, -1, -1, 0, -1, 1, 1, -1, -1, 1, -1, -1, 0, -1, 1, 0, -1, 1, 1, -1, 1, 0, -0.3317, 1, 0.0132, 1.6579, 1, 1, -0.3317, 1, 0, -0.3317, 1, 0, 1.6579, 1, 0.0132, 1.6579, 1, 0, 1.6579, -1, 0.0132, 1.6579, 1, 0.0132, 1.6579, 1, 0, 1.6579, -1, 0, 1.6579, -1, 0.0132, 1.6579, -1, 0, -0.3317, 1, 0, 1.6579, 1, 0, -0.3317, -1, 0, -0.3317, -1, 0, 1.6579, 1, 0, 1.6579, 1, 1, -0.3317, -1, 0.0132, 1.6579, -1, 1, -0.3317, 1, 1, -0.3317, 1, 0.0132, 1.6579, -1, 0.0132, 1.6579, 1, 1, -1, -1, 1, -0.3317, -1, 1, -1, 1, 1, -1, 1, 1, -0.3317, -1, 1, -0.3317, -1, 0, -1, 1, 0, -0.3317, 1, 0, -1, -1, 0, -1, -1, 0, -0.3317, 1, 0, -0.3317, 1, 0, -1, 1, 1, -0.3317, 1, 1, -1, 1, 0, -1, 1, 0, -0.3317, 1, 1, -0.3317, -1, 0, 1.6579, -1, 1, -0.3317, -1, 0.0132, 1.6579, -1, 0, 1.6579, -1, 0, -0.3317, -1, 1, -0.3317)

[node name="MiniArenaStair" type="MeshInstance3D"]
mesh = SubResource("ArrayMesh_3aon4")
skeleton = NodePath("")

[node name="StaticBody3D" type="StaticBody3D" parent="."]
collision_layer = 2

[node name="CollisionShape3D" type="CollisionShape3D" parent="StaticBody3D"]
shape = SubResource("ConcavePolygonShape3D_pv2hq")
