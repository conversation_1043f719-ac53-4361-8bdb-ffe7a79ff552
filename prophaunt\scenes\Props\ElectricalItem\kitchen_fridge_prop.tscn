[gd_scene load_steps=4 format=3 uid="uid://dbe6df3ij4mef"]

[ext_resource type="PackedScene" uid="uid://cy82bsxfj1ddv" path="res://prophaunt/scenes/PropObject.tscn" id="1_8eo2i"]
[ext_resource type="PackedScene" uid="uid://mcuxphs7tj6q" path="res://prophaunt/maps/Source/ElectricalItem/kitchen_fridge_large.tscn" id="2_c48sd"]

[sub_resource type="BoxShape3D" id="BoxShape3D_bx0gf"]
size = Vector3(2.11554, 3.68051, 1.56916)

[node name="kitchenFridgeLargeProp" instance=ExtResource("1_8eo2i")]

[node name="kitchenFridgeLarge" parent="Meshes" index="0" instance=ExtResource("2_c48sd")]

[node name="CollisionShape3D" type="CollisionShape3D" parent="StaticBody3D" index="0"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0.0285779, 1.85061, 0.0796265)
shape = SubResource("BoxShape3D_bx0gf")
