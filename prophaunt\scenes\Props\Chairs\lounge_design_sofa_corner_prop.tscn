[gd_scene load_steps=4 format=3 uid="uid://b14nox5t05h2l"]

[ext_resource type="PackedScene" uid="uid://cy82bsxfj1ddv" path="res://prophaunt/scenes/PropObject.tscn" id="1_ovhal"]
[ext_resource type="PackedScene" uid="uid://cfwa1b5c7q1n6" path="res://prophaunt/maps/Source/Furniture/lounge_design_sofa_corner.tscn" id="2_vd185"]

[sub_resource type="BoxShape3D" id="BoxShape3D_f8ox4"]
size = Vector3(5.43832, 1.62433, 1.61693)

[node name="LoungeDesignSofaCornerProp" instance=ExtResource("1_ovhal")]

[node name="LoungeDesignSofaCorner" parent="Meshes" index="0" instance=ExtResource("2_vd185")]

[node name="CollisionShape3D" type="CollisionShape3D" parent="StaticBody3D" index="0"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0.480524, 0.812164, 1.42441)
shape = SubResource("BoxShape3D_f8ox4")

[node name="CollisionShape3D2" type="CollisionShape3D" parent="StaticBody3D" index="1"]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -1.41948, 0.812164, -0.475586)
shape = SubResource("BoxShape3D_f8ox4")
