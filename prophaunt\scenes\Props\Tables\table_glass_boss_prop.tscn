[gd_scene load_steps=4 format=3 uid="uid://fs8oixfsuwu0"]

[ext_resource type="PackedScene" uid="uid://cy82bsxfj1ddv" path="res://prophaunt/scenes/PropObject.tscn" id="1_ly1of"]
[ext_resource type="PackedScene" uid="uid://w3h3owpjf3ck" path="res://prophaunt/maps/Source/Table/table_glass_boss.tscn" id="2_rmhbe"]

[sub_resource type="BoxShape3D" id="BoxShape3D_5kgdh"]
size = Vector3(3.41174, 1.32446, 1.81278)

[node name="TableClothProp" instance=ExtResource("1_ly1of")]

[node name="TableGlassBoss" parent="Meshes" index="0" instance=ExtResource("2_rmhbe")]

[node name="CollisionShape3D" type="CollisionShape3D" parent="StaticBody3D" index="0"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -0.00128162, 0.661499, 0.00473478)
shape = SubResource("BoxShape3D_5kgdh")
