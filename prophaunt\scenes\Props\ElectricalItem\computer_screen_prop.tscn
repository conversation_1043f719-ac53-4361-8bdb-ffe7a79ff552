[gd_scene load_steps=4 format=3 uid="uid://bstac2w45gnj"]

[ext_resource type="PackedScene" uid="uid://cy82bsxfj1ddv" path="res://prophaunt/scenes/PropObject.tscn" id="1_w4o8p"]
[ext_resource type="PackedScene" uid="uid://brj4v3n1j3tyb" path="res://prophaunt/maps/Source/ElectricalItem/computer_screen.tscn" id="2_wpery"]

[sub_resource type="BoxShape3D" id="BoxShape3D_bx0gf"]
size = Vector3(0.785782, 0.592415, 0.215814)

[node name="ComputerScreenProp" instance=ExtResource("1_w4o8p")]

[node name="ComputerScreen" parent="Meshes" index="0" instance=ExtResource("2_wpery")]

[node name="CollisionShape3D" type="CollisionShape3D" parent="StaticBody3D" index="0"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0.00919341, 0.294162, 0.0204681)
shape = SubResource("BoxShape3D_bx0gf")
