extends Node
class_name ProphauntDisguiseSystem

# Prophaunt disguise system for Props
# Handles visual disguises and prop transformations

var character: Character
var prophaunt_player: ProphauntPlayer

# Disguise data
var current_disguise: PropObject
var available_disguises: Array[PropObject] = []
var disguise_cooldown: float = 5.0
var last_disguise_change: float = 0.0

# Visual components
var disguise_mesh: Node3D
var disguise_collision: CollisionShape3D

signal disguise_applied(disguise_type: String)
signal disguise_removed()
signal disguise_cooldown_started(duration: float)

func _ready():
	setup_available_disguises()


func initialize(player_character: Character, prophaunt_comp: ProphauntPlayer):
	"""Initialize the disguise system"""
	character = player_character
	prophaunt_player = prophaunt_comp
	

func setup_available_disguises():
	"""Set up the list of available disguises"""
	#Constants.server.map.get_nodes_in_group()
	var objects = get_tree().get_nodes_in_group("PropObject")
	var index = 0
	for o in objects:
		available_disguises.append(o)
		o.index = index
		index += 1


func can_change_disguise() -> bool:
	"""Check if the player can change disguise"""
	if not prophaunt_player:
		return false
	
	if prophaunt_player.team != Constants.ProphauntTeam.PROPS:
		return false
	
	if prophaunt_player.player_state != Constants.ProphauntPlayerState.ALIVE:
		return false
	
	var current_time = Time.get_ticks_msec() / 1000.0
	return current_time - last_disguise_change >= disguise_cooldown


func apply_disguise(prop_object: PropObject) -> bool:
	"""Apply a specific disguise"""
	if not can_change_disguise():
		return false
	
	last_disguise_change = Time.get_ticks_msec() / 1000.0
	current_disguise = prop_object
	
	# Apply visual disguise
	apply_visual_disguise(prop_object)
	prop_object.disable()
	
	# Start cooldown
	disguise_cooldown_started.emit(disguise_cooldown)
	
	#disguise_applied.emit(disguise_type)
	return true


func remove_disguise():
	"""Remove current disguise"""
	if current_disguise == null:
		return
	
	# Restore original appearance
	restore_original_appearance()
	character.remove_prop_collisions()
	character.collision_shape.disabled = false
	
	current_disguise = null
	disguise_removed.emit()


func apply_visual_disguise(prop_object: PropObject):
	"""Apply the visual aspects of the disguise"""
	# Hide original character mesh
	
	# Load and apply disguise mesh
	disguise_mesh = prop_object.mesh.duplicate(true)
	character.collision_shape.disabled = true
	character.add_child(disguise_mesh)
	character.add_prop_collisions(prop_object)
	character.hide_armature_mesh()
	disguise_mesh.scale = prop_object.scale
	
	
	# Show disguise mesh
	disguise_mesh.visible = true
	
	#print("Applied disguise: ", prop_object.name)


func restore_original_appearance():
	"""Restore the character's original appearance"""
	# Hide disguise mesh
	if disguise_mesh:
		disguise_mesh.visible = false
	
	character.show_armature_mesh()
	print("Restored original appearance")


func get_random_disguise() -> PropObject:
	"""Get a random disguise type"""
	if available_disguises.size() == 0:
		return null
	
	return available_disguises[randi() % available_disguises.size()]


func find_disguise_by_index(index):
	return available_disguises[index]


func get_disguise_cooldown_remaining() -> float:
	"""Get remaining disguise cooldown"""
	var current_time = Time.get_ticks_msec() / 1000.0
	var remaining = disguise_cooldown - (current_time - last_disguise_change)
	return max(0, remaining)


func is_disguised() -> bool:
	"""Check if currently disguised"""
	return current_disguise != null


func get_current_disguise() -> PropObject:
	"""Get the current disguise type"""
	return current_disguise


# Map-specific disguise functions
func get_map_appropriate_disguises(map_name: String) -> Array:
	"""Get disguises appropriate for a specific map"""
	# This could be expanded to return different disguises based on the map
	match map_name:
		"warehouse":
			return ["box", "barrel", "crate"]
		"office":
			return ["chair", "table", "plant", "trash_can"]
		"house":
			return ["chair", "table", "plant", "lamp"]
		_:
			return available_disguises


# Hex ability for props
func cast_hex_ability() -> bool:
	"""Cast hex ability to stun nearby haunters"""
	if not prophaunt_player:
		return false
	
	if prophaunt_player.team != Constants.ProphauntTeam.PROPS:
		return false
	
	if prophaunt_player.hex_cooldown > 0:
		return false
	
	# Set cooldown
	prophaunt_player.hex_cooldown = Constants.PROPHAUNT_HEX_COOLDOWN
	
	# Send hex command to server
	if Constants.is_client() and character:
		var player_position = character.global_position
		multiplayer.rpc_id(1, "prophaunt_hex", player_position)
	
	# Create visual effect
	create_hex_effect()
	
	return true


func create_hex_effect():
	"""Create visual effect for hex casting"""
	if not character:
		return
	
	# This would create a visual effect around the character
	print("Hex effect created at: ", character.global_position)
	
	# Play sound
	SoundManager.play_3d_sound.rpc_id(1, character.global_position, 10, SoundManager.SOUND_TYPE.ORDER)


# Movement restrictions while disguised
func should_restrict_movement() -> bool:
	"""Check if movement should be restricted while disguised"""
	# Some disguises might restrict movement to maintain the illusion
	return false  # For now, allow full movement


func get_movement_speed_modifier() -> float:
	"""Get movement speed modifier based on current disguise"""
	if not is_disguised():
		return 1.0
	
	# Different disguises could have different speed modifiers
	match current_disguise:
		"box", "barrel":
			return 0.8  # Slightly slower
		"chair", "table":
			return 0.7  # Slower
		_:
			return 1.0


# Input handling
func handle_disguise_input():
	"""Handle disguise-related input"""
	if not character or not character.is_me():
		return
	
	# Change disguise
	if Input.is_action_just_pressed("change_disguise"):
		var new_disguise = get_random_disguise()
		apply_disguise(new_disguise)
	
	# Cast hex
	if Input.is_action_just_pressed("cast_hex"):
		cast_hex_ability()
	
	# Remove disguise (for testing)
	if Input.is_action_just_pressed("remove_disguise"):
		remove_disguise()


func _input(_event):
	"""Handle input events"""
	if Constants.game_mode != Constants.GameMode.Prophaunt:
		return
	
	if not prophaunt_player or prophaunt_player.team != Constants.ProphauntTeam.PROPS:
		return
	
	handle_disguise_input()
