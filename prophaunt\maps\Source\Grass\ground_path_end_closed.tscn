[gd_scene load_steps=5 format=4 uid="uid://c1a5kyjb0hprp"]

[ext_resource type="Material" uid="uid://dkmcjc7wc3ws6" path="res://prophaunt/Mat/GrassPath.tres" id="1_63kil"]

[sub_resource type="ArrayMesh" id="ArrayMesh_ed75k"]
_surfaces = [{
"aabb": AABB(-1, -0.1, -1, 2, 0.1, 2),
"format": 34896613377,
"index_count": 204,
"index_data": PackedByteArray("AgAAAAEAAQADAAIAAAAEAAEAAQAFAAMABgAEAAAABwAGAAAAAAAIAAcACAAJAAcACAAKAAkACAALAAoACAAMAAsACAANAAwACAAOAA0ACAAPAA4AEgAQABEAEQATABIAEQAUABUAFQATABEAGAAWABcAFwAZABgAGgAYABkAGQAbABoAFAAcAB0AHQAVABQAHAAeAB8AHwAdABwAFgAgACEAIQAXABYAIQAgACIAIgAjACEAHgAkACUAJQAfAB4AIwAiACYAJgAnACMAJwAmACUAJQAkACcAFwAhACMAIwAZABcAIwAnABkAJwAbABkAJwAQABsAJwAkABAAJAAUABAAJAAeABQAHgAcABQAFAARABAAAwAgABYAFgAaAAMAFgAYABoAIAADAAUABgAgAAUABgAFAAEAAQAEAAYABgAiACAABgAHACIABwAmACIABwAJACYACQAlACYACQAfACUACQAKAB8ACgAdAB8ACgAVAB0ACgATABUACgALABMAEwALAAwAEwAMAA4ADgASABMADAANAA4A"),
"lods": [0.0319277, PackedByteArray("AAADAAIABgADAAAABgAgAAMAIAAaAAMABgAAACIABgAiACAAGgAgABcAFwAgACIAFwAZABoAGQAbABoAIgAjABcAAAAmACIAIwAiACYAJgAnACMAAAAIACYAJwAmACUACAAlACYAJQAkACcAHgAkACUACAAfACUAJQAfAB4ACAAPAA4ACAAOAAoACAAKAB8AEgAKAA4AHwAdAB4ACgAdAB8ACgASAB0AFAAeAB0AEQAUAB0AHQASABEAEgAQABEAIwAZABcAIwAnABkAJwAbABkAJwAQABsAJwAkABAAJAAUABAAFAARABAAJAAeABQA"), 0.0868438, PackedByteArray("AAADAAIABgADAAAABgAaAAMAAAAnAAYAGgAGACMAIwAGACcAIwAbABoAAAAIACcACAAeACcACAAKAB4ACgAUAB4ACAAOAAoACAAPAA4AEgAKAA4ACgASABQAEgAQABQAJwAbACMAJwAQABsAJwAUABAAJwAeABQA"), 0.235938, PackedByteArray("AAADAAIAAAAaAAMAGgAAACcAJwAbABoAAAAIACcACAAOACcACAAPAA4AEgAnAA4AEgAQACcAJwAQABsA")],
"name": "Material",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 40,
"vertex_data": PackedByteArray("/////wAAAAD/X/7/Gg8AAAAA/v8AAAAAAAD+//keAAD/f/7/Gg8AAP8f/v/5HgAAwrL+/0kaAABx3/7/FVcAAP///////wAAcd/+/yuJAADCsv7/9sUAAP9//v8m0QAA/1/+/ybRAAD/H/7/BeEAAAAA/v8F4QAAAAD+////AAAAAAAAjLcAAO8aAACMtwAAAAD+/2vHAADfHP7/a8cAAO5aAACtpwAA31z+/4y3AAAgY/7/tCgAABBlAACTOAAAICP+/5M4AAAQJQAAckgAAAAA/v+TOAAAAAAAAHJIAAB7ewAAracAADZ9/v+MtwAAl54AAPCfAABPpv7/fq4AADZ9/v+0KAAAe3sAAJM4AABPpv7/wjEAAJeeAABPQAAAe7cAABN+AADHxv7/UoIAAMfG/v/uXQAAe7cAAC1iAAA=")
}]
blend_shape_mode = 0

[sub_resource type="ArrayMesh" id="ArrayMesh_uogno"]
resource_name = "GroundPathEndClosed_GroundPathEndClosed"
_surfaces = [{
"aabb": AABB(-1, -0.1, -1, 2, 0.1, 2),
"attribute_data": PackedByteArray("k/YJh7Cu14orhFCCl4MPkAW9b4u+kbGQrdNukZ3n/q2T9h37rOc1xUvT9+GAvLbnKa4n6I6RLfA0g5vwnYOE/lCCNdzVRwD+T46Z24FK/OxPjpnbxoJL5caCS+W7j7bku4+25LuPtuS3quTT1Eb6wreq5NMWrMjcFqzI3BasyNxJr1WWSa9VlkmvVZbwr22fpQGOsfCvbZ+Tkvmbk5L5m5OS+Zsyk/ykUwWP2zKT/KQagz2bGoM9m7mCTaSlAfryDbk80w9Kaq4NuTzTdro63Ha6Otx2ujrcp0h3l0/I/s5PyP7OMs1B1zLNQdcyzUHXBbvflgW735YFu9+Wp7m+n98DZKOnub6fjc30m43N9JuNzfSbPgz7jarIEKSqyBCkuzVihAPToL8D06C/QNwswkDcLMJA3CzCKtzhsCrc4bAq3OGwGySdgSnTWLMp01izUII13MaCS+Uagz2buYJNpCnTWLPGgkvlGoM9m0/I/s6qyBCkKdNYs8aCS+UFu9+Wjc30m0DcLMIq3OGw"),
"format": ***********,
"index_count": 204,
"index_data": PackedByteArray("AgAAAAEAAQADAAIAAAAEAAEAAQAFAAMABgAEAAAABwAGAAAAAAAIAAcACAAJAAcACAAKAAkACAALAAoACAAMAAsACAANAAwACAAOAA0ACAAPAA4AFQAQABIAEgAXABUAFAAcAB8AHwAZABQAKAAiACUAJQArACgALAAmACkAKQAuACwAGgAwADMAMwAdABoAMgA3ADoAOgA1ADIAIAA8AD8APwAjACAAQQA+AEMAQwBGAEEAOABJAEwATAA7ADgARwBEAE8ATwBSAEcAUwBQAE0ATQBKAFMAJABAAEUARQAqACQARQBRACoAUQAvACoAUQARAC8AUQBIABEASAAbABEASAA2ABsANgAxABsAGwATABEAAwA9ACEAIQAtAAMAIQAnAC0APQADAAUABgA9AAUABgAFAAEAAQAEAAYABgBCAD0ABgAHAEIABwBOAEIABwAJAE4ACQBLAE4ACQA5AEsACQAKADkACgA0ADkACgAeADQACgAYAB4ACgALABgAGAALAAwAGAAMAA4ADgAWABgADAANAA4A"),
"lods": [0.0319277, PackedByteArray("AAADAAIABgADAAAABgBfAAMAXwAsAAMABgAAAGAABgBgAF8ALABfACMAIwBfAGAAIwApACwAKQAuACwAYABGACMAAABiAGAARgBgAGIAYgBSAEYAAAAIAGIAUgBiAGEACABhAGIAYQBJAFIANwBJAGEACAA5AGEAYQA5ADcACAAPAA4ACAAOAAoACAAKADkAXgAKAA4AOQAzADcACgAzADkACgBeADMAGgA3ADMAEgAaADMAMwBeABIAXgAQABIARQAqACQARQBRACoAUQAvACoAUQARAC8AUQBIABEASAAbABEAGwATABEASAA2ABsA"), 0.0868438, PackedByteArray("AAADAAIABgADAAAABgBaAAMAAABdAAYAWgAGAFwAXAAGAF0AXAAuAFoAAAAIAF0ACABbAF0ACAAKAFsACgAaAFsACAAOAAoACAAPAA4AWQAKAA4ACgBZABoAWQAQABoAUQAvAEUAUQARAC8AUQAbABEAUQA2ABsA"), 0.235938, PackedByteArray("AAADAAIAAABWAAMAVgAAAFgAWABXAFYAAAAIAFgACAAOAFgACAAPAA4AVQBYAA4AVQBUAFgAUQARAC8A")],
"material": ExtResource("1_63kil"),
"name": "Material",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 99,
"vertex_data": PackedByteArray("/////wAAA8D/X/7/Gg8DwAAA/v8AAAPAAAD+//keBMD/f/7/Gg8DwP8f/v/5HgTAwrL+/0kaA8Bx3/7/FVcBwP///////wHAcd/+/yuJBMDCsv7/9sUCwP9//v8m0QHA/1/+/ybRAcD/H/7/BeEBwAAA/v8F4QLAAAD+////AcAAAAAAjLep2wAAAACMt8TR7xoAAIy3qdvvGgAAjLfE0e8aAACMt/XaAAD+/2vHqdsAAP7/a8cEwN8c/v9rx6jb3xz+/2vHAsDfHP7/a8f12u5aAACtp6jb7loAAK2nxNHuWgAAraf12t9c/v+Mt6jb31z+/4y3AcDfXP7/jLf12iBj/v+0KGCkIGP+/7QoBcAgY/7/tChspRBlAACTOF2kEGUAAJM4xNEQZQAAkzhspSAj/v+TOGSkICP+/5M4BcAgI/7/kzhrpRAlAABySGOkECUAAHJIxNEQJQAAckhrpQAA/v+TOGKkAAD+/5M4BMAAAAAAckhhpAAAAABySMTRe3sAAK2nqNt7ewAArafE0Xt7AACtpwvbNn3+/4y3qNs2ff7/jLcCwDZ9/v+Mtwvbl54AAPCfxNGXngAA8J8L25eeAADwn5jTT6b+/36uAsBPpv7/fq4L20+m/v9+rrXTNn3+/7QoZKQ2ff7/tCgEwDZ9/v+0KLqle3sAAJM4YaR7ewAAkzjE0Xt7AACTOLqlT6b+/8IxA8BPpv7/wjG5pU+m/v/CMWGzl54AAE9AxNGXngAAT0C5pZeeAABPQIyze7cAABN+xNF7twAAE34r03u3AAATftHFx8b+/1KCAMDHxv7/UoJT08fG/v9SghzFx8b+/+5dAMDHxv7/7l36s8fG/v/uXSjEe7cAAC1ixNF7twAALWI/tHu3AAAtYqXEAAAAAIy3A8UAAP7/a8cXzgAA/v+TOLK1AAAAAHJI87h7twAALWIHwwAA/v9rx6bPAAD+/5M4NLKXngAA8J88yJeeAABPQDqwe7cAAC1iKsAAAP7/a8fwzDZ9/v+0KHW0T6b+/8IxQLTHxv7/UoK/xcfG/v/uXVa6a/3Xemf90Xph/cN6QP2Denb97npJ/ZR6ZP3Keh3+PHxZ/NKBa/rJgsL7HYIw/OaBOfzigU382IHG+xuCUfzWgVz7IoM4r2KorvvqgjivYqhd9w6Pk/v8gmr6yYLi+8eCyvsZglv3EI8v/JOCOK9iqGj3CY9i/HGCCPz6gWb3Co+k/bh4Hf08eszn7m8N/v15OK9iqM7n628q/T93F/0xetXn4W9H/Zl3OK9iqNfn3m9k/fN3Iv1FeoD9SXg4r2Ko1vvPgjmvYqiu9laOCPytgl37UIKt9laOOK9iqLD2VY5N6lmq9vsDgq/2VY7P6i6qMP1Td039nHpR952ajP1veDivYqhP95uac/3oekz3mZqk5Km3OK9iqEn3l5oo5bq3OK9iqE3oAase6Gy1wP0egQ/pwaoe5rW1yf6TfWjm4rcv4x+2OK9iqCrn+7e85Oe1zPx8goL7eoPh+7935P0IelD8ZZwA+PWGt/pudgj8O4zT9/KVFPUlpL36S4QZ/YV43/mbiEznupfG70+R")
}]
blend_shape_mode = 0
shadow_mesh = SubResource("ArrayMesh_ed75k")

[sub_resource type="ConcavePolygonShape3D" id="ConcavePolygonShape3D_20nw3"]
data = PackedVector3Array(-1, 0, -1, 1, 0, -1, -0.25, 0, -0.882, -0.25, 0, -0.882, -1, 0, -0.758, -1, 0, -1, 1, 0, -1, 0, 0, -0.882, -0.25, 0, -0.882, -0.25, 0, -0.882, -0.75, 0, -0.758, -1, 0, -0.758, 0.3966, 0, -0.7946, 0, 0, -0.882, 1, 0, -1, 0.7457, 0, -0.3197, 0.3966, 0, -0.7946, 1, 0, -1, 1, 0, -1, 1, 0, 1, 0.7457, 0, -0.3197, 1, 0, 1, 0.7457, 0, 0.0716, 0.7457, 0, -0.3197, 1, 0, 1, 0.3966, 0, 0.5466, 0.7457, 0, 0.0716, 1, 0, 1, 0, 0, 0.634, 0.3966, 0, 0.5466, 1, 0, 1, -0.25, 0, 0.634, 0, 0, 0.634, 1, 0, 1, -0.75, 0, 0.758, -0.25, 0, 0.634, 1, 0, 1, -1, 0, 0.758, -0.75, 0, 0.758, 1, 0, 1, -1, 0, 1, -1, 0, 0.758, -1, 0, 0.558, -1, -0.1, 0.434, -0.7896, -0.1, 0.434, -0.7896, -0.1, 0.434, -0.7744, 0, 0.558, -1, 0, 0.558, -0.7896, -0.1, 0.434, -0.2896, -0.1, 0.31, -0.2744, 0, 0.434, -0.2744, 0, 0.434, -0.7744, 0, 0.558, -0.7896, -0.1, 0.434, -0.7256, 0, -0.558, -0.2256, 0, -0.682, -0.2104, -0.1, -0.558, -0.2104, -0.1, -0.558, -0.7104, -0.1, -0.434, -0.7256, 0, -0.558, -1, 0, -0.558, -0.7256, 0, -0.558, -0.7104, -0.1, -0.434, -0.7104, -0.1, -0.434, -1, -0.1, -0.434, -1, 0, -0.558, -0.2896, -0.1, 0.31, -0.0353, -0.1, 0.31, -0.0218, 0, 0.434, -0.0218, 0, 0.434, -0.2744, 0, 0.434, -0.2896, -0.1, 0.31, -0.0353, -0.1, 0.31, 0.239, -0.1, 0.2495, 0.2993, 0, 0.3632, 0.2993, 0, 0.3632, -0.0218, 0, 0.434, -0.0353, -0.1, 0.31, -0.2256, 0, -0.682, -0.0218, 0, -0.682, -0.0353, -0.1, -0.558, -0.0353, -0.1, -0.558, -0.2104, -0.1, -0.558, -0.2256, 0, -0.682, -0.0353, -0.1, -0.558, -0.0218, 0, -0.682, 0.2993, 0, -0.6113, 0.2993, 0, -0.6113, 0.239, -0.1, -0.4976, -0.0353, -0.1, -0.558, 0.239, -0.1, 0.2495, 0.4335, -0.1, -0.015, 0.553, 0, 0.0181, 0.553, 0, 0.0181, 0.2993, 0, 0.3632, 0.239, -0.1, 0.2495, 0.239, -0.1, -0.4976, 0.2993, 0, -0.6113, 0.553, 0, -0.2662, 0.553, 0, -0.2662, 0.4335, -0.1, -0.233, 0.239, -0.1, -0.4976, 0.4335, -0.1, -0.233, 0.553, 0, -0.2662, 0.553, 0, 0.0181, 0.553, 0, 0.0181, 0.4335, -0.1, -0.015, 0.4335, -0.1, -0.233, -0.2104, -0.1, -0.558, -0.0353, -0.1, -0.558, 0.239, -0.1, -0.4976, 0.239, -0.1, -0.4976, -0.7104, -0.1, -0.434, -0.2104, -0.1, -0.558, 0.239, -0.1, -0.4976, 0.4335, -0.1, -0.233, -0.7104, -0.1, -0.434, 0.4335, -0.1, -0.233, -1, -0.1, -0.434, -0.7104, -0.1, -0.434, 0.4335, -0.1, -0.233, -1, -0.1, 0.434, -1, -0.1, -0.434, 0.4335, -0.1, -0.233, 0.4335, -0.1, -0.015, -1, -0.1, 0.434, 0.4335, -0.1, -0.015, -0.2896, -0.1, 0.31, -1, -0.1, 0.434, 0.4335, -0.1, -0.015, 0.239, -0.1, 0.2495, -0.2896, -0.1, 0.31, 0.239, -0.1, 0.2495, -0.0353, -0.1, 0.31, -0.2896, -0.1, 0.31, -0.2896, -0.1, 0.31, -0.7896, -0.1, 0.434, -1, -0.1, 0.434, -1, 0, -0.758, -0.0218, 0, -0.682, -0.2256, 0, -0.682, -0.2256, 0, -0.682, -1, 0, -0.558, -1, 0, -0.758, -0.2256, 0, -0.682, -0.7256, 0, -0.558, -1, 0, -0.558, -0.0218, 0, -0.682, -1, 0, -0.758, -0.75, 0, -0.758, 0.3966, 0, -0.7946, -0.0218, 0, -0.682, -0.75, 0, -0.758, 0.3966, 0, -0.7946, -0.75, 0, -0.758, -0.25, 0, -0.882, -0.25, 0, -0.882, 0, 0, -0.882, 0.3966, 0, -0.7946, 0.3966, 0, -0.7946, 0.2993, 0, -0.6113, -0.0218, 0, -0.682, 0.3966, 0, -0.7946, 0.7457, 0, -0.3197, 0.2993, 0, -0.6113, 0.7457, 0, -0.3197, 0.553, 0, -0.2662, 0.2993, 0, -0.6113, 0.7457, 0, -0.3197, 0.7457, 0, 0.0716, 0.553, 0, -0.2662, 0.7457, 0, 0.0716, 0.553, 0, 0.0181, 0.553, 0, -0.2662, 0.7457, 0, 0.0716, 0.2993, 0, 0.3632, 0.553, 0, 0.0181, 0.7457, 0, 0.0716, 0.3966, 0, 0.5466, 0.2993, 0, 0.3632, 0.3966, 0, 0.5466, -0.0218, 0, 0.434, 0.2993, 0, 0.3632, 0.3966, 0, 0.5466, -0.2744, 0, 0.434, -0.0218, 0, 0.434, 0.3966, 0, 0.5466, -0.7744, 0, 0.558, -0.2744, 0, 0.434, 0.3966, 0, 0.5466, 0, 0, 0.634, -0.7744, 0, 0.558, -0.7744, 0, 0.558, 0, 0, 0.634, -0.25, 0, 0.634, -0.7744, 0, 0.558, -0.25, 0, 0.634, -1, 0, 0.758, -1, 0, 0.758, -1, 0, 0.558, -0.7744, 0, 0.558, -0.25, 0, 0.634, -0.75, 0, 0.758, -1, 0, 0.758)

[node name="GroundPathEndClosed" type="MeshInstance3D"]
mesh = SubResource("ArrayMesh_uogno")
skeleton = NodePath("")

[node name="StaticBody3D" type="StaticBody3D" parent="."]

[node name="CollisionShape3D" type="CollisionShape3D" parent="StaticBody3D"]
shape = SubResource("ConcavePolygonShape3D_20nw3")
