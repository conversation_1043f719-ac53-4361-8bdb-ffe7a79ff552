extends Control
class_name ProphauntGameUI

# Prophaunt Game UI Controller
# Manages all UI elements during Prophaunt gameplay

# UI References
@onready var round_label = $TopPanel/TopContainer/RoundInfo/RoundLabel
@onready var timer_label = $TopPanel/TopContainer/RoundInfo/TimerLabel
@onready var team_label = $TopPanel/TopContainer/TeamInfo/TeamLabel
@onready var props_alive_label = $TopPanel/TopContainer/TeamInfo/PropsAliveLabel

@onready var health_panel = $HealthPanel
@onready var health_label = $HealthPanel/HealthContainer/HealthLabel
@onready var health_bar = $HealthPanel/HealthContainer/HealthBar

@onready var props_list = $PlayerListPanel/PlayerListContainer/PropsList
@onready var haunters_list = $PlayerListPanel/PlayerListContainer/HauntersList

@onready var notification_panel = $NotificationPanel
@onready var notification_label = $NotificationPanel/NotificationLabel

# Game state
var current_team: Constants.ProphauntTeam
var local_player_id: int = -1
var round_time_remaining: float = 0.0
var props_alive_count: int = 0
var total_props_count: int = 0

# UI state
var notification_timer: float = 0.0

signal chat_message_sent(message: String)

func _ready():
	# Initialize UI
	setup_ui()
	
	# Connect to multiplayer signals
	if multiplayer:
		local_player_id = multiplayer.get_unique_id()


func setup_ui():
	"""Initialize UI elements"""
	# Hide health panel for haunters initially
	health_panel.visible = false
	
	# Set initial values
	round_label.text = "ROUND 1"
	timer_label.text = "03:00"
	team_label.text = "WAITING..."
	props_alive_label.text = "0/0"
	
	# Clear player lists
	clear_player_lists()


func set_team(team: Constants.ProphauntTeam):
	"""Set the local player's team and update UI accordingly"""
	current_team = team
	
	if team == Constants.ProphauntTeam.PROPS:
		setup_prop_ui()
	else:
		setup_haunter_ui()


func setup_prop_ui():
	"""Set up UI for prop players"""
	team_label.text = "PROPS"
	team_label.modulate = Color.BLUE
	
	# Show health panel
	health_panel.visible = true
	health_label.text = "HEALTH"
	
	#print("UI set up for Props team")


func setup_haunter_ui():
	"""Set up UI for haunter players"""
	team_label.text = "HAUNTERS"
	team_label.modulate = Color.RED
	
	# Hide health panel
	health_panel.visible = false
	
	#print("UI set up for Haunters team")


func update_round_info(round_number: int, time_remaining: float):
	"""Update round information"""
	round_label.text = "ROUND " + str(round_number)
	round_time_remaining = time_remaining
	
	# Format time as MM:SS
	@warning_ignore("integer_division")
	var minutes = int(time_remaining) / 60
	var seconds = int(time_remaining) % 60
	timer_label.text = "%02d:%02d" % [minutes, seconds]
	
	# Change color based on time remaining
	if time_remaining <= 10:
		timer_label.modulate = Color.RED
	elif time_remaining <= 30:
		timer_label.modulate = Color.YELLOW
	else:
		timer_label.modulate = Color.WHITE


func update_props_info(alive_count: int, total_count: int):
	"""Update props alive information"""
	props_alive_count = alive_count
	total_props_count = total_count
	props_alive_label.text = str(alive_count) + "/" + str(total_count) + " ALIVE"
	
	# Change color based on survival rate
	var survival_rate = float(alive_count) / float(total_count) if total_count > 0 else 0.0
	if survival_rate <= 0.25:
		props_alive_label.modulate = Color.RED
	elif survival_rate <= 0.5:
		props_alive_label.modulate = Color.YELLOW
	else:
		props_alive_label.modulate = Color.GREEN


func update_health(current_hp: int, max_hp: int):
	"""Update health display"""
	if not health_panel.visible:
		return
	
	health_bar.max_value = max_hp
	health_bar.value = current_hp
	health_label.text = "HEALTH: " + str(current_hp) + "/" + str(max_hp)
	
	# Change health bar color
	var health_percent = float(current_hp) / float(max_hp)
	if health_percent > 0.6:
		health_bar.modulate = Color.GREEN
	elif health_percent > 0.3:
		health_bar.modulate = Color.YELLOW
	else:
		health_bar.modulate = Color.RED


#func update_ability_cooldown(_ability_index: int, cooldown_remaining: float):
	#"""Update ability cooldown display"""
	#var cooldown_label: Label
	#var button: Button
	
	#match ability_index:
		#0:
			#cooldown_label = ability1_cooldown
			#button = ability1_button
		#1:
			#cooldown_label = ability2_cooldown
			#button = ability2_button
		#2:
			#cooldown_label = ability3_cooldown
			#button = ability3_button
		#_:
			#return
	
	#if cooldown_remaining > 0:
		#cooldown_label.text = "%.1f" % cooldown_remaining
		#button.disabled = true
		#button.modulate = Color.GRAY
	#else:
		#cooldown_label.text = ""
		#button.disabled = false
		#button.modulate = Color.WHITE


func add_player_to_list(player_id: int, player_name: String, team: Constants.ProphauntTeam, is_alive: bool = true):
	"""Add a player to the appropriate team list"""
	var player_label = Label.new()
	player_label.name = "Player_" + str(player_id)
	player_label.text = player_name
	player_label.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	
	# Set color based on status
	if is_alive:
		player_label.modulate = Color.WHITE
	else:
		player_label.modulate = Color.GRAY
		player_label.text += " (DEAD)"
	
	if team == Constants.ProphauntTeam.PROPS:
		props_list.add_child(player_label)
	else:
		haunters_list.add_child(player_label)


func update_player_status(player_id: int, is_alive: bool):
	"""Update a player's status in the list"""
	var player_label = find_player_label(player_id)
	if player_label:
		if is_alive:
			player_label.modulate = Color.WHITE
			player_label.text = player_label.text.replace(" (DEAD)", "")
		else:
			player_label.modulate = Color.GRAY
			if not player_label.text.ends_with(" (DEAD)"):
				player_label.text += " (DEAD)"


func find_player_label(player_id: int) -> Label:
	"""Find a player's label in the lists"""
	var label_name = "Player_" + str(player_id)
	
	# Check props list
	for child in props_list.get_children():
		if child.name == label_name:
			return child
	
	# Check haunters list
	for child in haunters_list.get_children():
		if child.name == label_name:
			return child
	
	return null


func clear_player_lists():
	"""Clear all player lists"""
	for child in props_list.get_children():
		child.queue_free()
	for child in haunters_list.get_children():
		child.queue_free()


func add_chat_message(_sender_name: String, _message: String, _is_team_message: bool = true):
	"""Add a message to the chat"""


func show_notification(text: String, duration: float = 3.0):
	"""Show a notification message"""
	notification_label.text = text
	notification_panel.visible = true
	notification_timer = duration


func hide_notification():
	"""Hide the notification panel"""
	notification_panel.visible = false
	notification_timer = 0.0


func _process(delta):
	"""Update UI elements"""
	if Constants.client.my_player_scene:
		%Pos.text = str(Constants.client.my_player_scene.global_position)
	# Update notification timer
	if notification_timer > 0:
		notification_timer -= delta
		if notification_timer <= 0:
			hide_notification()


func _on_chat_submitted(_text: String):
	"""Handle chat message submission"""


# Input handling
func _input(_event):
	"""Handle input events"""


# Utility functions
func get_formatted_time(seconds: float) -> String:
	"""Format time as MM:SS"""
	@warning_ignore("integer_division")
	var minutes = int(seconds) / 60
	var secs = int(seconds) % 60
	return "%02d:%02d" % [minutes, secs]


func flash_element(element: Control, color: Color = Color.RED, duration: float = 0.5):
	"""Flash a UI element"""
	var original_modulate = element.modulate
	element.modulate = color
	
	var tween = create_tween()
	tween.tween_property(element, "modulate", original_modulate, duration)


# Network event handlers (to be called by the game controller)
func on_round_started(round_number: int, duration: float):
	"""Called when a round starts"""
	update_round_info(round_number, duration)
	show_notification("ROUND " + str(round_number) + " STARTED!", 2.0)


func on_round_ended(winner_team: Constants.ProphauntTeam):
	"""Called when a round ends"""
	var winner_text = "PROPS WIN!" if winner_team == Constants.ProphauntTeam.PROPS else "HAUNTERS WIN!"
	show_notification(winner_text, 5.0)


func on_player_eliminated(player_id: int):
	"""Called when a player is eliminated"""
	update_player_status(player_id, false)
	show_notification("PLAYER ELIMINATED!", 2.0)


func on_time_warning(warning_type: String):
	"""Called for time warnings"""
	match warning_type:
		"30_seconds_remaining":
			show_notification("30 SECONDS REMAINING!", 2.0)
			flash_element(timer_label, Color.YELLOW)
		"10_seconds_remaining":
			show_notification("10 SECONDS REMAINING!", 2.0)
			flash_element(timer_label, Color.RED)
		"5_seconds_remaining":
			show_notification("5 SECONDS REMAINING!", 2.0)
			flash_element(timer_label, Color.RED)


func _on_controller_gui_input(_event: InputEvent) -> void:
	pass # Replace with function body.


func _on_full_screen_gui_input(_event: InputEvent) -> void:
	pass # Replace with function body.
