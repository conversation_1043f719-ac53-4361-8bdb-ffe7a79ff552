[gd_scene load_steps=4 format=4 uid="uid://cv8fbxq2cs11c"]

[ext_resource type="Material" uid="uid://b6gd8jjb1kopc" path="res://prophaunt/Mat/Colormap2.tres" id="1_emxp5"]

[sub_resource type="ArrayMesh" id="ArrayMesh_shlkc"]
_surfaces = [{
"aabb": AABB(-0.2125, -1.60433e-08, -0.2125, 0.425, 0.562, 0.425),
"format": 34359742465,
"index_count": 90,
"index_data": PackedByteArray("AQACAAAAAgABAAMABgAFAAcACQAGAAcABgAJAAgABgAKAAsABgALAAwADAANAAAADQAMAA4ACgAGAAgADAAAAAYADgALAAoADQABAAAAAQANAA8AEAABAA8ACQASABMAAQAQABEADgATABAACgAJABMACQAKAAgADQAQAA8AEAATABIAEgARABAAEwAOAAoABQAGAAQACwAOAAwAEgAHAAEAEgAJAAcAAQARABIAEAANAA4A"),
"name": "Material",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 20,
"vertex_data": PackedByteArray("mJlZPtJN4j6cmVm+mJlZPtRN4j6YmVk+AAAAszvfDz+dmVm+AAAAszvfDz+XmVk+AAAAsxKDAD+dmVm+AAAAsxKDAD+XmVk+nJlZvtJN4j6cmVm+nJlZvtRN4j6YmVk+nJlZvprPibKamVm+nJlZvprPiTKamVk+nEMLvZrPibKamVm+nEMLvUS28z2bmVm+kEMLPUS28z2bmVm+mJlZPprPibKamVm+kEMLPZrPibKamVm+mJlZPprPiTKamVk+kEMLPZrPiTKamVk+kEMLPUi28z2ZmVk+nEMLvUi28z2ZmVk+nEMLvZrPiTKamVk+")
}]
blend_shape_mode = 0

[sub_resource type="ArrayMesh" id="ArrayMesh_c3362"]
resource_name = "BoxClosed_BoxClosed"
_surfaces = [{
"aabb": AABB(-0.2125, -1.60433e-08, -0.2125, 0.425, 0.562, 0.425),
"attribute_data": PackedByteArray("jI5BP1qvrD6MjkE/Wq+sPoyOQT9ar6w+jI5BP1qvrD6MjkE/Wq+sPoyOQT9ar6w+jI5BP1qvrD6MjkE/Wq+sPoyOQT9ar6w+jI5BP1qvrD6MjkE/Wq+sPoyOQT9ar6w+jI5BP1qvrD6MjkE/Wq+sPoyOQT9ar6w+jI5BP1qvrD6MjkE/Wq+sPoyOQT9ar6w+jI5BP1qvrD6MjkE/Wq+sPoyOQT9ar6w+jI5BP1qvrD4poSs/6DONPYyOQT9ar6w+KaErP+gzjT2MjkE/Wq+sPimhKz/oM409jI5BP1qvrD4poSs/6DONPYyOQT9ar6w+jI5BP1qvrD6MjkE/Wq+sPoyOQT9ar6w+KaErP+gzjT2MjkE/Wq+sPimhKz/oM409jI5BP1qvrD6MjkE/Wq+sPoyOQT9ar6w+jI5BP1qvrD4poSs/6DONPYyOQT9ar6w+KaErP+gzjT2MjkE/Wq+sPimhKz/oM409jI5BP1qvrD4poSs/6DONPYyOQT9ar6w+KaErP+gzjT2MjkE/Wq+sPimhKz/oM409jI5BP1qvrD4="),
"format": 34359742487,
"index_count": 90,
"index_data": PackedByteArray("BAAGAAEABgAEAAcACwAJAA4AFQAMAA8ADAAVABIACgAXABsACgAbAB0AHQAeAAAAHgAdACIAFwAKABAAHQAAAAoAIQAaABYAIAAFAAIABQAgACcAKQADACUAEwAvADEAAwApAC0AIwAyACoAGQAUADMAFAAZABEAHwArACYAKAAwAC4ALgAsACgAMgAjABgACQALAAgAGgAhABwALwANAAMALwATAA0AAwAtAC8AKwAfACQA"),
"material": ExtResource("1_emxp5"),
"name": "Material",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 52,
"vertex_data": PackedByteArray("mJlZPtJN4j6cmVm+mJlZPtJN4j6cmVm+mJlZPtJN4j6cmVm+mJlZPtRN4j6YmVk+mJlZPtRN4j6YmVk+mJlZPtRN4j6YmVk+AAAAszvfDz+dmVm+AAAAszvfDz+XmVk+AAAAsxKDAD+dmVm+AAAAsxKDAD+XmVk+nJlZvtJN4j6cmVm+nJlZvtJN4j6cmVm+nJlZvtJN4j6cmVm+nJlZvtRN4j6YmVk+nJlZvtRN4j6YmVk+nJlZvtRN4j6YmVk+nJlZvprPibKamVm+nJlZvprPibKamVm+nJlZvprPibKamVm+nJlZvprPiTKamVk+nJlZvprPiTKamVk+nJlZvprPiTKamVk+nEMLvZrPibKamVm+nEMLvZrPibKamVm+nEMLvZrPibKamVm+nEMLvZrPibKamVm+nEMLvUS28z2bmVm+nEMLvUS28z2bmVm+kEMLPUS28z2bmVm+kEMLPUS28z2bmVm+mJlZPprPibKamVm+mJlZPprPibKamVm+mJlZPprPibKamVm+kEMLPZrPibKamVm+kEMLPZrPibKamVm+kEMLPZrPibKamVm+kEMLPZrPibKamVm+mJlZPprPiTKamVk+mJlZPprPiTKamVk+mJlZPprPiTKamVk+kEMLPZrPiTKamVk+kEMLPZrPiTKamVk+kEMLPZrPiTKamVk+kEMLPZrPiTKamVk+kEMLPUi28z2ZmVk+kEMLPUi28z2ZmVk+nEMLvUi28z2ZmVk+nEMLvUi28z2ZmVk+nEMLvZrPiTKamVk+nEMLvZrPiTKamVk+nEMLvZrPiTKamVk+nEMLvZrPiTKamVk+/////////78wrs3R////v////3////+//3//f////z8wrs3R////v////3////+/MK7N0f///78wrs3R////v9Fj0OP///+/0WPQ4////7//////////v9Fj0OP///+/AAD/f////7//f/9/////P9Fj0OP///+/AAD/f////7//////////v/9/AAD///+/AAD/f////7//f/9/////P/9/AAD///+/AAD/f////7//////////v/////////+//38AAP///7//fwAA////v/////////+//////////7//////////v/////////+//////////7//fwAA////v////3////+//////////7//////////v/9/AAD///+//38AAP///7//f/9/////P/9/AAD///+/////f////7//f/9/////P/9//3////8//38AAP///7//fwAA////v/9//3////8//3//f////z//f/9/////P/9//3////8//3//f////z//f/9/////P/9/AAD///+//38AAP///78=")
}]
blend_shape_mode = 0
shadow_mesh = SubResource("ArrayMesh_shlkc")

[node name="BoxClosed" type="MeshInstance3D"]
mesh = SubResource("ArrayMesh_c3362")
skeleton = NodePath("")
