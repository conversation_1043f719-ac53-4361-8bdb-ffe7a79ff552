[gd_scene load_steps=4 format=3 uid="uid://da2uohs82apd8"]

[ext_resource type="PackedScene" uid="uid://cy82bsxfj1ddv" path="res://prophaunt/scenes/PropObject.tscn" id="1_s1qah"]
[ext_resource type="PackedScene" uid="uid://cwxp4jj3wnpd3" path="res://prophaunt/maps/Source/Furniture/bookcase_door.tscn" id="2_ger1t"]

[sub_resource type="BoxShape3D" id="BoxShape3D_n2ccm"]
size = Vector3(1.23079, 2.56427, 0.759491)

[node name="BookcaseDoorProp" instance=ExtResource("1_s1qah")]

[node name="BookcaseDoor" parent="Meshes" index="0" instance=ExtResource("2_ger1t")]
transform = Transform3D(3, 0, 0, 0, 3, 0, 0, 0, 3, 0, 0, 0)

[node name="CollisionShape3D" type="CollisionShape3D" parent="StaticBody3D" index="0"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0.00249478, 1.285, 0.00761414)
shape = SubResource("BoxShape3D_n2ccm")
