[gd_scene load_steps=4 format=3 uid="uid://b0e6f3yqci5rt"]

[ext_resource type="PackedScene" uid="uid://cy82bsxfj1ddv" path="res://prophaunt/scenes/PropObject.tscn" id="1_kg2hh"]
[ext_resource type="PackedScene" uid="uid://ckoe83bw5lkem" path="res://prophaunt/maps/Source/Furniture/lounge_chair_relax.tscn" id="2_rmcxt"]

[sub_resource type="BoxShape3D" id="BoxShape3D_f8ox4"]
size = Vector3(2.0021, 2.50345, 1.87686)

[node name="LoungeChairRelaxProp" instance=ExtResource("1_kg2hh")]

[node name="LoungeChairRelax" parent="Meshes" index="0" instance=ExtResource("2_rmcxt")]

[node name="CollisionShape3D" type="CollisionShape3D" parent="StaticBody3D" index="0"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -0.0107177, 1.25172, 0.0480443)
shape = SubResource("BoxShape3D_f8ox4")
