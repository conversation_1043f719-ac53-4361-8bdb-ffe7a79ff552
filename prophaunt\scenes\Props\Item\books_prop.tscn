[gd_scene load_steps=4 format=3 uid="uid://dpyqqu8as07hk"]

[ext_resource type="Script" path="res://prophaunt/scenes/PropObject.gd" id="1_b3258"]
[ext_resource type="PackedScene" uid="uid://ckcvjx2x3sfb4" path="res://prophaunt/maps/Source/Item/books.tscn" id="2_x1u3x"]

[sub_resource type="BoxShape3D" id="BoxShape3D_qqs5e"]
size = Vector3(0.693553, 0.453449, 0.362457)

[node name="BooksProp" type="Node3D" node_paths=PackedStringArray("export_mesh") groups=["PropObject"]]
script = ExtResource("1_b3258")
export_mesh = NodePath("Meshes")

[node name="Meshes" type="Node3D" parent="."]

[node name="Books" parent="Meshes" instance=ExtResource("2_x1u3x")]

[node name="StaticBody3D" type="StaticBody3D" parent="."]
collision_layer = 2

[node name="CollisionShape3D" type="CollisionShape3D" parent="StaticBody3D"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -0.0354916, 0.2241, -0.0774078)
shape = SubResource("BoxShape3D_qqs5e")
