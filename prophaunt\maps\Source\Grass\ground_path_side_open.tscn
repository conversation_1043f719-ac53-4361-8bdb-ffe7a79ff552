[gd_scene load_steps=5 format=4 uid="uid://7sojky52lwrb"]

[ext_resource type="Material" uid="uid://dkmcjc7wc3ws6" path="res://prophaunt/Mat/GrassPath.tres" id="1_t60bp"]

[sub_resource type="ArrayMesh" id="ArrayMesh_0bmqy"]
_surfaces = [{
"aabb": AABB(-1, -0.1, -1, 2, 0.1, 2),
"format": 34896613377,
"index_count": 120,
"index_data": PackedByteArray("AgAAAAEAAQADAAIAAQAEAAMABwAFAAYABgAIAAcACAAJAAcADAAKAAsACwANAAwAEAAOAA8ADwARABAAFAASABMAEwAVABQAGAAWABcAFwAZABgAGQAXABMAEwASABkADQALABEAEQAPAA0AFgAQABEAFwAWABEAGgAXABEAGgARAAsAGgALAAoAGwAaAAoACgAcABsAGgATABcAGgAdABMAFQATAB0ABQAYABkAGQASAAUAEgAGAAUAEgAUAAYAFAAIAAYAFAAJAAgADgADAAQABAANAA4ADQAPAA4ADQAEAAEADQABAAAAAAAMAA0A"),
"lods": [0.117582, PackedByteArray("AAADAAIADgADAAAAAAAMAA4ADAAQAA4AEQAQAAwADAAKABEABQAJAAcAFAAJAAUAEwAUAAUAEwAVABQAGAATAAUAGAAWABMAFgAQABEAEwAWABEAGgATABEAGgARAAoAGgAdABMAFQATAB0AGwAaAAoACgAcABsA")],
"name": "Material",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 30,
"vertex_data": PackedByteArray("AAD+/wXhAAB8D/7/BeEAAAAA/v/+/wAA+R7+//7/AAD5Hv7/gvAAAAXh/v///wAABeH+/4LwAAD+//7///8AAILw/v8F4QAA/v/+/wXhAAAAAAAAjLcAAP8fAACMtwAAAAD//2vHAAD/H///a8cAAJM4/v/+/wAAkzj+///fAABySAAA/v8AAHJIAAD/3wAA/9///2vHAAD/3wAAjLcAAP7///9rxwAA/v8AAIy3AACMtwAA/v8AAIy3AAD/3wAAa8f+//7/AABrx/7//98AAP//AAAAAAAAAAAAAAAAAAAAAAAAckgAAP7/AABySAAA")
}]
blend_shape_mode = 0

[sub_resource type="ArrayMesh" id="ArrayMesh_ojggc"]
resource_name = "GroundPathSideOpen_GroundPathSideOpen"
_surfaces = [{
"aabb": AABB(-1, -0.1, -1, 2, 0.1, 2),
"attribute_data": PackedByteArray("KrNQgtOyk5pQglCCpoJRs/ua9bIk/aeztuRTs1b9UIIFzPSa8ctQgiv7rIW+JP2BviT9gb73Lbe+JH2Rvvctt6DbfIOg23yDxNics8TYnLPE2JyztYPo27WD6NvqtSnZ6rUp2eq1KdmmARWluIaE/iYRFaVRvHv4Ubx7+C6mD7Qupg+0LqYPtGaGqLi+JH/uZoaouB2jkoIdo5KCUIIthb4k//2mAefa/vaE/iYR59r3w9b498PW+Jz6AN6c+gDeFsl82RbJfNkWyXzZqH3//ah9/YGoff2BkFqegZBa//2g23yDUbx7+GaGqLgdo5KCnPoA3g=="),
"format": 34896613399,
"index_count": 120,
"index_data": PackedByteArray("AgAAAAEAAQADAAIAAQAEAAMABwAFAAYABgAIAAcACAAJAAcAEAAKAA0ADQASABAAGwAWABkAGQAeABsAJQAfACIAIgAnACUALwAqAC0ALQAyAC8AMQAsACQAJAAhADEAFAAPAB0AHQAYABQAKQAaABwAKwApABwAMwArABwAMwAcAA4AMwAOAAsANAAzAAsADAA2ADUAMwAjACsAMwA3ACMAKAAjADcABQAuADAAMAAgAAUAIAAGAAUAIAAmAAYAJgAIAAYAJgAJAAgAFQADAAQABAATABUAEwAXABUAEwAEAAEAEwABAAAAAAARABMA"),
"lods": [0.117582, PackedByteArray("AAADAAIAFQADAAAAAAA4ABUAOAAbABUAOQAbADgAOAAKADkABQAJAAcAOwAJAAUAOgA7AAUAOgAnADsAPAA6AAUAPAAqADoAKQAaABwAIwApABwAMwAjABwAMwAcAAsAMwA3ACMAKAAjADcANAAzAAsACwA2ADQA")],
"material": ExtResource("1_t60bp"),
"name": "Material",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 61,
"vertex_data": PackedByteArray("AAD+/wXhbtV8D/7/BeEI1QAA/v/+/z/V+R7+//7/ENX5Hv7/gvD71AXh/v///4rUBeH+/4LwGdX+//7///9F1YLw/v8F4V3V/v/+/wXhZ9UAAAAAjLen5wAAAACMt1TVAAAAAIy3VNX/HwAAjLcO6P8fAACMt1TV/x8AAIy33dgAAP//a8fh5wAA//9rxwzW/x///2vHSej/H///a8dr1f8f//9rx/vYkzj+//7/ttSTOP7//v/TxpM4/v//3/LTkzj+///foNmTOP7//98jxnJIAAD+/1TVckgAAP7/b8ZySAAA/99U1XJIAAD/31fZckgAAP/fwsX/3///a8ev6P/f//9rxwTV/9///2vHH9n/3wAAjLdb6P/fAACMt1TV/98AAIy3ONn+////a8cb6P7///9rxzLV/v8AAIy3yef+/wAAjLdU1Yy3AAD+/1TVjLcAAP7/7MWMtwAA/99U1Yy3AAD/32bZjLcAAP/fGsZrx/7//v9b02vH/v/+/wbGa8f+///fidRrx/7//99a2WvH/v//3zXG//8AAAAAVNUAAAAAAABU1QAAAAAAAFTVAAAAAHJIqzP+/wAAckhU1QAA//9rx8vYckgAAP/fUdj/3wAAjLeo2v7///9rxzDda8f+//7/881F1YsqgtUHK2HVwyp91fwqitUWK6GrLqrxqoaqu6qhqp+qr6qTqrWq6cqIMVTVqirG2MdYTcqgMFTVqip0u+EtkcoFMebUzin1yR4wRtWPKia7Ny211Wsr9b4kIS7WXyyfud0pUL/ZIlTVqioovxoiVNWqKka6TCuEv9IjPq+gtgureapwrDLF9K8ltlTVqirsq27FgbDFtdOqlao5sUm1VNWqKlTVqipko5DAVNWqKgCr2sXuoqnAH61vqSGjnsCiqy2qPKu+xauit8BU1aoqVNWqKsnYyVhryDUkVNWqKpfNxDfUwJAuTLWItva2grGBqDay")
}]
blend_shape_mode = 0
shadow_mesh = SubResource("ArrayMesh_0bmqy")

[sub_resource type="ConcavePolygonShape3D" id="ConcavePolygonShape3D_yjgf0"]
data = PackedVector3Array(-1, 0, 1, -1, 0, 0.758, -0.879, 0, 0.758, -0.879, 0, 0.758, -0.758, 0, 1, -1, 0, 1, -0.879, 0, 0.758, -0.758, 0, 0.879, -0.758, 0, 1, 1, 0, 1, 0.758, 0, 1, 0.758, 0, 0.879, 0.758, 0, 0.879, 0.879, 0, 0.758, 1, 0, 1, 0.879, 0, 0.758, 1, 0, 0.758, 1, 0, 1, -1, 0, 0.558, -1, -0.1, 0.434, -0.75, -0.1, 0.434, -0.75, -0.1, 0.434, -0.75, 0, 0.558, -1, 0, 0.558, -0.434, -0.1, 1, -0.558, 0, 1, -0.558, 0, 0.75, -0.558, 0, 0.75, -0.434, -0.1, 0.75, -0.434, -0.1, 1, 1, 0, 0.558, 0.75, 0, 0.558, 0.75, -0.1, 0.434, 0.75, -0.1, 0.434, 1, -0.1, 0.434, 1, 0, 0.558, 0.558, 0, 1, 0.434, -0.1, 1, 0.434, -0.1, 0.75, 0.434, -0.1, 0.75, 0.558, 0, 0.75, 0.558, 0, 1, 0.558, 0, 0.75, 0.434, -0.1, 0.75, 0.75, -0.1, 0.434, 0.75, -0.1, 0.434, 0.75, 0, 0.558, 0.558, 0, 0.75, -0.75, 0, 0.558, -0.75, -0.1, 0.434, -0.434, -0.1, 0.75, -0.434, -0.1, 0.75, -0.558, 0, 0.75, -0.75, 0, 0.558, 0.434, -0.1, 1, -0.434, -0.1, 1, -0.434, -0.1, 0.75, 0.434, -0.1, 0.75, 0.434, -0.1, 1, -0.434, -0.1, 0.75, 1, -0.1, -1, 0.434, -0.1, 0.75, -0.434, -0.1, 0.75, 1, -0.1, -1, -0.434, -0.1, 0.75, -0.75, -0.1, 0.434, 1, -0.1, -1, -0.75, -0.1, 0.434, -1, -0.1, 0.434, -1, -0.1, -1, 1, -0.1, -1, -1, -0.1, 0.434, -1, -0.1, 0.434, -1, -0.1, -0.434, -1, -0.1, -1, 1, -0.1, -1, 0.75, -0.1, 0.434, 0.434, -0.1, 0.75, 1, -0.1, -1, 1, -0.1, -0.434, 0.75, -0.1, 0.434, 1, -0.1, 0.434, 0.75, -0.1, 0.434, 1, -0.1, -0.434, 0.758, 0, 1, 0.558, 0, 1, 0.558, 0, 0.75, 0.558, 0, 0.75, 0.75, 0, 0.558, 0.758, 0, 1, 0.75, 0, 0.558, 0.758, 0, 0.879, 0.758, 0, 1, 0.75, 0, 0.558, 1, 0, 0.558, 0.758, 0, 0.879, 1, 0, 0.558, 0.879, 0, 0.758, 0.758, 0, 0.879, 1, 0, 0.558, 1, 0, 0.758, 0.879, 0, 0.758, -0.558, 0, 1, -0.758, 0, 1, -0.758, 0, 0.879, -0.758, 0, 0.879, -0.75, 0, 0.558, -0.558, 0, 1, -0.75, 0, 0.558, -0.558, 0, 0.75, -0.558, 0, 1, -0.75, 0, 0.558, -0.758, 0, 0.879, -0.879, 0, 0.758, -0.75, 0, 0.558, -0.879, 0, 0.758, -1, 0, 0.758, -1, 0, 0.758, -1, 0, 0.558, -0.75, 0, 0.558)

[node name="GroundPathSideOpen" type="MeshInstance3D"]
mesh = SubResource("ArrayMesh_ojggc")
skeleton = NodePath("")

[node name="StaticBody3D" type="StaticBody3D" parent="."]

[node name="CollisionShape3D" type="CollisionShape3D" parent="StaticBody3D"]
shape = SubResource("ConcavePolygonShape3D_yjgf0")
