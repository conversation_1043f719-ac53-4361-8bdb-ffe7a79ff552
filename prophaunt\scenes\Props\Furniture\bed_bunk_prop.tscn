[gd_scene load_steps=4 format=3 uid="uid://74cv3v1ck04n"]

[ext_resource type="PackedScene" uid="uid://cy82bsxfj1ddv" path="res://prophaunt/scenes/PropObject.tscn" id="1_a8xpk"]
[ext_resource type="PackedScene" uid="uid://bae733fs21kx4" path="res://prophaunt/maps/Source/Furniture/bed_bunk.tscn" id="2_6l18m"]

[sub_resource type="BoxShape3D" id="BoxShape3D_n2ccm"]
size = Vector3(2.26045, 3.41693, 4.4108)

[node name="BedBunkProp" instance=ExtResource("1_a8xpk")]

[node name="BedBunk" parent="Meshes" index="0" instance=ExtResource("2_6l18m")]

[node name="CollisionShape3D" type="CollisionShape3D" parent="StaticBody3D" index="0"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0.0655441, 1.71133, 0.0063324)
shape = SubResource("BoxShape3D_n2ccm")
