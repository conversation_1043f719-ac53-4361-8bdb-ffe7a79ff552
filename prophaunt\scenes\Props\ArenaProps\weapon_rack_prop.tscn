[gd_scene load_steps=4 format=3 uid="uid://cy2v6fjmwvk4j"]

[ext_resource type="PackedScene" uid="uid://cy82bsxfj1ddv" path="res://prophaunt/scenes/PropObject.tscn" id="1_wapht"]
[ext_resource type="PackedScene" uid="uid://c885immana41d" path="res://prophaunt/maps/Source/ArenaProp/weapon_rack.tscn" id="2_mp72f"]

[sub_resource type="CapsuleShape3D" id="CapsuleShape3D_5q4rx"]
radius = 0.795174
height = 2.6677

[node name="WeaponRackProp" instance=ExtResource("1_wapht")]

[node name="WeaponRack" parent="Meshes" index="0" instance=ExtResource("2_mp72f")]

[node name="CollisionShape3D" type="CollisionShape3D" parent="StaticBody3D" index="0"]
transform = Transform3D(-4.37114e-08, -1, 0, 1, -4.37114e-08, 0, 0, 0, 1, 0, 0.807856, -0.010301)
shape = SubResource("CapsuleShape3D_5q4rx")
