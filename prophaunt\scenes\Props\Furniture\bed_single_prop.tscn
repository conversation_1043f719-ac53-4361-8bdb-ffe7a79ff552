[gd_scene load_steps=4 format=3 uid="uid://cbroc83leuwrp"]

[ext_resource type="PackedScene" uid="uid://cy82bsxfj1ddv" path="res://prophaunt/scenes/PropObject.tscn" id="1_73n2f"]
[ext_resource type="PackedScene" uid="uid://dlg6blgcsfaio" path="res://prophaunt/maps/Source/Furniture/bed_single.tscn" id="2_0olof"]

[sub_resource type="BoxShape3D" id="BoxShape3D_qk6m1"]
size = Vector3(0.583496, 0.376953, 1.13568)

[node name="BedSingleProp" instance=ExtResource("1_73n2f")]

[node name="BedSingle" parent="Meshes" index="0" instance=ExtResource("2_0olof")]

[node name="CollisionShape3D" type="CollisionShape3D" parent="StaticBody3D" index="0"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0.0012207, 0.19043, 0.0414124)
shape = SubResource("BoxShape3D_qk6m1")
