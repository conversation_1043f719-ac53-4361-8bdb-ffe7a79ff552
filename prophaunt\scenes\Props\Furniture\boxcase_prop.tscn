[gd_scene load_steps=4 format=3 uid="uid://by01s6pquawf6"]

[ext_resource type="PackedScene" uid="uid://cy82bsxfj1ddv" path="res://prophaunt/scenes/PropObject.tscn" id="1_xsaat"]
[ext_resource type="PackedScene" uid="uid://df1j8uxcytioo" path="res://prophaunt/maps/Source/Furniture/boxcase.tscn" id="2_cl6pp"]

[sub_resource type="BoxShape3D" id="BoxShape3D_n2ccm"]
size = Vector3(1.23079, 3.04532, 0.759491)

[node name="BoxcaseProp" instance=ExtResource("1_xsaat")]

[node name="Boxcase" parent="Meshes" index="0" instance=ExtResource("2_cl6pp")]

[node name="CollisionShape3D" type="CollisionShape3D" parent="StaticBody3D" index="0"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0.00249478, 1.52552, 0.00761414)
shape = SubResource("BoxShape3D_n2ccm")
