[gd_scene load_steps=6 format=4 uid="uid://dy07d3t08fllp"]

[ext_resource type="Texture2D" uid="uid://qkq7mh1bb3ul" path="res://prophaunt/Mat/Colormap2.png" id="1_cm652"]

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_gy4dw"]
albedo_texture = ExtResource("1_cm652")

[sub_resource type="ArrayMesh" id="ArrayMesh_aelcf"]
_surfaces = [{
"aabb": AABB(-1, -0.1, -1, 2, 0.1, 2),
"format": 34359742465,
"index_count": 36,
"index_data": PackedByteArray("BQAAAAIABQAEAAAAAgAGAAUAAgADAAYABAABAAAABAAHAAEAAgABAAMAAgAAAAEABgAEAAUABgAHAAQABwADAAEABwAGAAMA"),
"name": "Material",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 8,
"vertex_data": PackedByteArray("AACAP2khorMAAIC/AACAP9fMzL0AAIC/AACAP2khojMAAIA/AACAP8PMzL0AAIA/AACAv2khorMAAIC/AACAv2khojMAAIA/AACAv8PMzL0AAIA/AACAv9fMzL0AAIC/")
}]
blend_shape_mode = 0

[sub_resource type="ArrayMesh" id="ArrayMesh_bx8f3"]
resource_name = "FloorFull_FloorFull"
_surfaces = [{
"aabb": AABB(-1, -0.1, -1, 2, 0.1, 2),
"attribute_data": PackedByteArray("OrBBPyJQwz46sEE/IlDDPjqwQT8iUMM+OrBBPyJQwz46sEE/IlDDPjqwQT8iUMM+OrBBPyJQwz46sEE/IlDDPjqwQT8iUMM+OrBBPyJQwz46sEE/IlDDPjqwQT8iUMM+OrBBPyJQwz46sEE/IlDDPjqwQT8iUMM+OrBBPyJQwz46sEE/IlDDPjqwQT8iUMM+OrBBPyJQwz46sEE/IlDDPjqwQT8iUMM+OrBBPyJQwz46sEE/IlDDPjqwQT8iUMM+"),
"format": 34359742487,
"index_count": 36,
"index_data": PackedByteArray("EAABAAcAEAANAAEABgASAA8ABgAJABIADAADAAAADAAVAAMACAAFAAsACAACAAUAFAAOABEAFAAXAA4AFgAKAAQAFgATAAoA"),
"material": SubResource("StandardMaterial3D_gy4dw"),
"name": "Material",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 24,
"vertex_data": PackedByteArray("AACAP2khorMAAIC/AACAP2khorMAAIC/AACAP2khorMAAIC/AACAP9fMzL0AAIC/AACAP9fMzL0AAIC/AACAP9fMzL0AAIC/AACAP2khojMAAIA/AACAP2khojMAAIA/AACAP2khojMAAIA/AACAP8PMzL0AAIA/AACAP8PMzL0AAIA/AACAP8PMzL0AAIA/AACAv2khorMAAIC/AACAv2khorMAAIC/AACAv2khorMAAIC/AACAv2khojMAAIA/AACAv2khojMAAIA/AACAv2khojMAAIA/AACAv8PMzL0AAIA/AACAv8PMzL0AAIA/AACAv8PMzL0AAIA/AACAv9fMzL0AAIC/AACAv9fMzL0AAIC/AACAv9fMzL0AAIC//////////7//f///////v////3////+//////////7//fwAA////v////3////+//3//f////z//f///////v////3////+//3//f////z//fwAA////v////3////+//////////7//f///////vwAA/3////+//3//f////z//f///////vwAA/3////+//3//f////z//fwAA////vwAA/3////+//////////7//fwAA////vwAA/3////+/")
}]
blend_shape_mode = 0
shadow_mesh = SubResource("ArrayMesh_aelcf")

[sub_resource type="BoxShape3D" id="BoxShape3D_bqjc3"]
size = Vector3(2, 0.110413, 2.00391)

[node name="FloorFull" type="MeshInstance3D" groups=["VisibleGroup2"]]
mesh = SubResource("ArrayMesh_bx8f3")
skeleton = NodePath("")

[node name="StaticBody3D" type="StaticBody3D" parent="."]

[node name="CollisionShape3D" type="CollisionShape3D" parent="StaticBody3D"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, -0.0475159, 0)
shape = SubResource("BoxShape3D_bqjc3")
