extends Control
class_name Prop<PERSON>aunt<PERSON><PERSON>

# Prophaunt client-side game controller
# Manages the client-side game state and UI

@onready var player_parent: Node3D = $Player
@onready var map_http_request: HTTPRequest = $MapHTTPRequest
@onready var client_request: HTTPRequest = $ClientRequest


# Game UI
@onready var game_ui: ProphauntGameUI = $ProphauntGameUI
@onready var find_game_hud: Control = $FindGameHUD
@onready var find_game_loading: Panel = $FindGameHUD/Loading
@onready var find_game_found_panel: Panel = $FindGameHUD/FindGame
@onready var server_check_label: Label = $FindGameHUD/Loading/ServerCheckLabel
@onready var waiting_label: Label = $FindGameHUD/Loading/WaitingLabel
@onready var camera_controller: Node3D = $CameraController
@onready var touch_controller: ProphauntTouchController = $ProphauntTouchController
@onready var camera3d: Camera3D = $CameraController/SpringArm3D/Camera3D


var server:Server
var client:Client
var server_node = preload("res://networking/server.tscn")
var client_node = preload("res://networking/client.tscn")
var selected_map_packed
var selected_map

# Game state
var local_player_id: int = -1
var current_team: Constants.ProphauntTeam
var game_started: bool = false
var connected_to_server: bool = false

# Player management
var prophaunt_player_manager: ProphauntPlayerManager

# Prop detection system
var prop_detection_timer: float = 0.0
var prop_detection_interval: float = 0.1  # Check every 0.1 seconds
var current_prop_object: PropObject = null
var raycast_query: PhysicsRayQueryParameters3D

#Client
var server_index = 0 #For selecting which server to connect
var available_servers = [] #Result of all available servers
var player: Character


func _ready():
	# Initialize
	Selector.selected_game_mode = Constants.GameMode.Prophaunt
	server = server_node.instantiate()

	server.player_parent = player_parent
	add_child(server)
	server.game_scene = self
	Constants.server = server
	server.on_new_game()
	
	client = client_node.instantiate()
	client.game_scene = self
	ClientRPC.client = client
	client.connect_success.connect(on_client_connect_success)
	client.error_connecting.connect(on_client_error_connecting)
	client.disconnected.connect(on_client_disconnect)
	add_child(client)
	client.remote_parent = player_parent
	Constants.client = client
	client.server = server
	server.client = client
	
	if Constants.is_client():
		local_player_id = multiplayer.get_unique_id()
	else:
		init_server_mode()

	# Initialize player manager
	prophaunt_player_manager = ProphauntPlayerManager.get_instance()

	# Connect touch controller signals
	touch_controller.change_prop_pressed.connect(on_change_prop_button_pressed)

	# Connect to server
	if Constants.is_client():
		connect_to_prophaunt_server()


func init_server_mode():
	print("PropHaunt game. init server mode")
	send_select_map_request()


func client_init():
	if not Constants.is_client():
		return
	
	if StageManager.current_stage > 1:
		return
	
	SoundManager.play_bg_music()
	
	
	find_game_hud.visible = true
	find_game_loading.visible = true
	find_game_found_panel.visible = false
	var url = Constants.BACKEND_URL + "/game/find_prophaunt_game/"
	var data = {}
	var json = JSON.stringify(data)
	var headers = ["Content-Type: application/json", "Authorization: JWT " + DataSaver.get_item("JWT", "")]
	
	if len(available_servers) == 0:
		client_request.cancel_request()
		client_request.request(url, headers, HTTPClient.METHOD_POST, json)
	else:
		if server_index >= len(available_servers):
			print("no server can serve you change your net!")
			ClientBackendManager.send_server_test_fail_request()
			on_client_disconnect()
		else:
			check_next_server()


#Client
func check_next_server():
	find_game_hud.visible = false
	find_game_loading.visible = true
	
	waiting_label.visible = false
	server_check_label.visible = true
	server_check_label.text = tr("SERVER_CHECK") + "#" + str(server_index + 1)
	#Create player and add to scene
	if Constants.is_headless:
		#init_player_character_headless()#For bots in previous implementation
		pass
	else:
		pass
		#Do Nothing
		#init_player_character()
	var current_server = available_servers[server_index]
	Constants.URL = current_server["ip"]
	Constants.PORT = int(current_server["port"])
	#Constants.URL = "************"
	#Constants.PORT = 11019
	StageManager.total_stages = current_server["total_stage"]
	StageManager.is_finished = false
	ClientRPC.connect_to_server()
	server_index += 1


func connect_to_prophaunt_server():
	client_init()

	# Initialize game UI
	initialize_game_ui()


func initialize_game_ui():
	"""Initialize the game UI"""

	# Hide initial UI elements
	game_ui.visible = true


func client_load_map(resource):
	if Constants.is_headless:#ClientHeadless
		return
	
	selected_map = load(resource)
	selected_map = selected_map.instantiate()
	selected_map.game_scene = self
	server.map = selected_map
	add_child(selected_map)
	client.iam_ready()


func set_my_team(team: Constants.ProphauntTeam):
	"""Set the local player's team"""
	current_team = team

	if game_ui:
		game_ui.set_team(team)
	
	if team == Constants.ProphauntTeam.HAUNTERS:
		touch_controller.set_haunter()
	else:
		touch_controller.set_prop()

	#print("Local player assigned to team: ", "Props" if team == Constants.ProphauntTeam.PROPS else "Haunters")


func prophaunt_lobby_update(lobby_data: Dictionary):
	"""Handle lobby update from server"""
	var _players_count = lobby_data.get("j", 0)
	var _min_players = lobby_data.get("s", 2)
	var _timer = lobby_data.get("t", 0)


func prophaunt_round_start(round_number: int, duration: float):
	"""Handle round start"""
	if game_ui:
		game_ui.on_round_started(round_number, duration)
	find_game_hud.visible = false
	game_started = true

	if game_ui:
		game_ui.show_notification("PROPHAUNT GAME STARTED!", 3.0)


func prophaunt_round_end(winner_team: Constants.ProphauntTeam, _results: Dictionary):
	"""Handle round end"""
	if game_ui:
		game_ui.on_round_ended(winner_team)

	print("Round ended - Winner: ", "Props" if winner_team == Constants.ProphauntTeam.PROPS else "Haunters")


@rpc("authority", "call_local", "reliable")
func prophaunt_game_update(game_data: Dictionary):
	"""Handle game state update"""
	var round_timer = game_data.get("round_timer", 0.0)
	var props_alive = game_data.get("props_alive", 0)
	var total_props = game_data.get("total_props", 0)
	var current_round = game_data.get("current_round", 1)

	if game_ui:
		game_ui.update_round_info(current_round, round_timer)
		game_ui.update_props_info(props_alive, total_props)

	# Update local player health if prop
	var player_states = game_data.get("player_states", {})
	if local_player_id in player_states:
		var player_state = player_states[local_player_id]
		var hp = player_state.get("hp", 100)
		var max_hp = 100  # Could be dynamic

		if game_ui and current_team == Constants.ProphauntTeam.PROPS:
			game_ui.update_health(hp, max_hp)


@rpc("authority", "call_local", "reliable")
func prophaunt_player_eliminated(player_id: int):
	"""Handle player elimination"""
	if game_ui:
		game_ui.on_player_eliminated(player_id)

	print("Player ", player_id, " eliminated")


@rpc("authority", "call_local", "reliable")
func prophaunt_player_damaged(player_id: int, new_hp: int):
	"""Handle player damage"""
	if player_id == local_player_id and game_ui:
		game_ui.update_health(new_hp, 100)

	print("Player ", player_id, " damaged - HP: ", new_hp)


@rpc("authority", "call_local", "reliable")
func prophaunt_player_hexed(player_id: int, duration: float):
	"""Handle player hex"""
	if game_ui:
		var message = "Player hexed for " + str(duration) + " seconds!"
		game_ui.show_notification(message, 2.0)

	print("Player ", player_id, " hexed for ", duration, " seconds")


@rpc("authority", "call_local", "reliable")
func prophaunt_player_unhexed(player_id: int):
	"""Handle player unhex"""
	print("Player ", player_id, " unhexed")


@rpc("authority", "call_local", "reliable")
func prophaunt_player_disguised(player_id: int, disguise_index: int):
	"""Handle player disguise change"""
	var p:Character = server.players[player_id]
	var disguise = p.prophaunt_player.disguise_system.find_disguise_by_index(disguise_index)
	p.prophaunt_player.disguise_system.apply_disguise(disguise)


@rpc("authority", "call_local", "reliable")
func prophaunt_round_warning(warning_type: String, time_remaining: float):
	"""Handle round time warning"""
	if game_ui:
		game_ui.on_time_warning(warning_type)

	print("Round warning: ", warning_type, " - ", time_remaining, " seconds")


@rpc("authority", "call_local", "reliable")
func sync_all_players_start_data(packed_data: PackedByteArray):
	var player_count = packed_data.decode_u8(0)
	var offset = 1
	
	print("start data: ", player_count, " ", packed_data.size())

	for i in range(player_count):
		if offset >= packed_data.size():
			break


		var _player_data = decodeNewPlayerStart(packed_data, offset)
		offset += Constants.PROPHAUNT_SYNC_START_SIZE  # Haunter data size


@rpc("authority", "call_local", "unreliable_ordered")
func sync_all_players_data(packed_data: PackedByteArray):
	"""Receive and decode all players data from server"""
	if packed_data.size() < 1:
		return

	var player_count = packed_data.decode_u8(0)
	var offset = 1

	for i in range(player_count):
		if offset >= packed_data.size():
			break

		# Read team first to determine decode method
		var team = packed_data.decode_u8(offset + 4)
		var player_data: Dictionary

		if team == Constants.ProphauntTeam.PROPS:
			player_data = decodeProp(packed_data, offset)
			offset += Constants.PROPHAUNT_SYNC_PROP_SIZE  # Prop data size
		else:
			player_data = decodeHaunter(packed_data, offset)
			offset += Constants.PROPHAUNT_SYNC_HAUNTER_SIZE  # Haunter data size

		if player_data["id"] == multiplayer.get_unique_id():
			#it's me
			update_me(player_data)
		elif not client.remote_players_data.has(player_data["id"]):
			#new_remote_player(player_data)
			pass
		else:
			# Update local player representation
			update_player_from_sync_data(player_data)


func decodeNewPlayerStart(buf: PackedByteArray, start: int):
	var player_data = {}
	
	player_data["id"] = buf.decode_u32(start + 0)
	if player_data["id"] == multiplayer.get_unique_id():
		#its me
		return
	
	player_data["selection"] = {"name": "", "character": "", "backend_id": 0}
	var _str = ""
	if buf.decode_var(start +  4):
		_str = buf.decode_var(start + 4)
	player_data["selection"]["name"] = _str
	var id = buf.decode_u16(start + 59)
	player_data["selection"]["character"] = Selector.find_character_by_id_in_characters(id)["path"]
	
	
	#print("new player! ", player_data["selection"]["name"], " ", player_data["selection"]["character"])
	id = player_data["id"]
	client.remote_players_data[id] = server.PLAYER_DEFAULT.duplicate(true)
	for key in player_data:
		client.remote_players_data[id][key] = player_data[key]
	
	#Create player scene
	var path = player_data["selection"]["character"]
	
	var scene = null
	if ResourceLoader.exists(path):
		scene = load(path)
	else:
		scene = load(Selector.get_random_character_path())
	client.remote_players_scenes[id] = scene.instantiate() as Character
	client.remote_players_scenes[id].name = str(id)
	client.remote_players_scenes[id].player_id = id
	client.remote_parent.add_child(client.remote_players_scenes[id])
	client.remote_players_scenes[id].make_remote()
	client.remote_players_scenes[id].server = client.server
	client.remote_players_scenes[id].global_position.x = 1000
	client.remote_players_scenes[id].global_position.y = 0
	client.remote_players_scenes[id].global_position.z = 1000
	client.remote_players_scenes[id].set_character_name(player_data["selection"]["name"])
	
	return [player_data["id"], player_data]


func decodeProp(buf: PackedByteArray, start: int) -> Dictionary:
	"""Decode prop player data from PackedByteArray"""
	var player_data = {}
	player_data["d"] = {}

	# Player ID (4 bytes)
	player_data["id"] = buf.decode_u32(start + 0)

	# Team (1 byte)
	player_data["prophaunt_team"] = buf.decode_u8(start + 4)

	# Position (6 bytes)
	player_data["d"]["p"] = Vector3(
		buf.decode_half(start + 5),
		buf.decode_half(start + 7),
		buf.decode_half(start + 9)
	)

	# Rotation (6 bytes)
	player_data["d"]["R"] = Vector3(
		buf.decode_half(start + 11),
		buf.decode_half(start + 13),
		buf.decode_half(start + 15)
	)

	# Velocity (6 bytes)
	player_data["d"]["v"] = Vector3(
		buf.decode_half(start + 17),
		buf.decode_half(start + 19),
		buf.decode_half(start + 21)
	)

	# Animation (1 byte)
	player_data["d"]["a"] = buf.decode_u8(start + 23)

	# Prop ID/disguise (4 bytes)
	player_data["prophaunt_disguise"] = buf.decode_u8(start + 24)

	# Player state (1 byte)
	player_data["prophaunt_state"] = buf.decode_u8(start + 25)

	# HP (2 bytes)
	player_data["prophaunt_hp"] = buf.decode_u8(start + 26)

	return player_data


func decodeHaunter(buf: PackedByteArray, start: int) -> Dictionary:
	"""Decode haunter player data from PackedByteArray"""
	var player_data = {}
	player_data["d"] = {}

	# Player ID (4 bytes)
	player_data["id"] = buf.decode_u32(start + 0)

	# Team (1 byte)
	player_data["prophaunt_team"] = buf.decode_u8(start + 4)

	# Position (6 bytes)
	player_data["d"]["p"] = Vector3(
		buf.decode_half(start + 5),
		buf.decode_half(start + 7),
		buf.decode_half(start + 9)
	)

	# Rotation (6 bytes)
	player_data["d"]["R"] = Vector3(
		buf.decode_half(start + 11),
		buf.decode_half(start + 13),
		buf.decode_half(start + 15)
	)

	# Velocity (6 bytes)
	player_data["d"]["v"] = Vector3(
		buf.decode_half(start + 17),
		buf.decode_half(start + 19),
		buf.decode_half(start + 21)
	)

	# Animation (1 byte)
	player_data["d"]["a"] = buf.decode_u8(start + 23)

	# Player state (1 byte)
	player_data["prophaunt_state"] = buf.decode_u8(start + 24)

	# Hex cooldown (4 bytes)
	player_data["prophaunt_hp"] = buf.decode_u8(start + 25)

	return player_data


func update_player_from_sync_data(player_data: Dictionary):
	"""Update local player representation from sync data"""
	var player_id = player_data["id"]

	# Update server player data if it exists
	# Update position, rotation, velocity
	client.remote_players_data[player_id]["d"]["p"] = player_data["d"]["p"]
	client.remote_players_data[player_id]["d"]["R"] = player_data["d"]["R"]
	client.remote_players_data[player_id]["d"]["v"] = player_data["d"]["v"]
	client.remote_players_data[player_id]["d"]["a"] = player_data["d"]["a"]

	# Update prophaunt-specific data
	client.remote_players_data[player_id]["prophaunt_team"] = player_data["prophaunt_team"]
	client.remote_players_data[player_id]["prophaunt_state"] = player_data["prophaunt_state"]

	if player_data.has("prophaunt_disguise"):
		client.remote_players_data[player_id]["prophaunt_disguise"] = player_data["prophaunt_disguise"]
	if player_data.has("prophaunt_hp"):
		client.remote_players_data[player_id]["prophaunt_hp"] = player_data["prophaunt_hp"]

	# Update visual representation if player exists in scene
	var player_character:Character = client.remote_players_scenes[player_id]
	# Update position and rotation
	player_character.global_position = player_data["d"]["p"]
	player_character.rotation = player_data["d"]["R"]
	if player_character.prophaunt_player == null:
		prophaunt_player_manager.initialize_prophaunt_player(player_id, player_character, player_data["prophaunt_team"])
	
	
	if player_data["prophaunt_team"] == Constants.ProphauntTeam.PROPS:
		#print("prop: ", client.remote_players_data[player_id]["selection"]["name"], " ", player_data["prophaunt_disguise"], " ", player_character.global_position)
		if player_id == multiplayer.get_unique_id():
			print("it's me")
		player_character.prophaunt_player.disguise_system.apply_disguise_by_index(player_data["prophaunt_disguise"])
	else:
		pass
		#print("haunter: ", client.remote_players_data[player_id]["selection"]["name"], " ", client.remote_players_data[player_id]["selection"]["character"])


func update_me(player_data:Dictionary):
	if current_team == Constants.ProphauntTeam.HAUNTERS:
		return
	
	player.prophaunt_player.disguise_system.apply_disguise_by_index(player_data["prophaunt_disguise"])


@rpc("authority", "call_local", "reliable")
func prophaunt_final_results(results: Dictionary):
	"""Handle final game results"""
	print("Final results: ", results)

	if game_ui:
		game_ui.show_notification("GAME ENDED - CHECK RESULTS!", 5.0)


@rpc("authority", "call_local", "reliable")
func prophaunt_game_ended():
	"""Handle game end"""
	print("Prophaunt game ended")

	if game_ui:
		game_ui.show_notification("GAME ENDED", 3.0)


# UI event handlers
func _on_ability_used(ability_index: int):
	"""Handle ability use from UI"""
	var prophaunt_player = player.prophaunt_player

	match ability_index:
		0:  # First ability
			if current_team == Constants.ProphauntTeam.PROPS:
				# Disguise change
				var new_disguise = prophaunt_player.disguise_system.get_random_disguise()
				prophaunt_player.change_disguise(new_disguise)
			else:
				# Shoot
				player.rifleShootState.start_state()
		1:  # Second ability
			if current_team == Constants.ProphauntTeam.PROPS:
				# Hex
				prophaunt_player.cast_hex()
			else:
				# Grenade
				#var target_pos = get_aim_target()
				#prophaunt_player.throw_grenade(target_pos)
				player.start_grenade_shoot()
		2:  # Third ability
			# Additional abilities can be added here
			pass


func _on_chat_message_sent(message: String):
	"""Handle chat message from UI"""
	# Send chat message to server
	print("Chat message: ", message)


func get_aim_target() -> Vector3:
	"""Get the current aim target position"""
	var pos = touch_controller.haunter_aim.global_position + Vector2(touch_controller.haunter_aim.size) / 2
	#var start = camera_controller.camera3d.project_position(pos, 0)
	var end = camera_controller.camera3d.project_position(pos, 50)
	return end


func _on_back_button_pressed():
	"""Handle back button press"""
	SoundManager.play_click_sound()

	# Disconnect from server if connected
	if connected_to_server:
		# Disconnect logic here
		pass

	get_tree().change_scene_to_file("res://Scenes/main_menu.tscn")


func _process(delta):
	"""Update game controller"""
	if Input.is_action_just_pressed("exit"):
		_on_back_button_pressed()


	if Constants.is_client():
		if server.state == Constants.ServerState.InGame:
			if current_team == Constants.ProphauntTeam.HAUNTERS:
				if Input.is_action_just_pressed("Grenade"):
					player.start_grenade_shoot()
			if current_team == Constants.ProphauntTeam.PROPS:
				if Input.is_action_just_pressed("ChangeProp"):
					on_change_prop_button_pressed()

	# Update UI cooldowns if game is active
	if game_started and game_ui and prophaunt_player_manager:
		update_ability_cooldowns()

	# Update prop detection for props team
	if game_started and current_team == Constants.ProphauntTeam.PROPS:
		update_prop_detection(delta)


func update_ability_cooldowns():
	"""Update ability cooldown displays"""
	var prophaunt_player = prophaunt_player_manager.get_prophaunt_player(local_player_id)
	if not prophaunt_player:
		return

	if current_team == Constants.ProphauntTeam.PROPS:
		# Prop abilities
		var disguise_cooldown = 0.0
		var hex_cooldown = prophaunt_player.hex_cooldown

		if prophaunt_player.disguise_system:
			disguise_cooldown = prophaunt_player.disguise_system.get_disguise_cooldown_remaining()

		game_ui.update_ability_cooldown(0, disguise_cooldown)
		game_ui.update_ability_cooldown(1, hex_cooldown)
		game_ui.update_ability_cooldown(2, 0.0)  # No third ability yet
	else:
		# Haunter abilities
		var gun_cooldown = 0.0
		var grenade_cooldown = 0.0

		if prophaunt_player.weapon_system:
			gun_cooldown = prophaunt_player.weapon_system.get_gun_cooldown_remaining()
			grenade_cooldown = prophaunt_player.weapon_system.get_grenade_cooldown_remaining()

		game_ui.update_ability_cooldown(0, gun_cooldown)
		game_ui.update_ability_cooldown(1, grenade_cooldown)
		game_ui.update_ability_cooldown(2, 0.0)  # No third ability yet


func update_prop_detection(delta: float):
	"""Update prop detection system for props team"""
	prop_detection_timer += delta

	if prop_detection_timer >= prop_detection_interval:
		prop_detection_timer = 0.0
		check_for_prop_objects()


func check_for_prop_objects():
	"""Cast a ray from center of screen to detect prop objects"""
	if not player or not camera_controller:
		return

	# Get screen center
	var screen_center = touch_controller.prop_cursor.global_position + touch_controller.prop_cursor.size / 2

	# Create ray from camera through screen center
	var from = camera3d.project_ray_origin(screen_center)
	var to = from + camera3d.project_ray_normal(screen_center) * 100  # 100 units range

	# Setup raycast query
	var space_state = player.get_world_3d().direct_space_state
	if not raycast_query:
		raycast_query = PhysicsRayQueryParameters3D.new()

	raycast_query.from = from
	raycast_query.to = to
	raycast_query.exclude = [player]  # Exclude the player

	# Perform raycast
	var result = space_state.intersect_ray(raycast_query)

	var new_prop_object = null
	if result:
		var collider = result.get("collider")
		#print(collider, " ", collider.get_parent().name)
		if collider and is_prop_object(collider):
			new_prop_object = collider.get_parent()

	# Update cursor based on detection
	if new_prop_object != current_prop_object:
		current_prop_object = new_prop_object
		update_prop_cursor()


func is_prop_object(object: Node) -> bool:
	"""Check if the object is a valid prop object that can be disguised as"""
	if not is_instance_valid(object):
		return false

	# Check if object has the "PropObject" group or specific naming convention
	if object.get_parent().is_in_group("PropObject"):
		return object.get_parent().enabled
	
	return false


func update_prop_cursor():
	"""Update the touch controller cursor based on prop detection"""
	if current_prop_object:
		# Show prop cursor when looking at a prop object
		touch_controller.prop_cursor.visible = true
		touch_controller.prop_normal_cursor.visible = false
	else:
		# Show normal cursor when not looking at a prop object
		touch_controller.prop_cursor.visible = false
		touch_controller.prop_normal_cursor.visible = true


func on_change_prop_button_pressed():
	"""Handle change prop button press"""
	if current_team != Constants.ProphauntTeam.PROPS:
		return

	if not current_prop_object:
		return

	# Get prop ID from the object
	var prop_id = current_prop_object.index
	if prop_id == -1:
		return

	# Send RPC to server to change disguise
	var prophaunt_in_game = server.prophaunt_in_game
	if prophaunt_in_game:
		prophaunt_in_game.prophaunt_change_disguise.rpc_id(1, prop_id)


#Client
func on_client_connect_success():
	init_my_player_character()
	client.init_after_connect()


func init_my_player_character():
	var selected_player = load(Selector.selected_character["path"])
	player = selected_player.instantiate()
	player.name = str(multiplayer.get_unique_id())
	player_parent.add_child(player)
	player.clientNetworkHistory.reset()
	player.make_player()
	player.set_camera(camera_controller)
	player.set_controller(touch_controller)
	player.set_character_name(Selector.my_name)
	player.client = client
	player.server = server
	player.remove_native_character_item()
	player.add_selected_rifle()
	client.set_my_player_scene(player, selected_player.resource_path, Selector.selected_character["id"])
	touch_controller.camera_controller = camera_controller
	touch_controller.client = client
	player.rifle_shoot.connect(server.prophaunt_in_game.on_my_rifle_shoot)
	player.grenade_shoot.connect(server.prophaunt_in_game.on_my_grenade_shoot)
	return player


#Server
func send_select_map_request():
	var url = Constants.BACKEND_URL + "/game/select_prophaunt_map/"
	var headers = ["Content-Type: application/json", "Authorization: JWT " + Constants.BACKEND_TOKEN]
	var data = {
		"id": Constants.BACKEND_ID,
	}
	var json = JSON.stringify(data)
	map_http_request.cancel_request()
	map_http_request.request(url, headers, HTTPClient.METHOD_POST, json)


func _on_select_map_request_completed(_result: int, response_code: int, _headers: PackedStringArray, body: PackedByteArray) -> void:
	if response_code == 200:
		var json = JSON.parse_string(body.get_string_from_utf8())
		
		var data = json["map"]
		BackendManager.map_id = data["id"]
		server.MAX_PLAYERS = data["max_players_count"]
		Constants.LOBBY_PLAYER_COUNT_START = data["min_players_count"]
		server.BOT_WAIT_REMOVE = data["bot_wait_remove"]
		server.BOT_RESET_TIME = data["bot_reset_time"]
		#print("reset time = ", server.BOT_RESET_TIME)
		
		selected_map_packed = load(data["path"])
		
		selected_map = selected_map_packed.instantiate()
		add_child(selected_map)
		print("Map Selected and Loaded: ", selected_map_packed.resource_path)
		server.map = selected_map
		server.map_packed_scene = selected_map_packed
		UniversalSyncManager.map = selected_map
		
		
		Constants.SERVER_IN_GAME_TIME = json["settings"]["game_time"]
		Constants.SERVER_PROPHAUNT_LOBBY_TIME = json["settings"]["prophaunt_lobby_time"]
		Constants.PROPHAUNT_ROUND_TIME = json["settings"]["prophaunt_round_time"]
		Constants.SERVER_COUNTDOWN_TIME = json["settings"]["countdown_time"]
		Constants.SERVER_RESULT_TIME = json["settings"]["result_time"]
		if BackendManager.server_is_test:
			Constants.LOBBY_TIME = 5
		if StageManager.current_stage > 1:
			Constants.LOBBY_TIME = 0.1
		Constants.SERVER_PLAYER_JOINED_ADD_LOBBY_TIME = json["settings"]["player_join_add_time"]
		print ("Server Settings: RoundTime=", Constants.PROPHAUNT_ROUND_TIME, " LobbyTime=", Constants.SERVER_PROPHAUNT_LOBBY_TIME)
		server.set_state_to_lobby()
	else:
		print("map select error: ", response_code)
		if response_code == 400:
			print("MAP SELECT SHOULD RESTART ASAP")
			await get_tree().create_timer(2).timeout
			StageManager.is_finished = true
			StageManager.server_instance = server
			Constants.server = null
			server.restart_server()
			server = null
			StageManager.server_instance = null
			return
		
		await get_tree().create_timer(2).timeout
		send_select_map_request()


#Client 
func on_game_server_find(_result, response_code, _headers, body):
	if response_code == 200:
		var res = JSON.parse_string(body.get_string_from_utf8())
		var json = res["best_server"]
		available_servers = []
		available_servers.append({
			"ip": json["ip"],
			"port": int(json["port"]),
			"total_stage": json["total_stage"],
		})
		available_servers.append_array(res["all_servers"])
		server_index = 0
		check_next_server()
	else:
		print(response_code, body.get_string_from_utf8())
		on_client_error_connecting()


func on_client_error_connecting():
	pass


func on_client_disconnect():
	pass


func _on_find_cancel_button_pressed() -> void:
	Selector.is_exit_game = true
	ClientRPC.disconnect_from_server()
	get_tree().change_scene_to_file("res://Scenes/main_menu.tscn")


func create_and_add_bot():
	var random_character = Selector.get_random_character_v2()
	var bot_data = client.my_data.duplicate(true)
	bot_data["backend_id"] = -randi() % 1000
	#bot_data["selection"]["name"] = "AnimalRush#" + str(randi() % 1000)
	bot_data["selection"]["name"] = BotNameGenerator.get_random_name()
	bot_data["selection"]["character"] = random_character["path"]
	bot_data["selection"]["character_id"] = random_character["id"]
	server.new_bot_player(bot_data)
