extends Node

signal i_damaged

var client: Client
var peer:ENetMultiplayerPeer = null

func connect_to_server():
	if Constants.is_headless:
		Constants.URL = Constants.SERVER_IP
		Constants.PORT = Constants.SERVER_PORT
	
	print("connecting: ", Constants.URL, ":", Constants.PORT)
	peer = ENetMultiplayerPeer.new()

	peer.create_client(Constants.URL, Constants.PORT)
	peer.get_host().compress(ENetConnection.COMPRESS_RANGE_CODER)
	multiplayer.set_multiplayer_peer(peer)
	client.connecting = true
	client.connecting_timer = 0


func disconnect_from_server():
	if peer:
		peer.close()
		peer = null


@rpc("authority", "call_remote", "unreliable_ordered", Constants.CHANNEL_PLAYER_SYNC)
func sync_players_unreliable(players_data):
	if Constants.is_headless:
		return
	if peer == null:
		return
	if in_between_stages():
		return
	
	client.sync_players(players_data)


@rpc("authority", "call_remote", "reliable", Constants.CHANNEL_NEW_PLAYER)
func sync_players_reliable(players_data):
	if Constants.is_headless:
		return
	if peer == null:
		return

	if in_between_stages():
		return

	client.sync_players(players_data)


var copy_my_data
var sync_my_data_time = 0
@rpc("authority", "call_remote", "unreliable_ordered", Constants.CHANNEL_INDIE_PLAYER_SYNC)
func sync_my_data(_my_data):
	if Constants.is_headless:
		return
	if peer == null:
		return

	if in_between_stages():
		return

	var dt = Constants.convert_packed_byte_array_to_player_data(_my_data)
	copy_my_data = dt
	sync_my_data_time = Time.get_ticks_msec()
	if TickManager.tick_counter == -1:
		TickManager.set_start_tick(copy_my_data["tick"])
		client.my_player_scene.clientNetworkHistory.start(dt)
	else:
		var should_force = client.server.state == Constants.ServerState.CountDown
		should_force = should_force or client.im_server_authoritive
		if should_force:
			client.my_player_scene.clientNetworkHistory.on_server_snapshot(copy_my_data)
		if Selector.selected_game_mode == Constants.GameMode.FreeRide:
			if client.game_scene.client_state != 2: # 2=CLIENT_STATE.Game
				client.my_player_scene.clientNetworkHistory.on_server_snapshot(copy_my_data)
	TickManager.last_tick_received = copy_my_data["tick"]


@rpc("authority", "call_remote", "unreliable_ordered", Constants.CHANNEL_FORCE_PLAYER_SYNC)
func force_sync_my_data(_my_data):
	if Constants.is_headless:
		return
	if peer == null:
		return
	
	_my_data = Constants.convert_packed_byte_array_to_player_data(_my_data)
	sync_my_data_time = Time.get_ticks_msec()
	if TickManager.tick_counter == -1:
		if is_instance_valid(copy_my_data):
			TickManager.set_start_tick(copy_my_data["tick"])
		client.my_player_scene.clientNetworkHistory.start(_my_data)
	else:
		client.my_player_scene.clientNetworkHistory.on_server_snapshot(_my_data)
		if Selector.selected_game_mode != Constants.GameMode.Prophaunt:
			client.my_player_scene.checkpoint(client.my_player_scene.global_position)
	TickManager.last_tick_received = _my_data["tick"]
	client.my_player_scene.check_ami_dead()


@rpc("authority", "call_remote", "reliable", Constants.CHANNEL_FORCE_PLAYER_SYNC)
func force_sync_my_data_reliable(_my_data):
	if Constants.is_headless:
		return
	if peer == null:
		return
	
	_my_data = Constants.convert_packed_byte_array_to_player_data(_my_data)
	sync_my_data_time = Time.get_ticks_msec()
	if TickManager.tick_counter == -1:
		TickManager.set_start_tick(copy_my_data["tick"])
		client.my_player_scene.clientNetworkHistory.start(_my_data)
	else:
		client.my_player_scene.clientNetworkHistory.on_server_snapshot(_my_data)
		client.my_player_scene.checkpoint(client.my_player_scene.global_position)
	TickManager.last_tick_received = _my_data["tick"]


@rpc("reliable", "authority", "call_remote", Constants.CHANNEL_LOBBY)
func map_details(map_data):
	if Constants.is_headless:
		return
	if peer == null:
		return
	if Selector.selected_game_mode == Constants.GameMode.FreeRide:
		#print("map details: ", map_data)
		client.game_scene.client_load_map(map_data["resource"])
		return
	
	if Selector.selected_game_mode == Constants.GameMode.Prophaunt:
		#print("map details: ", map_data)
		client.game_scene.client_load_map(map_data["resource"])
		return

	#RACE:
	
	if StageManager.current_stage == 1:
		if in_between_stages():
			return
		client.game_scene.load_map(map_data["resource"])
		print("loading finished!")
	else:
		StageManager.on_next_stage_map_received(map_data)


@rpc("reliable", "authority", "call_remote", Constants.CHANNEL_GAME_START)
func game_start():
	if Constants.is_headless:
		return
	if peer == null:
		return

	if in_between_stages():
		return

	client.game_scene.start_game()


@rpc("unreliable", "authority", "call_remote", Constants.CHANNEL_LOBBY_UNRELIABLE)
func lobby_update(data):
	if Constants.is_headless:
		return
	if peer == null:
		return

	if in_between_stages():
		return

	client.lobby_players_joined = data["j"]
	client.lobby_players_start = data["s"]
	client.lobby_timer = data["t"]
	GameSettings.qualified_game_finished = data["q_count"]


@rpc("reliable", "authority", "call_remote", Constants.CHANNEL_RESULT)
func update_result(player_id):#Qualifed or Eliminated
	if Constants.is_headless:
		return
	if peer == null:
		return

	if in_between_stages():
		return

	if client.server.map.type == Constants.MapType.Qualification:
		GameSettings.qualified_players.append(player_id)
		if multiplayer.get_unique_id() == player_id:
			#I Win
			client.spector_mode = true
			client.server.map.i_quailifed()
			client.server.map.get_parent().set_spector_mode() #GameScene
			client.i_qualified = true
			client.spector_player_index = 0
			client.my_player_scene.disable()
			client.tag_eliminate_me()
		else:
			client.tag_eliminate(player_id)
			if client.remote_players_scenes.has(player_id):
				if is_instance_valid(client.remote_players_scenes[player_id]):
					client.remote_players_scenes[player_id].disable()
	
	if client.server.map.type == Constants.MapType.Elimination:
		print("eliminated ", player_id)
		GameSettings.eliminated_players.append(player_id)
		if multiplayer.get_unique_id() == player_id:
			#I Eliminated
			client.spector_mode = true
			client.server.map.i_eliminated()
			client.server.map.get_parent().set_spector_mode() #GameScene
			client.i_eliminated = true
			client.spector_player_index = 0
			client.my_player_scene.disable()
			client.tag_eliminate_me()
		else:
			client.tag_eliminate(player_id)
			if client.remote_players_scenes.has(player_id):
				if is_instance_valid(client.remote_players_scenes[player_id]):
					client.remote_players_scenes[player_id].disable()


@rpc("reliable", "authority", "call_remote", Constants.CHANNEL_RESULT)
func map_finished(qualified_list):
	if Constants.is_headless:
		return
	if peer == null:
		return

	if in_between_stages():
		return
	GameSettings.backend_game_id = qualified_list.pop_front()
	if client.server.map.type == Constants.MapType.Qualification:
		get_tree().paused = true
		if not client.i_qualified:
			#i lose
			client.server.map.map_finished()
			if not StageManager.me_eliminated:
				await get_tree().create_timer(2).timeout
				client.server.map.i_eliminated()
		else:
			client.server.map.map_finished()
	
	if client.server.map.type == Constants.MapType.Elimination:
		get_tree().paused = true
		if StageManager.me_eliminated or client.i_eliminated:
			client.server.map.map_finished()
		else:
			client.server.map.i_quailifed()
	
	StageManager.set_qualified_list(qualified_list)

	if not is_qualified(multiplayer.get_unique_id()):
		StageManager.me_eliminated = true
	StageManager.client_players_data = client.all_players_selection_data.duplicate(true)
	
	
	await get_tree().create_timer(3).timeout
	if is_instance_valid(client):
		client.server.state = Constants.ServerState.Lobby
	StageManager.on_stage_finished(null)
	get_tree().change_scene_to_file("res://Scenes/result_scene.tscn")


@rpc("unreliable_ordered", "authority", "call_remote", Constants.CHANNEL_TIMER_SYNC)
func countdown(data):
	if Constants.is_headless:
		return
	if peer == null:
		return

	if in_between_stages():
		return
	GameSettings.qualified_game_finished = data["q_count"]
	GameSettings.current_stage_players_count = data["total_count"]
	client.game_scene.update_countdown(data["time"])


@rpc("unreliable_ordered", "authority", "call_remote", Constants.CHANNEL_TIMER_SYNC)
func ingame_timer(time):
	if Constants.is_headless:
		return
	if peer == null:
		return

	if in_between_stages():
		return
	client.game_scene.update_in_game_timer(time)


var last_pong_time = -1
@rpc("reliable", "any_peer", "call_remote", Constants.CHANNEL_PING)
func pong(id):
	if Constants.is_headless:
		return
	if peer == null:
		return
	
	#print("pong: ", id, " client_id = ", client.ping_id)
	last_pong_time = Time.get_ticks_msec()
	if id == client.ping_id:
		@warning_ignore("integer_division")
		client.ping_ms = (Time.get_ticks_msec() - client.ping_time) / 2


@rpc("unreliable", "authority", "call_remote", Constants.CHANNEL_EMOTE)
func emote_player(data):
	if Constants.is_headless:
		return
	if peer == null:
		return

	#data = {
	#	"p": #player id
	#	"e": #emote id
	#	"t": #emote show time
	#}
	#print("show emote received for: ", data)
	var player_scene = null
	if data["p"] == multiplayer.get_unique_id():
		#it's me!
		player_scene = client.my_player_scene
	else:
		if not client.remote_players_scenes.has(data["p"]):
			return
		player_scene = client.remote_players_scenes[data["p"]]
	
	if player_scene != null and is_instance_valid(player_scene):
		player_scene.show_emote(data["e"], data["t"])


@rpc("authority", "call_remote", "reliable", Constants.CHANNEL_CHANGE_CHARACTER)
func character_changed(changed_data):
	if Constants.is_headless:
		return
	if peer == null:
		return

	if changed_data["id"] == multiplayer.get_unique_id():
		#it's me!
		return
	#print("received changed data: ", changed_data)
	
	var id = changed_data["id"]
	var path = changed_data["character"]
	if path == null:
		return
	var scene = load(path)
	var previous_scene = client.remote_players_scenes[id]
	previous_scene.disable()
	client.remote_players_scenes[id] = scene.instantiate()
	client.remote_players_scenes[id].name = str(id)
	client.remote_parent.add_child(client.remote_players_scenes[id])
	client.remote_players_scenes[id].make_remote()
	client.remote_players_scenes[id].set_character_name(previous_scene.get_character_name())
	client.remote_players_scenes[id].server = client.server
	
	await get_tree().process_frame
	await get_tree().process_frame
	previous_scene.queue_free()


@rpc("authority", "call_remote", "reliable", Constants.CHANNEL_CHANGE_CHARACTER)
func character_name_changed(changed_data):
	if Constants.is_headless:
		return
	if peer == null:
		return

	if changed_data["id"] == multiplayer.get_unique_id():
		return
	
	var id = changed_data["id"]
	var handle = changed_data["handle"]
	if client.remote_players_data.has(id):
		client.remote_players_data[id]["selection"]["name"] = handle
		client.remote_players_scenes[id].set_character_name(handle)


@rpc("authority", "call_remote", "unreliable_ordered", Constants.CHANNEL_UNIVERSAL_SYNC)
func freeride_network_sync(packed_data: PackedByteArray):
	if Constants.is_headless:
		return
	if peer == null:
		return
	
	if client.network_visibility_manager:
		client.network_visibility_manager.apply_packed_data(packed_data)


@rpc("authority", "call_remote", "reliable", Constants.CHANNEL_PLAYER_SYNC)
func set_my_authoritive(am_i: bool) -> void:
	if Constants.is_headless:
		return
	client.im_server_authoritive = am_i


@rpc("authority", "call_remote", "reliable", Constants.CHANNEL_PLAYER_SYNC)
func set_player_visibility(data) -> void:
	if Constants.is_headless:
		return
	if Constants.is_server:
		return
	#var vis_data = {
	#	"id": player_id,
	#	"v": false
	#}
	var player_id = data["id"]
	var vis = data["v"]

	#print("ClientRPC: ", player_id, " ", vis)
	client.character_visibility[player_id] = vis
	if player_id == multiplayer.get_unique_id():
		#it's me!
		client.my_player_scene.visible = vis


@rpc("reliable", "any_peer", "call_remote", Constants.CHANNEL_ACTIONABLES)
func update_stats():
	if Constants.is_headless:
		return
	if peer == null:
		return
	print("update stats received!")
	ClientBackendManager.send_amiban_request()


@rpc("any_peer", "call_remote", "reliable", Constants.CHANNEL_PRIVATE_CHAT)
func kick():#I Kicked From Server
	var freeride = Constants.client.game_scene
	if NativeMarket.is_purchasing:
		await Constants.wait_timer(10)
		Selector.need_to_sync = true
		freeride.on_quit_pressed()
		return
	Selector.need_to_sync = true
	freeride.on_quit_pressed()


@rpc("any_peer", "call_remote", "reliable", Constants.CHANNEL_PRIVATE_CHAT)
func kick_server_full():#I Kicked From Server
	Selector.need_to_sync = true
	var freeride = Constants.client.game_scene
	freeride.on_quit_pressed(true)#Go to server selector
	Constants.show_toast(tr("SERVER_FULL"))


@rpc("authority", "call_remote", "reliable", Constants.CHANNEL_GUN)
func damage(_damage, damager_id):#Damage From baton or Pistol
	if Selector.im_in_game:
		return

	EmoteManager.show_emote(EmoteManager.Damage)
	client.my_player_scene.statManager.reset_alarm()
	MobileManager.hud.start_hide()
	if client.my_player_scene.ami_damageable():
		Selector.my_hunger -= _damage
		if Selector.my_hunger <= 0:
			client.my_player_scene.im_dead()
			VehicleManager.on_unmount_vehicle_pressed()
			i_kill_success.rpc_id(damager_id, DataSaver.get_item("id", -1))
	
	i_damaged.emit()


@rpc("any_peer", "call_remote", "reliable", Constants.CHANNEL_GUN)
func snowball_hit(_damage):#Snowball hit
	EmoteManager.show_emote(EmoteManager.Frozen)
	client.my_player_scene.statManager.reset_alarm()
	if client.my_player_scene.ami_damageable():
		Selector.my_warmth -= _damage
	Constants.exit_mobile_state()
	MobileManager.hud.start_hide()


#Client
@rpc("reliable", "authority", "call_remote", Constants.CHANNEL_POLICE)
func arrest_me():
	var player = client.my_player_scene
	Selector.in_arresting = true
	player.freezeState.start_state(player.animation_prefix + "Praying", 2.0, "on_arrest_finished")
	EmoteManager.show_emote(EmoteManager.HandCuffs)


#Client
@rpc("reliable", "authority", "call_remote", Constants.CHANNEL_POLICE)
func i_arrest_success(guilty_id):#Iam Police and arrest success
	ClientBackendManager.send_success_arrest_request(guilty_id)
	var data = {
		"arrest": 1
	}
	JobManager.job_done(data)


#Client
@rpc("reliable", "authority", "call_remote", Constants.CHANNEL_POLICE)
func i_heal_success(_patient_id):#Iam Doctor and heal success with syringe
	print("i heal success")
	var player = client.my_player_scene
	player.controller.drug_parent.visible = false
	InventoryManager.remove_item_in_hand()
	DataSaver.send_save_request()
	Selector.last_syringe_use = Time.get_ticks_msec()


#Client
@rpc("reliable", "authority", "call_remote", Constants.CHANNEL_POLICE)
func heal_me():#Doctor Unfreeze player
	var player = client.my_player_scene
	player.freezeState.start_state(player.animation_prefix + "Praying", 2.0, "on_heal_finished")
	EmoteManager.show_emote(EmoteManager.Strong)


#Client
@rpc("reliable", "any_peer", "call_remote", Constants.CHANNEL_POLICE)
func i_kill_success(dead_id):#I Killed someone
	ClientBackendManager.send_kill_request(dead_id)
	var data = {
		"policeKill": 1
	}
	JobManager.job_done(data)


#Client
@rpc("reliable", "authority", "call_remote", Constants.CHANNEL_POLICE)
func send_me_to_jail(jail_position):#I Killed someone
	Selector.in_arresting = false
	client.my_player_scene.global_position = jail_position
	client.my_player_scene.im_alive()
	ClientBackendManager.send_amiban_request()
	client.game_scene.player_inventory_container.visible = false
	InventoryManager.on_jail()


@rpc("authority", "call_remote", "unreliable_ordered", Constants.CHANNEL_PLAYER_GENERAL)
func syncv2_players_general(buffer: PackedByteArray):
	if Constants.is_headless:
		return
	if peer == null:
		return
	
	client.clientSyncV2.on_general_data_received(buffer)


@rpc("authority", "call_remote", "unreliable_ordered", Constants.CHANNEL_PLAYER_INSIGHT)
func syncv2_players_insight(buffer: PackedByteArray):
	if Constants.is_headless:
		return
	if peer == null:
		return
	
	client.clientSyncV2.on_insight_data_received(buffer)


@rpc("authority", "call_remote", "unreliable_ordered", Constants.CHANNEL_PLAYER_GENERAL)
func syncv2_players_list_data(buffer: PackedByteArray):
	if Constants.is_headless:
		return
	if peer == null:
		return
	
	client.clientSyncV2.on_list_data_received(buffer)


func in_between_stages():
	if peer == null:
		return false

	if client == null:
		return true
	if client.game_scene == null:
		return true
	return false


#Use in map finished rpc
func is_qualified(id):
	if peer == null:
		return false
	return StageManager.qualifed_list.find(id) != -1
