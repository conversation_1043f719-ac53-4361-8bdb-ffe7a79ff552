[gd_scene load_steps=5 format=4 uid="uid://mxley74wyhax"]

[ext_resource type="Material" uid="uid://lhnii45g7kv1" path="res://prophaunt/Mat/DarkLightBrown.tres" id="1_7vmvu"]

[sub_resource type="ArrayMesh" id="ArrayMesh_x0wf1"]
_surfaces = [{
"aabb": AABB(-1, -0.1, -1, 2, 0.1, 2),
"format": 34896613377,
"index_count": 60,
"index_data": PackedByteArray("AAAKAAQAAAAIAAoAAgAGAAUAAgADAAYABAABAAAABAAHAAEAAgABAAMAAgAAAAEABgAEAAUABgAHAAQABwADAAEABwAGAAMACwAIAAkACwAKAAgABQAJAAIABQALAAkABAALAAUABAAKAAsAAgAIAAAAAgAJAAgA"),
"name": "Wall.001",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 12,
"vertex_data": PackedByteArray("//+mzAAAAAD//wAAAAAAAP//p8z//wAA//8AAP//AAAAAKbMAAAAAAAAp8z//wAAAAAAAP//AAAAAAAAAAAAACz8/v/SAwAALPz//yz8AADSA/7/0gMAANID//8s/AAA")
}]
blend_shape_mode = 0

[sub_resource type="ArrayMesh" id="ArrayMesh_spjdl"]
resource_name = "MosaicUV_FloorFull_001"
_surfaces = [{
"aabb": AABB(-1, -0.1, -1, 2, 0.1, 2),
"attribute_data": PackedByteArray("hHOq+IRzqviEc6r4hHOq+AluXvMJbl7zCW5e80dz6IxHc+iMR3PojEdz6IwlbjOSJW4zkiVuM5JaCKr4Wgiq+FoIqvhaCKr4HQjojB0I6IwdCOiMHQjojJgNM5KYDTOSmA0zknwNXvN8DV7zfA1e8yx6jPwWiIH7LHqM/Cx60YQWiGaDLHrRhJECjPwT+IH7kQKM/JEC0YQT+GaDkQLRhA=="),
"format": 34896613399,
"index_count": 60,
"index_data": PackedByteArray("AQAiAA8AAQAcACIABwAWABIABwALABYADgAEAAAADgAZAAQACgAGAA0ACgADAAYAGAARABUAGAAbABEAGgAMAAUAGgAXAAwAJgAdACAAJgAjAB0AEwAfAAgAEwAlAB8AEAAnABQAEAAkACcACQAeAAIACQAhAB4A"),
"material": ExtResource("1_7vmvu"),
"name": "Wall.001",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 40,
"vertex_data": PackedByteArray("//+mzAAA/////6bMAAAX2P//pswAALbt//+mzAAAg+z//wAAAAD/////AAAAAP+///8AAAAAzuv//6fM//8AgP//p8z//+en//+nzP//b83//6fM//9DxP//AAD//wCA//8AAP///7///wAA///zwwAApswAAP//AACmzAAAF9gAAKbMAADm7gAApswAADTtAACnzP//AIAAAKfM///npwAAp8z//8HMAACnzP//k8QAAAAA//8AgAAAAAD///+/AAAAAP//6MQAAAAAAAD//wAAAAAAAP+/AAAAAAAA6u0s/P7/0gMX2Cz8/v/SA///LPz+/9ID1e0s/P//LPznpyz8//8s/P//LPz//yz8NM3SA/7/0gMX2NID/v/SA///0gP+/9IDku7SA///LPznp9ID//8s/P//0gP//yz818z///9/////fwKrVaFQE1c2////fwAA/3/5EwM2/3///////38ewBIZC0z6Gf9///8AAP9/V01TGf///3////9/590eKlVJq5L/f///////fziamL9oZdDK/3///wAA/3/KZJTJ////fwAA/38ASQGS////f/9//7/qqmmh////f/9//782wHUZ////f/9//78c3l0q////f/9//78SmqG/")
}]
blend_shape_mode = 0
shadow_mesh = SubResource("ArrayMesh_x0wf1")

[sub_resource type="BoxShape3D" id="BoxShape3D_82jpa"]
size = Vector3(2, 0.1, 2)

[node name="Mosaic" type="MeshInstance3D" groups=["VisibleGroup0"]]
mesh = SubResource("ArrayMesh_spjdl")
skeleton = NodePath("")

[node name="StaticBody3D" type="StaticBody3D" parent="."]
collision_layer = 2
collision_mask = 0

[node name="CollisionShape3D" type="CollisionShape3D" parent="StaticBody3D"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 2.98023e-08, -0.05, 0)
shape = SubResource("BoxShape3D_82jpa")
