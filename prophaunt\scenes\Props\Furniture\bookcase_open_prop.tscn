[gd_scene load_steps=4 format=3 uid="uid://pehm3apge633"]

[ext_resource type="PackedScene" uid="uid://cy82bsxfj1ddv" path="res://prophaunt/scenes/PropObject.tscn" id="1_23dg4"]
[ext_resource type="PackedScene" uid="uid://da5oleoa8chky" path="res://prophaunt/maps/Source/Furniture/bookcase_open.tscn" id="2_gh8iy"]

[sub_resource type="BoxShape3D" id="BoxShape3D_n2ccm"]
size = Vector3(1.23079, 2.56427, 0.759491)

[node name="BookcaseOpenProp" instance=ExtResource("1_23dg4")]

[node name="BookcaseOpen" parent="Meshes" index="0" instance=ExtResource("2_gh8iy")]
transform = Transform3D(0.06, 0, 0, 0, 0.06, 0, 0, 0, 0.06, 0, 0, 0)

[node name="CollisionShape3D" type="CollisionShape3D" parent="StaticBody3D" index="0"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0.00249478, 1.285, 0.00761414)
shape = SubResource("BoxShape3D_n2ccm")
