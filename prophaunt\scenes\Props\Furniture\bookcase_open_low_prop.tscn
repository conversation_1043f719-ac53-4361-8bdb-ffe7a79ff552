[gd_scene load_steps=4 format=3 uid="uid://bmake0ocvvfv2"]

[ext_resource type="PackedScene" uid="uid://cy82bsxfj1ddv" path="res://prophaunt/scenes/PropObject.tscn" id="1_r05sp"]
[ext_resource type="PackedScene" uid="uid://c70xipesshv5l" path="res://prophaunt/maps/Source/Furniture/bookcase_open_low.tscn" id="2_qe0xs"]

[sub_resource type="BoxShape3D" id="BoxShape3D_n2ccm"]
size = Vector3(1.23079, 1.22061, 0.759491)

[node name="BookcaseOpenLowProp" instance=ExtResource("1_r05sp")]

[node name="BookcaseOpenLow" parent="Meshes" index="0" instance=ExtResource("2_qe0xs")]
transform = Transform3D(3, 0, 0, 0, -1.31134e-07, -3, 0, 3, -1.31134e-07, 0, 0, 0)

[node name="CollisionShape3D" type="CollisionShape3D" parent="StaticBody3D" index="0"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0.00249478, 0.613171, 0.00761414)
shape = SubResource("BoxShape3D_n2ccm")
