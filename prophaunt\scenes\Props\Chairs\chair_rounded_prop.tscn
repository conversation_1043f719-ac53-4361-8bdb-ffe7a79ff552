[gd_scene load_steps=4 format=3 uid="uid://cmyjgelirlbr7"]

[ext_resource type="PackedScene" uid="uid://cy82bsxfj1ddv" path="res://prophaunt/scenes/PropObject.tscn" id="1_ulw7h"]
[ext_resource type="PackedScene" uid="uid://6walmtvy7ei6" path="res://prophaunt/maps/Source/Chairs/chair_rounded.tscn" id="2_afti3"]

[sub_resource type="BoxShape3D" id="BoxShape3D_vj1wa"]
size = Vector3(0.817505, 1.83445, 0.814911)

[node name="ChairRoundedProp" instance=ExtResource("1_ulw7h")]

[node name="ChairRounded" parent="Meshes" index="0" instance=ExtResource("2_afti3")]

[node name="CollisionShape3D" type="CollisionShape3D" parent="StaticBody3D" index="0"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -0.00445554, 0.917194, -0.000320464)
shape = SubResource("BoxShape3D_vj1wa")
