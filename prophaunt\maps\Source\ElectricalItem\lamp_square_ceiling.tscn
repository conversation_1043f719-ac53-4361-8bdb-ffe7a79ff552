[gd_scene load_steps=4 format=4 uid="uid://cnbk5ocbt6570"]

[ext_resource type="Material" uid="uid://b6gd8jjb1kopc" path="res://prophaunt/Mat/Colormap2.tres" id="1_r4pjo"]

[sub_resource type="ArrayMesh" id="ArrayMesh_xr8b7"]
_surfaces = [{
"aabb": AABB(-0.18, -0.689964, -0.18, 0.36, 0.69, 0.36),
"format": 34359742465,
"index_count": 180,
"index_data": PackedByteArray("AQACAAAAAgABAAMABAACAAMAAgAEAAUAAQAHAAYABwABAAAABwAJAAgACQAHAAoACgAHAAAACAAFAAcABQAIAAsABQALAAIAAgALAAoADAAIAAkADAAKAA4ACgAMAAkADwAIAA0ACAAPAAsABAANAAwADQAEAA8ADwAEAAMADwADAAEADAAGAAQABgAMAA4ACgAPAA4ADwAKAAsABAAHAAUAEQASABAAEgARABMAEQAUABMAFgARABAAHAAZAB0AEwAYABsAEwAbABIAFgAUABUAFAAWAB8AGQAeAB0AHAAeABsAHgAcAB0AEgAWABAAFgASAB8ACgAAAAIACAAMAA0ABgAOAAEAAQAOAA8ABwAEAAYAFAARABUAEQAWABUAGAAZABcAGQAYABoAGAAcABsAHAAYABcAGQAcABcAFAAaABgAGgAUAB4AHgAUAB8AHgAfABIAGAATABQAEgAbAB4AHgAZABoA"),
"name": "Material",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 32,
"vertex_data": PackedByteArray("DLrwvSy5db4WuvC9DLrwvX6hML8auvC9DLrwvSy5db4EuvA9DLrwvX6hML8CuvA9D7rwPX6hML8CuvA9D7rwPSy5db4EuvA9D7rwPX6hML8auvC9D7rwPSy5db4WuvC97FE4Piy5db7nUTg+7FE4Piy5db7wUTi+6lE4viy5db7wUTi+6lE4viy5db7nUTg+7FE4Pn6hML/xUTi+7FE4Pn6hML/lUTg+6lE4vn6hML/xUTi+6lE4vn6hML/lUTg+TPvevEtuHb9++968WPvePEtuHb9++968TPvevAyddb1w+968WPvePAyddb1w+968WPvePAyddb00+948WPvePEtuHb8m+948TPvevEtuHb8m+948D7rwPYQ1FjgVuvC9D7rwPQqddb0VuvC9D7rwPXwqFjgGuvA9D7rwPQ6ddb0GuvA9DLrwvQqddb0VuvC9DLrwvYQ1FjgVuvC9DLrwvXwqFjgGuvA9DLrwvQ6ddb0GuvA9TPvevAyddb00+948")
}]
blend_shape_mode = 0

[sub_resource type="ArrayMesh" id="ArrayMesh_4aunc"]
resource_name = "LampSquareCeiling_lampSquareCeiling"
_surfaces = [{
"aabb": AABB(-0.18, -0.689964, -0.18, 0.36, 0.69, 0.36),
"attribute_data": PackedByteArray("8nVHP+gVrT7ydUc/6BWtPvJ1Rz/oFa0+8nVHP+gVrT7ydUc/6BWtPvJ1Rz/oFa0+8nVHP+gVrT7ydUc/6BWtPvJ1Rz/oFa0+8nVHP+gVrT7ydUc/6BWtPvJ1Rz/oFa0+8nVHP+gVrT7ydUc/6BWtPvJ1Rz/oFa0+8nVHP+gVrT7ydUc/6BWtPvJ1Rz/oFa0+8nVHP+gVrT7ydUc/6BWtPvJ1Rz/oFa0+8nVHP+gVrT7ydUc/6BWtPvJ1Rz/oFa0+8nVHP+gVrT7ydUc/6BWtPvJ1Rz/oFa0+8nVHP+gVrT7ydUc/6BWtPvJ1Rz/oFa0+8nVHP+gVrT7ydUc/6BWtPvJ1Rz/oFa0+8nVHP+gVrT7ydUc/6BWtPvJ1Rz/oFa0+8nVHP+gVrT7ydUc/6BWtPvJ1Rz/oFa0+8nVHP+gVrT7ydUc/6BWtPvJ1Rz/oFa0+8nVHP+gVrT7ydUc/6BWtPvJ1Rz/oFa0+8nVHP+gVrT7ydUc/6BWtPvJ1Rz/oFa0+q2xOP5CVJz2rbE4/kJUnPatsTj+QlSc9q2xOP5CVJz2rbE4/kJUnPatsTj+QlSc9q2xOP5CVJz2rbE4/kJUnPatsTj+QlSc9q2xOP5CVJz2rbE4/kJUnPatsTj+QlSc9q2xOP5CVJz2rbE4/kJUnPatsTj+QlSc9q2xOP5CVJz2rbE4/kJUnPatsTj+QlSc9q2xOP5CVJz2rbE4/kJUnPatsTj+QlSc9q2xOP5CVJz2rbE4/kJUnPatsTj+QlSc9q2xOP5CVJz2rbE4/kJUnPatsTj+QlSc9q2xOP5CVJz2rbE4/kJUnPatsTj+QlSc9q2xOP5CVJz2rbE4/kJUnPatsTj+QlSc9q2xOP5CVJz2rbE4/kJUnPatsTj+QlSc9q2xOP5CVJz2rbE4/kJUnPatsTj+QlSc9q2xOP5CVJz2rbE4/kJUnPatsTj+QlSc9q2xOP5CVJz2rbE4/kJUnPatsTj+QlSc9q2xOP5CVJz2rbE4/kJUnPatsTj+QlSc9"),
"format": 34359742487,
"index_count": 180,
"index_data": PackedByteArray("BQAIAAIACAAFAAsADAAGAAkABgAMAA8AAwAVABIAFQADAAAAFgAcABkAHAAWAB8AHwAWAAEAGQAQABYAEAAZACIAEAAiAAcABwAiAB8AJgAaAB0AJAAeACoAHgAkABsALQAYACcAGAAtACEADQAoACUAKAANAC4ALgANAAoALgAKAAQAJQATAA0AEwAlACsAIAAvACwALwAgACMADgAXABEAMwA2ADAANgAzADkANQA+ADsAQwA0ADEAVQBMAFgAOgBJAFIAOgBSADcAQgA8AD8APABCAF0ASwBaAFcAVgBcAFMAXABWAFkAOABEADIARAA4AF8AHwABAAcAGgAmACkAEwArAAQABAArAC4AFwAOABQAPgA1AEEANABDAEAASgBNAEcATQBKAFAASABUAFEAVABIAEUATABVAEYAPQBPAEkATwA9AFsAWwA9AF4AWwBeADcASQA6AD0ANwBSAFsAWgBLAE4A"),
"material": ExtResource("1_r4pjo"),
"name": "Material",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 96,
"vertex_data": PackedByteArray("DLrwvSy5db4WuvC9DLrwvSy5db4WuvC9DLrwvSy5db4WuvC9DLrwvX6hML8auvC9DLrwvX6hML8auvC9DLrwvX6hML8auvC9DLrwvSy5db4EuvA9DLrwvSy5db4EuvA9DLrwvSy5db4EuvA9DLrwvX6hML8CuvA9DLrwvX6hML8CuvA9DLrwvX6hML8CuvA9D7rwPX6hML8CuvA9D7rwPX6hML8CuvA9D7rwPX6hML8CuvA9D7rwPSy5db4EuvA9D7rwPSy5db4EuvA9D7rwPSy5db4EuvA9D7rwPX6hML8auvC9D7rwPX6hML8auvC9D7rwPX6hML8auvC9D7rwPSy5db4WuvC9D7rwPSy5db4WuvC9D7rwPSy5db4WuvC97FE4Piy5db7nUTg+7FE4Piy5db7nUTg+7FE4Piy5db7nUTg+7FE4Piy5db7wUTi+7FE4Piy5db7wUTi+7FE4Piy5db7wUTi+6lE4viy5db7wUTi+6lE4viy5db7wUTi+6lE4viy5db7wUTi+6lE4viy5db7nUTg+6lE4viy5db7nUTg+6lE4viy5db7nUTg+7FE4Pn6hML/xUTi+7FE4Pn6hML/xUTi+7FE4Pn6hML/xUTi+7FE4Pn6hML/lUTg+7FE4Pn6hML/lUTg+7FE4Pn6hML/lUTg+6lE4vn6hML/xUTi+6lE4vn6hML/xUTi+6lE4vn6hML/xUTi+6lE4vn6hML/lUTg+6lE4vn6hML/lUTg+6lE4vn6hML/lUTg+TPvevEtuHb9++968TPvevEtuHb9++968TPvevEtuHb9++968WPvePEtuHb9++968WPvePEtuHb9++968WPvePEtuHb9++968TPvevAyddb1w+968TPvevAyddb1w+968TPvevAyddb1w+968WPvePAyddb1w+968WPvePAyddb1w+968WPvePAyddb1w+968WPvePAyddb00+948WPvePAyddb00+948WPvePAyddb00+948WPvePEtuHb8m+948WPvePEtuHb8m+948WPvePEtuHb8m+948TPvevEtuHb8m+948TPvevEtuHb8m+948TPvevEtuHb8m+948D7rwPYQ1FjgVuvC9D7rwPYQ1FjgVuvC9D7rwPYQ1FjgVuvC9D7rwPQqddb0VuvC9D7rwPQqddb0VuvC9D7rwPQqddb0VuvC9D7rwPXwqFjgGuvA9D7rwPXwqFjgGuvA9D7rwPXwqFjgGuvA9D7rwPQ6ddb0GuvA9D7rwPQ6ddb0GuvA9D7rwPQ6ddb0GuvA9DLrwvQqddb0VuvC9DLrwvQqddb0VuvC9DLrwvQqddb0VuvC9DLrwvYQ1FjgVuvC9DLrwvYQ1FjgVuvC9DLrwvYQ1FjgVuvC9DLrwvXwqFjgGuvA9DLrwvXwqFjgGuvA9DLrwvXwqFjgGuvA9DLrwvQ6ddb0GuvA9DLrwvQ6ddb0GuvA9DLrwvQ6ddb0GuvA9TPvevAyddb00+948TPvevAyddb00+948TPvevAyddb00+948/3//f////z//f///////v////3////+//3//f////z//fwAA////v////3////+//////////7//f///////v////3////+//////////7//fwAA////v////3////+//////////7//fwAA////vwAA/3////+//////////7//f///////vwAA/3////+//3//f////z//fwAA////vwAA/3////+//3//f////z//f///////vwAA/3////+//3//f////z//f///////v////3////+//////////7//f///////v////3////+//////////7//f///////vwAA/3////+//3//f////z//f///////vwAA/3////+//////////7//fwAA////v////3////+//3//f////z//fwAA////v////3////+//////////7//fwAA////vwAA/3////+//3//f////z//fwAA////vwAA/3////+//////////7//fwAA////vwAA/3////+//////////7//fwAA////v////3////+//////////7//fwAA////vwAA/3////+//////////7//fwAA////v////3////+//3//f////z//fwAA////v////3////+//3//f////z//fwAA////v////3////+//3//f////z//fwAA////vwAA/3////+//////////7//f///////v////3////+//////////7//fwAA////v////3////+//3//f////z//f///////v////3////+//3//f////z//fwAA////v////3////+//////////7//fwAA////vwAA/3////+//////////7//f///////vwAA/3////+//3//f////z//f///////vwAA/3////+//3//f////z//fwAA////vwAA/3////+//3//f////z//fwAA////vwAA/3////+/")
}]
blend_shape_mode = 0
shadow_mesh = SubResource("ArrayMesh_xr8b7")

[node name="lampSquareCeiling" type="MeshInstance3D"]
mesh = SubResource("ArrayMesh_4aunc")
skeleton = NodePath("")
