[gd_scene load_steps=4 format=3 uid="uid://dnce4onj55k6w"]

[ext_resource type="PackedScene" uid="uid://cy82bsxfj1ddv" path="res://prophaunt/scenes/PropObject.tscn" id="1_cb1ob"]
[ext_resource type="PackedScene" uid="uid://cntwvtstnv46d" path="res://prophaunt/maps/Source/Bathroom/toilet_open.tscn" id="2_f5tn3"]

[sub_resource type="BoxShape3D" id="BoxShape3D_f8ox4"]
size = Vector3(1.08613, 1.51083, 1.39828)

[node name="ToiletOpenProp" instance=ExtResource("1_cb1ob")]

[node name="ToiletOpen" parent="Meshes" index="0" instance=ExtResource("2_f5tn3")]

[node name="CollisionShape3D" type="CollisionShape3D" parent="StaticBody3D" index="0"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0.00755113, 0.755417, 0.151657)
shape = SubResource("BoxShape3D_f8ox4")
