[gd_scene load_steps=5 format=4 uid="uid://e3s6ndkkeix0"]

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_e2mkv"]
resource_name = "Wall.001"
cull_mode = 2
metallic = 0.2

[sub_resource type="ArrayMesh" id="Array<PERSON><PERSON>_batli"]
_surfaces = [{
"aabb": AABB(-2, -1.98498e-07, -2.2, 4, 4, 0.4),
"format": 34896613377,
"index_count": 744,
"index_data": PackedByteArray("DwAtAAgADwApAC0ALAAOACcALAAIAA4AcAAxABIAcAB2ADEAXQArACUAXQBaACsATwAtACkATwBMAC0ASAAOACgASABGAA4AcgANAB8AcgBtAA0AdAALABsAdABqAAsAPQAKAB0APQBBAAoAEQArAAkAEQAlACsATQAuABQATQBTAC4AbgAwABYAbgB3ADAAWwAwABcAWwBhADAAKgAQACMAKgAJABAASgAQACQASgBHABAAPAALABoAPABCAAsACQAkABAACQArACQACAAoAA4ACAAtACgAUwAVAC4AUwBOABUAdwAXADAAdwBvABcAdgATADEAdgBxABMAYQAWADAAYQBcABYAWgAkACsAWgBeACQATAAoAC0ATABQACgARgAnAA4ARgBJACcARwAjABAARwBLACMAQgAbAAsAQgBDABsAbQAeAA0AbQBzAB4AagAaAAsAagB1ABoAQQAcAAoAQQBEABwAWQATAAQAWQBQABMAawATAHEAawAEABMAewABAGgAewA+AAEAWAAjAEsAWABOACMAZQADAF8AZQBAAAMAZwAnAEkAZwBcACcAeAAXAG8AeAA7ABcAZgAZAAcAZgBeABkAYABFAGQAYAACAEUAfQAcAF8AfQB1ABwAfAABAFEAfABoAAEAbAAhAAcAbABzACEAIgAJACoAIgARAAkAJgAIACwAJgAPAAgAXQA6ABgAXQBiADoAWwA3ACYAWwBjADcAegA7AHgAegBAADsATQA1ACIATQBUADUAcAA4AHkAcAASADgATwA4ABIATwBVADgADwA3ADIADwAmADcAEQA1ADMAEQAiADUAJQAzADQAJQARADMAKQAyADYAKQAPADIAaQBAAHoAaQADAEAAYgBFADoAYgBkAEUAYwBEADcAYwBlAEQAVABDADUAVABWAEMAeQA+AHsAeQA4AD4AVQA+ADgAVQBXAD4AMgBEAEEAMgA3AEQAMwBDAEIAMwA1AEMANABCADwANAAzAEIANgBBAD0ANgAyAEEAUgBDAFYAUgAbAEMAUQA+AFcAUQABAD4AbgAGAGwAbgAWAAYABgBJAB4ABgBnAEkABQBLACAABQBYAEsADABLAEcADAAgAEsADQBJAEYADQAeAEkAIQBHAEoAIQAMAEcAHwBGAEgAHwANAEYAHwBZAAQAHwBIAFkAHQBXAD0AHQBRAFcAAABWAD8AAABSAFYANgBXAFUANgA9AFcAOQBWAFQAOQA/AFYAKQBVAE8AKQA2AFUAFABUAE0AFAA5AFQAcgAFACAAcgBrAAUAdABRAB0AdAB8AFEABQBOAFgABQAVAE4ASABQAFkASAAoAFAAMQBQAEwAMQATAFAAKgBOAFMAKgAjAE4AIgBTAE0AIgAqAFMAEgBMAE8AEgAxAEwAIQBmAAcAIQBKAGYAOwBlAGMAOwBAAGUANABkAGIANAA8AGQAFwBjAFsAFwA7AGMAJQBiAF0AJQA0AGIAaQBfAAMAaQB9AF8AGgBkADwAGgBgAGQASgBeAGYASgAkAF4ABgBcAGcABgAWAFwARABfABwARABlAF8ALwBeAFoALwAZAF4ALABcAGEALAAnAFwAJgBhAFsAJgAsAGEAGABaAF0AGAAvAFoAAgB9AGkAAgBgAH0AGwB8AHQAGwBSAHwAHwBrAHIAHwAEAGsAGQBsAAcAGQBuAGwAOQB7AD8AOQB5AHsAAgB6AEUAAgBpAHoAFAB5ADkAFABwAHkARQB4ADoARQB6AHgABgBzAGwABgAeAHMAUgBoAHwAUgAAAGgAYAB1AH0AYAAaAHUAOgBvABgAOgB4AG8APwBoAAAAPwB7AGgABQBxABUABQBrAHEACgB1AGoACgAcAHUADABzAG0ADAAhAHMALgBxAHYALgAVAHEALwBvAHcALwAYAG8AGQB3AG4AGQAvAHcAHQBqAHQAHQAKAGoAIABtAHIAIAAMAG0AFAB2AHAAFAAuAHYA"),
"lods": [0.109058, PackedByteArray("DwAtAAgADwApAC0AJgAPAAgAJgAIACwAKQAPADIAKQAyADYADwAmADcADwA3ADIALAAIAA4ALAAOACcARgAnAA4ARgBJACcACAAoAA4ACAAtACgASAAOACgASABGAA4AXQArACUAXQBaACsATwAtACkATwBMAC0AIAANAB8AIAAeAA0ADAAeACAADAAhAB4AHwBrACAAawBLACAAawBYAEsAHwBZAGsAHwBIAFkAbAAeACEAbABJAB4AbABnAEkAIQBmAGwAIQBKAGYAHwBGAEgAHwANAEYADQBJAEYADQAeAEkAIQBHAEoAIQAMAEcADABLAEcADAAgAEsAEQArAAkACQArACQACQAkABAAEQAlACsAJQARADMAJQAzADQASgAQACQASgBHABAAIgARAAkAIgAJACoAKgAJABAAKgAQACMAEQAiADUAEQA1ADMARwBLACMARwAjABAATQBTABUAFQBUAE0AFQA5AFQAUwBOABUAawAVAE4AawBOAFgAFQB5ADkAFQATAHkAawATABUAEwA4AHkATwBVADgATwA4ABMAWQATAGsAEwBMAE8AWQBQABMAEwBQAEwAWwBhABYAYQBcABYAFgBjAFsAFgA7AGMAeAA7ABYAOgB4ABYAOgAWABgAbAAYABYAXQBiADoAXQA6ABgAGABaAF0AGABeAFoAZgBeAGwAGABsAF4AbAAWAFwAbABcAGcAWgAkACsAWgBeACQATAAoAC0ATABQACgANgA3ABwAMgA3ADYANwBAABwAYwBAADcAOwBAAGMAQAADABwAHAA+ADYAHAABAD4ANgA+AFUAVQA+ADgAewABAGgAewA+AAEAeQA+AHsAeQA4AD4AWAAjAEsAWABOACMAZwAnAEkAZwBcACcAYAACAEUAYgBgAEUAYgBFADoANABgAGIANAAbAGAANAA1ABsANAAzADUAUgAbADUAUgA1AD8AAABSAD8AVAA/ADUAOQA/AFQAfQB0ABwAaQB9ABwAaQAcAAMAdAB8ABwAfAABABwAfABoAAEAWwA3ACYAWwBjADcAegA7AHgAegBAADsAaQBAAHoAaQADAEAATQA1ACIATQBUADUAKQBVAE8AKQA2AFUASABQAFkASAAoAFAAKgBOAFMAKgAjAE4AIgBTAE0AIgAqAFMAJQBiAF0AJQA0AGIASgBeAGYASgAkAF4ALABcAGEALAAnAFwAJgBhAFsAJgAsAGEAAgB9AGkAAgBgAH0AGwB8AHQAGwBSAHwAYAAbAHQAYAB0AH0AOQB7AD8AOQB5AHsAPwB7AGgAPwBoAAAAAgB6AEUAAgBpAHoARQB6AHgARQB4ADoAUgBoAHwAUgAAAGgA")],
"name": "Wall.001",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 126,
"vertex_data": PackedByteArray("//8AAP//AAD//wAAAAAAAAAAAAD//wAAAAAAAAAAAAD/////VhkAAP////+o5gAAAAD//1YZAAAAAP//qOYAAP9//39WGQAA/3//f6jmAAD/fwAAAAAAAP9/AAD//wAA/3///6jmAAD/f///VhkAAP9/joJWGQAA/39wfVYZAAD/f46CqOYAAP9/cH2o5gAA//9wfVYZAAD//46CVhkAAP//cH2o5gAA//+OgqjmAAAAAI6CVhkAAAAAcH1WGQAAAABwfajmAAAAAI6CqOYAAHB9AAD//wAAjoIAAP//AABwfQAAAAAAAI6CAAAAAAAAcH3//1YZAACOgv//VhkAAI6C//+o5gAAcH3//6jmAACOgnB9/v8AAI6CjoL+/wAAcH2Ogv7/AABwfXB9/v8AAHB9cH0AAAAAcH2OggAAAACOgo6CAAAAAI6CcH0AAAAAjoL/f6jmAABwff9/qOYAAHB9/39WGQAAjoL/f1YZAAD///9/qOYAAAAA/3+o5gAAAAD/f1YZAAD///9/VhkAAP9/l1lWGQAA/3+XWajmAABwfZdZ/v8AAI6Cl1n+/wAAjoKXWQAAAABwfZdZAAAAAP//l1lWGQAA//+XWajmAAAAAJdZqOYAAAAAl1lWGQAAcH0JV/7/AACOgglXAAAAAP//CVcAAAAA//8JV/7/AAAAAAlXAAAAAP9/CVcAAAAA/38JV///AACOgglX/v8AAHB9CVcAAAAAAAAJV/7/AAD/f2/9VhkAAP9/b/2o5gAAjoJv/QAAAABwfW/9AAAAAHB9b/3+/wAAjoJv/f7/AABv/f9/VhkAAG/9cH3+/wAAb/2Ogv7/AABv/XB9AAAAAG/9joIAAAAAb/0AAAAAAABv/QAA//8AAG/9/3+o5gAAb/2XWf7/AABv/ZdZAAAAAG/9CVf+/wAAb/0JVwAAAABv/W/9/v8AAG/9b/0AAAAAjwL/f6jmAACPAnB9AAAAAI8CjoIAAAAAjwJwff7/AACPAo6C/v8AAI8CAAAAAAAAjwIAAP//AACPAv9/VhkAAI8Cl1n+/wAAjwKXWQAAAACPAglX/v8AAI8CCVcAAAAAjwJv/f7/AACPAm/9AAAAAP//AAD/fwAAAAAAAP9/AAD/fwAA/38AAP//////fwAAAAD///9/AAD/f////38AAAAAjoL/fwAAAABwff9/AAD//3B9/38AAP//joL/fwAAjoL///9/AABwff///38AAI6CAAD/fwAAcH0AAP9/AAD///9//38AAAAA/3//fwAAAACXWf9/AAD//5dZ/38AAAAACVf/fwAA//8JV/9/AABv/QAA/38AAI8CAAD/fwAA")
}]
blend_shape_mode = 0

[sub_resource type="ArrayMesh" id="ArrayMesh_v86hy"]
resource_name = "WallCorniceUV_WallCorniceUV_001"
_surfaces = [{
"aabb": AABB(-2, -1.98498e-07, -2.2, 4, 4, 0.4),
"attribute_data": PackedByteArray("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"),
"format": 34896613399,
"index_count": 744,
"index_data": PackedByteArray("NACYAB4ANACIAJgAlQAuAIAAlQAdAC4AbwGlAEAAbwF3AaUAPAGOAHcAPAEvAY4ADQGWAIcADQEAAZYA8wAxAIUA8wDrADEAcQErAGIAcQFsASsAcwEnAFkAcwFmAScA0AAkAFwA0ADbACQAOgCQACAAOgB4AJAABgGbAEQABgEcAZsAbQGiAEwAbQF4AaIANQGgAE0ANQFKAaAAjQA4AHAAjQAjADgA+wA3AHUA+wDtADcAzgAmAFYAzgDeACYAIQB0ADYAIQCRAHQAHwCEADAAHwCZAIQAGwFHAJoAGwEKAUcAeAFPAKIAeAFuAU8AdwFDAKUAdwFwAUMASwFKAKEASwE5AUoAMAFzAI8AMAFAAXMAAQGDAJcAAQERAYMA6QCBAC8A6QD3AIEA7wBxADkA7wD/AHEA3gBYACYA3gDhAFgAbAFfACsAbAFyAV8AZgFXACcAZgF1AVcA2wBaACQA2wDjAFoALgFCAA4ALgETAUIAaQFDAHABaQEPAEMAggEFAGEBggHUAAUAKQFuAPwAKQEIAW4AVgEJAEMBVgHYAAkAWwF+APQAWwE3AX4AeQFPAG4BeQHMAE8AWgFUABoAWgFCAVQARQHlAFQBRQEGAOUAhwFbAEQBhwF2AVsAhAEEABUBhAFfAQQAagFoABkAagFyAWgAbAAiAIwAbAA8ACIAfAAcAJQAfAAyABwAPgHHAFEAPgFPAccAMwG6AHoAMwFQAboAgAHNAHsBgAHaAM0ABAGzAGoABAEeAbMAbwHAAHwBbwFAAMAADwG/AD8ADwEkAb8AMwC9AKcAMwB9AL0APQC1AK0APQBtALUAeQCrALEAeQA7AKsAiQCpALkAiQA1AKkAZQHaAIABZQELANoATgHmAMYATgFVAeYAUQHjALsAUQFWAeMAHQHhALIAHQElAeEAfgHUAIIBfgHBANQAIwHTAL4AIwEoAdMApgDkANwApgC8AOQArADiAOAArAC0AOIAsADfAM8AsACqAN8AuADdANEAuACoAN0AFgHhACUBFgFYAOEAFAHSACcBFAEDANIAbQEXAGsBbQFMABcAFAD1AF4AFABcAfUAEAD9AGQAEAAqAf0AKgD+AO4AKgBmAP4ALAD2AOgALABgAPYAaQDsAPoAaQApAOwAYwDqAPIAYwAtAOoAYQAtAQwAYQDxAC0BXAAnAdAAXAAUAScBAAAlAdUAAAAWASUBtwAnASIBtwDQACcBwgAmAR8BwgDWACYBhgAhAQwBhgC2ACEBRQAgAQcBRQDDACABcQERAGUAcQFoAREAdAEVAV0AdAGEARUBEgALASsBEgBIAAsB8AAQASwB8ACCABABpAASAQMBpABBABIBiwAJARoBiwBvAAkBawAZAQUBawCKABkBPgACAQ4BPgCjAAIBZwBZARgAZwD5AFkBygBXAVIBygDZAFcBrgBUAUwBrgDOAFQBTgBTATYBTgDLAFMBdgBNATsBdgCvAE0BYgFEAQoAYgGHAUQBVgBUAc4AVgBFAVQB+AA/AVgB+AByAD8BFgA6AV0BFgBLADoB4wBDAVoA4wBWAUMBnQBBATEBnQBTAEEBkwA4AUkBkwB/ADgBewBIATQBewCSAEgBUAAyAT0BUACeADIBBwCIAWMBBwBHAYgBWQCDAXMBWQAXAYMBYgBoAXEBYgANAGgBVQBrARsAVQBtAWsBxQCBAdcAxQB9AYEBCAB/AecACABkAX8BRgB8AcQARgBvAXwB5wB6AckA5wB/AXoBFQByAWoBFQBfAHIBGAFeAYUBGAEBAF4BRgF1AYYBRgFXAHUByABuAVIAyAB5AW4B1wBgAQIA1wCBAWABEwBwAUkAEwBpAXABJQB2AWcBJQBbAHYBKAByAWwBKABoAHIBnABwAXcBnABJAHABnwBuAXgBnwBSAG4BVQB4AW0BVQCfAHgBXQBnAXQBXQAlAGcBZQBsAXEBZQAoAGwBRgB3AW8BRgCcAHcB"),
"lods": [0.109058, PackedByteArray("kAGmAYkBkAGIAKYBfACQAYkBfACJAaUBiAA1AKkAiACpALkAkQF8AL0AkQG9AKcApQGKAY8BpQGPAYAA6QCAAI8B6QD3AIAAHwCEAI8BHwCmAYQA8wAxAIQA8wDrADEAPAGOAHcAPAEvAY4ADQGWAIcADQEAAZYAnwErAJ0BnwGbASsAKACbAZ8BKAChAZsBnQFoAZ8BaAGwAZ8BaAG1AbABnQEtAWgBnQHxAC0BagGbAaEBagGuAZsBagG9Aa4BoQFZAWoBoQH5AFkBngGsAfEAngGOAawBjgGvAawBjgGcAa8BogGtAfkAogGNAa0BjQGxAa0BjQGgAbEBlAGkAYsBiwGkAXQAiwF0AJIBlAF4AKQBeACUAasAeACrALEA+wCTAXQA+wDtAJMBbACUAYwBbACMAaMBowEjAJIBowGSAXAAPQBsALUAPQC1AK0A7wD/AHAA7wBwADkABgG0AZYBlgEgAQYBlgGpASABtAGzAZYBvwGXAbMBvwGzAbYBSQB8AakBSQBDAHwBaQFDAEkAQwCoAXwBDgEkAagBDgGoAZUBtwGVAb8BlQGyAQ4BtwESAZUBlQESAbIBNQG7AUoAuwE5AZgBmAFTATUBmAGrAVMBeQGrAUwAqgF5AUwAqgFMAFIAawFSAEwAPQFPAaoBPQGqAZkBmQG4AT0BmQG6AbgBvAG6AcABmgHAAboBwAGYAbkBwAG5Ab4BMAFzAI8AMAFAAXMAAQGDAJcAAQERAYMAtwC7AFoApwG7ALcAuwDYAFoAUQHYALsAygDYAFEB2AAJAFoAWgDSALcAWgADANIAtwDSACIBIgHSAL4AggEFAGEBggHUAAUAfgHUAIIBfgHBANQAKQFuAPwAKQEIAW4AWwF+APQAWwE3AX4ARQEGAOUATAFFAeUATAHlAMYArgBFAUwBrgBYAEUBrgCyAFgArgCqALIAFgFYALIAFgGyANUAAAAWAdUAHQHVALIAwgDVAB0BhwF0AVsAYgGHAVsAYgFbAAoAdAGEAVsAhAEEAFsAhAFfAQQAMwG6AHoAMwFQAboAgAHNAHsBgAHaAM0AZQHaAIABZQELANoABAGzAGoABAEeAbMAhgAhAQwBhgC2ACEB8AAQASwB8ACCABABiwAJARoBiwBvAAkBawAZAQUBawCKABkBdgBNATsBdgCvAE0B+AA/AVgB+AByAD8BkwA4AUkBkwB/ADgBewBIATQBewCSAEgBBwCIAWMBBwBHAYgBWQCDAXMBWQAXAYMBRgFZAHMBRgFzAYYBxQCBAdcAxQB9AYEB1wCBAWAB1wBgAQIACAB/AecACABkAX8B5wB/AXoB5wB6AckAGAFeAYUBGAEBAF4B")],
"material": SubResource("StandardMaterial3D_e2mkv"),
"name": "Wall.001",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 449,
"vertex_data": PackedByteArray("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")
}]
blend_shape_mode = 0
shadow_mesh = SubResource("ArrayMesh_batli")

[sub_resource type="BoxShape3D" id="BoxShape3D_j8rqw"]
size = Vector3(4, 4, 0.4)

[node name="WallCornice" type="MeshInstance3D" groups=["VisibleGroup0"]]
mesh = SubResource("ArrayMesh_v86hy")
skeleton = NodePath("")

[node name="StaticBody3D" type="StaticBody3D" parent="."]
collision_layer = 2

[node name="CollisionShape3D" type="CollisionShape3D" parent="StaticBody3D"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 2, -2)
shape = SubResource("BoxShape3D_j8rqw")
