[gd_scene load_steps=4 format=3 uid="uid://chn2r7he14ma0"]

[ext_resource type="PackedScene" uid="uid://cy82bsxfj1ddv" path="res://prophaunt/scenes/PropObject.tscn" id="1_op70e"]
[ext_resource type="PackedScene" uid="uid://j1sl62tsafm0" path="res://prophaunt/maps/Source/ArenaProp/weapon_sword.tscn" id="2_y5i7v"]

[sub_resource type="CapsuleShape3D" id="CapsuleShape3D_5q4rx"]
radius = 0.4
height = 1.8

[node name="WeaponSwordProp" instance=ExtResource("1_op70e")]

[node name="WeaponSword" parent="Meshes" index="0" instance=ExtResource("2_y5i7v")]

[node name="CollisionShape3D" type="CollisionShape3D" parent="StaticBody3D" index="0"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0.9, -0.010301)
shape = SubResource("CapsuleShape3D_5q4rx")
