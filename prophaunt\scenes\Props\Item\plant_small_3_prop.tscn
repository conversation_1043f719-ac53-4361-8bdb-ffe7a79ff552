[gd_scene load_steps=4 format=3 uid="uid://jkoxibubs50i"]

[ext_resource type="Script" path="res://prophaunt/scenes/PropObject.gd" id="1_bv47s"]
[ext_resource type="PackedScene" uid="uid://ubwmwbhxagk5" path="res://prophaunt/maps/Source/Item/plant_small_3.tscn" id="2_k8647"]

[sub_resource type="CylinderShape3D" id="CylinderShape3D_tkk3a"]
height = 0.430454
radius = 0.153564

[node name="PlantSmall3Prop" type="Node3D" node_paths=PackedStringArray("export_mesh") groups=["PropObject"]]
script = ExtResource("1_bv47s")
export_mesh = NodePath("Meshes")

[node name="Meshes" type="Node3D" parent="."]

[node name="PlantSmall3" parent="Meshes" instance=ExtResource("2_k8647")]

[node name="StaticBody3D" type="StaticBody3D" parent="."]
collision_layer = 2

[node name="CollisionShape3D" type="CollisionShape3D" parent="StaticBody3D"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0.213872, 0)
shape = SubResource("CylinderShape3D_tkk3a")
