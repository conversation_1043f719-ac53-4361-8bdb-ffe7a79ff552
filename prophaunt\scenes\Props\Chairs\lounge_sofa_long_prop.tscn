[gd_scene load_steps=5 format=3 uid="uid://b6yvjsxk38n1u"]

[ext_resource type="PackedScene" uid="uid://cy82bsxfj1ddv" path="res://prophaunt/scenes/PropObject.tscn" id="1_1yinf"]
[ext_resource type="PackedScene" uid="uid://2mlyvmwmt5wx" path="res://prophaunt/maps/Source/Furniture/lounge_sofa_long.tscn" id="2_opxqy"]

[sub_resource type="BoxShape3D" id="BoxShape3D_f8ox4"]
size = Vector3(3.897, 1.85541, 1.59935)

[sub_resource type="BoxShape3D" id="BoxShape3D_vsxbc"]
size = Vector3(1.83216, 0.952118, 1.91967)

[node name="LoungeSofaLongProp" instance=ExtResource("1_1yinf")]

[node name="LoungeSofaLong" parent="Meshes" index="0" instance=ExtResource("2_opxqy")]

[node name="CollisionShape3D" type="CollisionShape3D" parent="StaticBody3D" index="0"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0.193933, 0.927704, 0.70315)
shape = SubResource("BoxShape3D_f8ox4")

[node name="CollisionShape3D2" type="CollisionShape3D" parent="StaticBody3D" index="1"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -0.59617, 0.476059, -0.881481)
shape = SubResource("BoxShape3D_vsxbc")
