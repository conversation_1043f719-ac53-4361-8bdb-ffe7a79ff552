[gd_scene load_steps=4 format=3 uid="uid://dj4ggj2t2a3qv"]

[ext_resource type="PackedScene" uid="uid://cy82bsxfj1ddv" path="res://prophaunt/scenes/PropObject.tscn" id="1_dvjtn"]
[ext_resource type="PackedScene" uid="uid://cflyhu4lrh0tl" path="res://prophaunt/maps/Source/ElectricalItem/laptop.tscn" id="2_lkyk4"]

[sub_resource type="BoxShape3D" id="BoxShape3D_bx0gf"]
size = Vector3(0.525436, 0.329607, 0.454404)

[node name="laptopProp" instance=ExtResource("1_dvjtn")]

[node name="laptop" parent="Meshes" index="0" instance=ExtResource("2_lkyk4")]

[node name="CollisionShape3D" type="CollisionShape3D" parent="StaticBody3D" index="0"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0.0137405, 0.162759, 0.0021957)
shape = SubResource("BoxShape3D_bx0gf")
