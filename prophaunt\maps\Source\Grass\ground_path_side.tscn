[gd_scene load_steps=5 format=4 uid="uid://cmtmq53mqsvao"]

[ext_resource type="Material" uid="uid://dkmcjc7wc3ws6" path="res://prophaunt/Mat/GrassPath.tres" id="1_qqwip"]

[sub_resource type="ArrayMesh" id="ArrayMesh_m3g62"]
_surfaces = [{
"aabb": AABB(-1, -0.1, -1, 2, 0.1, 2),
"format": 34896613377,
"index_count": 102,
"index_data": PackedByteArray("AgAAAAEAAQADAAIAAQAEAAMABAAFAAMABAAGAAUABgAHAAUABgAIAAcACAAJAAcACAAKAAkACgALAAkADgAMAA0ADQAPAA4ADwAQAA4AEAARAA4AEQASAA4AEgATAA4AEwAUAA4ADwANABUAEAAFAAcABwARABAADwADAAUABQAQAA8AFQACAAMAAwAPABUAEQAHAAkACQASABEAEgAJAAsACwATABIAFwAWAAQABAAAABcABAABAAAAFgAGAAQAFgAIAAYAFgAKAAgA"),
"lods": [0.0432161, PackedByteArray("AAAEAAIABAAAABcAFwAWAAQAFgAGAAQAFgAKAAYABAAGAAcABgAKAAcABAAHAAIACgALAAcADwACAAcAAgAPABUABwAQAA8ABwARABAAEQAHAAsACwASABEACwATABIADgAMAA0ADwANABUADQAPAA4ADwAQAA4AEAARAA4AEQASAA4AEgATAA4AEwAUAA4A"), 0.0788086, PackedByteArray("AAAWAAIAFgAAABcAFgARAAIAAgARABUAFgAKABEACgALABEACwATABEADgAMAA0ADQARAA4AEQATAA4AEQANABUAEwAUAA4A")],
"name": "Material",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 24,
"vertex_data": PackedByteArray("///+/wXhAAD/3/7/BeEAAP///v9rxwAA/9/+/2vHAAD/n/7/5PAAAP+f/v9K1wAA/1/+/+TwAAD/X/7/StcAAP8f/v8F4QAA/x/+/2vHAAAAAP7/BeEAAAAA/v9rxwAA//8AAAAAAAD//wAAckgAAAAAAAAAAAAA/98AAIy3AAD/nwAAa8cAAP9fAABrxwAA/x8AAIy3AAAAAAAAjLcAAAAAAABySAAA//8AAIy3AAAAAP////8AAP///////wAA")
}]
blend_shape_mode = 0

[sub_resource type="ArrayMesh" id="ArrayMesh_fqb2w"]
resource_name = "GroundPathSide_GroundPathSide"
_surfaces = [{
"aabb": AABB(-1, -0.1, -1, 2, 0.1, 2),
"attribute_data": PackedByteArray("m5hPgpGYuJE4jFaCOIxWglSMt5FUjLeRVIy3kSigubCck6awnJOmsJyTprAroALQrZMM0K2TDNCtkwzQlJgQ72CME+9gjBPvYIwT74aYff4ljGL+JYxi/qYBnYG+JJ2BpgGg/aYBoP2RgvyRkFoekZGC/JG5idiwQGIfsLmJ2LC2idnPQGIfz7aJ2c+ZgtPukFog7pmC0+5Wgjj+kFqg/ZBaoP2+JP/9ToJ4gpBanYF/p4X+f6dSgjiMVoIljGL+OIxWgq2TDNA="),
"format": 34896613399,
"index_count": 102,
"index_data": PackedByteArray("AwAAAAEAAQAFAAMAAQAHAAUABwAJAAUABwALAAkACwANAAkACwAPAA0ADwARAA0ADwATABEAEwAVABEAGAAWABcAFwAbABgAGwAeABgAHgAhABgAIQAkABgAJAAnABgAKAApABkAGwAXACsAHQAIAAwADAAgAB0AHAAGAAoACgAfABwAKgACAAQABAAaACoAIgAOABIAEgAlACIAIwAQABQAFAAmACMALQAsAAcABwAAAC0ABwABAAAALAALAAcALAAPAAsALAATAA8A"),
"lods": [0.0432161, PackedByteArray("AAAHADAABwAAAC0ALQAsAAcALAALAAcALAATAAsABwALADEACwATADEABwAxADAAEwAUADEAGgAwADEAMAAaACoAMQAdABoAMQAgAB0AIAAxABQAFAAjACAAFAAmACMAGAAWABcAGwAXACsAFwAbABgAGwAeABgAHgAhABgAIQAkABgAJAAnABgAKAApABkA"), 0.0788086, PackedByteArray("AAAsAC4ALAAAAC0ALAAgAC4ALgAgACoALAATACAAEwAvACAALwAmACAAGAAWABcAFwAhABgAIQAnABgAIQAXACsAKAApABkA")],
"material": ExtResource("1_qqwip"),
"name": "Material",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 50,
"vertex_data": PackedByteArray("///+/wXhUdX/3/7/BeET1f///v9rx+nm///+/2vHWtX/3/7/a8cH5//f/v9rx07V/9/+/2vH4OL/n/7/5PAG1f+f/v9K1/7m/5/+/0rXN9X/n/7/StfP4v9f/v/k8P7V/1/+/0rXk+b/X/7/Stdm1f9f/v9K17Dq/x/+/wXhXtX/H/7/a8dn5v8f/v9rx0zV/x/+/2vHoOoAAP7/BeFA1QAA/v9rx3nmAAD+/2vHINX//wAAAABU1f//AABySFTVAAAAAAAAVNUAAAAAAABU1f/fAACMtxfn/98AAIy3VNX/3wAAjLfb4v+fAABrx+Tm/58AAGvHVNX/nwAAa8fL4v9fAABrx3nm/18AAGvHVNX/XwAAa8es6v8fAACMt3Hm/x8AAIy3VNX/HwAAjLec6gAAAACMt4TmAAAAAIy3VNUAAAAAjLdU1QAAAABySP////8AAIy3+ub//wAAjLdU1QAA/////1vV////////QdX///7/a8d23QAA/v9rx07g///+/2vH2N3/X/7/SteZ3a6qp6r5qoKqO7Pts6Kqrar2shy0saqmqnGy1rcIq3qqC7MOtM2qmKqhsri33qkPqwe0Y7OUqrSqh7TKr56qr6pwtByzs6qlqqa0s6/Cqp2qRLQ6s+mqiqqpqqqqqaqqqqmqqqr/fwAAz7I2tKqqqqp9ss63SLPks6qqqaqusrC3RbQ5s6qqqqqOtMWvV7Qts6qqqqqttK6vK7RLs6qqqqr/fwAA/7//fxSzCLSqqqqqoaquqsCqnqr3uKaw17lZrs64vrAIulCv")
}]
blend_shape_mode = 0
shadow_mesh = SubResource("ArrayMesh_m3g62")

[sub_resource type="ConcavePolygonShape3D" id="ConcavePolygonShape3D_x7wm6"]
data = PackedVector3Array(1, 0, 0.558, 1, 0, 0.758, 0.75, 0, 0.758, 0.75, 0, 0.758, 0.75, 0, 0.558, 1, 0, 0.558, 0.75, 0, 0.758, 0.25, 0, 0.882, 0.75, 0, 0.558, 0.25, 0, 0.882, 0.25, 0, 0.682, 0.75, 0, 0.558, 0.25, 0, 0.882, -0.25, 0, 0.882, 0.25, 0, 0.682, -0.25, 0, 0.882, -0.25, 0, 0.682, 0.25, 0, 0.682, -0.25, 0, 0.882, -0.75, 0, 0.758, -0.25, 0, 0.682, -0.75, 0, 0.758, -0.75, 0, 0.558, -0.25, 0, 0.682, -0.75, 0, 0.758, -1, 0, 0.758, -0.75, 0, 0.558, -1, 0, 0.758, -1, 0, 0.558, -0.75, 0, 0.558, -1, -0.1, -1, 1, -0.1, -1, 1, -0.1, -0.434, 1, -0.1, -0.434, 0.75, -0.1, 0.434, -1, -0.1, -1, 0.75, -0.1, 0.434, 0.25, -0.1, 0.558, -1, -0.1, -1, 0.25, -0.1, 0.558, -0.25, -0.1, 0.558, -1, -0.1, -1, -0.25, -0.1, 0.558, -0.75, -0.1, 0.434, -1, -0.1, -1, -0.75, -0.1, 0.434, -1, -0.1, 0.434, -1, -0.1, -1, -1, -0.1, 0.434, -1, -0.1, -0.434, -1, -0.1, -1, 0.75, -0.1, 0.434, 1, -0.1, -0.434, 1, -0.1, 0.434, 0.25, -0.1, 0.558, 0.25, 0, 0.682, -0.25, 0, 0.682, -0.25, 0, 0.682, -0.25, -0.1, 0.558, 0.25, -0.1, 0.558, 0.75, -0.1, 0.434, 0.75, 0, 0.558, 0.25, 0, 0.682, 0.25, 0, 0.682, 0.25, -0.1, 0.558, 0.75, -0.1, 0.434, 1, -0.1, 0.434, 1, 0, 0.558, 0.75, 0, 0.558, 0.75, 0, 0.558, 0.75, -0.1, 0.434, 1, -0.1, 0.434, -0.25, -0.1, 0.558, -0.25, 0, 0.682, -0.75, 0, 0.558, -0.75, 0, 0.558, -0.75, -0.1, 0.434, -0.25, -0.1, 0.558, -0.75, -0.1, 0.434, -0.75, 0, 0.558, -1, 0, 0.558, -1, 0, 0.558, -1, -0.1, 0.434, -0.75, -0.1, 0.434, 1, 0, 1, -1, 0, 1, 0.25, 0, 0.882, 0.25, 0, 0.882, 1, 0, 0.758, 1, 0, 1, 0.25, 0, 0.882, 0.75, 0, 0.758, 1, 0, 0.758, -1, 0, 1, -0.25, 0, 0.882, 0.25, 0, 0.882, -1, 0, 1, -0.75, 0, 0.758, -0.25, 0, 0.882, -1, 0, 1, -1, 0, 0.758, -0.75, 0, 0.758)

[node name="GroundPathSide" type="MeshInstance3D"]
mesh = SubResource("ArrayMesh_fqb2w")
skeleton = NodePath("")

[node name="StaticBody3D" type="StaticBody3D" parent="."]

[node name="CollisionShape3D" type="CollisionShape3D" parent="StaticBody3D"]
shape = SubResource("ConcavePolygonShape3D_x7wm6")
