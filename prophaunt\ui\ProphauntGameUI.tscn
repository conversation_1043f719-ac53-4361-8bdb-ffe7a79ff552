[gd_scene load_steps=3 format=3 uid="uid://dmu6rpse3pie5"]

[ext_resource type="Script" path="res://prophaunt/scripts/ProphauntGameUI.gd" id="1_ui_script"]

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_1"]
bg_color = Color(0, 0, 0, 0.7)
corner_radius_top_left = 5
corner_radius_top_right = 5
corner_radius_bottom_right = 5
corner_radius_bottom_left = 5

[node name="ProphauntGameUI" type="Control"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
mouse_filter = 2
script = ExtResource("1_ui_script")
metadata/_edit_lock_ = true

[node name="TopPanel" type="Panel" parent="."]
layout_mode = 1
offset_right = 198.0
offset_bottom = 80.0
theme_override_styles/panel = SubResource("StyleBoxFlat_1")

[node name="TopContainer" type="HBoxContainer" parent="TopPanel"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 10.0
offset_top = 10.0
offset_right = -10.0
offset_bottom = -10.0
grow_horizontal = 2
grow_vertical = 2

[node name="RoundInfo" type="VBoxContainer" parent="TopPanel/TopContainer"]
layout_mode = 2

[node name="RoundLabel" type="Label" parent="TopPanel/TopContainer/RoundInfo"]
layout_mode = 2
text = "ROUND 1"
horizontal_alignment = 1

[node name="TimerLabel" type="Label" parent="TopPanel/TopContainer/RoundInfo"]
layout_mode = 2
text = "03:00"
horizontal_alignment = 1

[node name="VSeparator" type="VSeparator" parent="TopPanel/TopContainer"]
layout_mode = 2

[node name="TeamInfo" type="VBoxContainer" parent="TopPanel/TopContainer"]
layout_mode = 2

[node name="TeamLabel" type="Label" parent="TopPanel/TopContainer/TeamInfo"]
layout_mode = 2
text = "PROPS"
horizontal_alignment = 1

[node name="PropsAliveLabel" type="Label" parent="TopPanel/TopContainer/TeamInfo"]
layout_mode = 2
text = "5/6 Alive"
horizontal_alignment = 1

[node name="HealthPanel" type="Panel" parent="."]
layout_mode = 1
anchors_preset = 1
anchor_left = 1.0
anchor_right = 1.0
offset_left = -200.0
offset_bottom = 60.0
grow_horizontal = 0
grow_vertical = 0
theme_override_styles/panel = SubResource("StyleBoxFlat_1")

[node name="HealthContainer" type="VBoxContainer" parent="HealthPanel"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 10.0
offset_top = 10.0
offset_right = -10.0
offset_bottom = -10.0
grow_horizontal = 2
grow_vertical = 2

[node name="HealthLabel" type="Label" parent="HealthPanel/HealthContainer"]
layout_mode = 2
text = "HEALTH"
horizontal_alignment = 1

[node name="HealthBar" type="ProgressBar" parent="HealthPanel/HealthContainer"]
layout_mode = 2
value = 100.0
show_percentage = false

[node name="PlayerListPanel" type="Panel" parent="."]
visible = false
layout_mode = 1
anchors_preset = 1
anchor_left = 1.0
anchor_right = 1.0
offset_left = -250.0
offset_top = 100.0
offset_bottom = 400.0
grow_horizontal = 0
theme_override_styles/panel = SubResource("StyleBoxFlat_1")

[node name="PlayerListContainer" type="VBoxContainer" parent="PlayerListPanel"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 10.0
offset_top = 10.0
offset_right = -10.0
offset_bottom = -10.0
grow_horizontal = 2
grow_vertical = 2

[node name="PlayerListLabel" type="Label" parent="PlayerListPanel/PlayerListContainer"]
layout_mode = 2
text = "PLAYERS"
horizontal_alignment = 1

[node name="HSeparator" type="HSeparator" parent="PlayerListPanel/PlayerListContainer"]
layout_mode = 2

[node name="PropsLabel" type="Label" parent="PlayerListPanel/PlayerListContainer"]
layout_mode = 2
text = "PROPS"
horizontal_alignment = 1

[node name="PropsList" type="VBoxContainer" parent="PlayerListPanel/PlayerListContainer"]
layout_mode = 2

[node name="HSeparator2" type="HSeparator" parent="PlayerListPanel/PlayerListContainer"]
layout_mode = 2

[node name="HauntersLabel" type="Label" parent="PlayerListPanel/PlayerListContainer"]
layout_mode = 2
text = "HAUNTERS"
horizontal_alignment = 1

[node name="HauntersList" type="VBoxContainer" parent="PlayerListPanel/PlayerListContainer"]
layout_mode = 2

[node name="NotificationPanel" type="Panel" parent="."]
visible = false
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -200.0
offset_top = -50.0
offset_right = 200.0
offset_bottom = 50.0
grow_horizontal = 2
grow_vertical = 2
theme_override_styles/panel = SubResource("StyleBoxFlat_1")

[node name="NotificationLabel" type="Label" parent="NotificationPanel"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
text = "NOTIFICATION"
horizontal_alignment = 1
vertical_alignment = 1
