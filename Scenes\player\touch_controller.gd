extends Control
class_name TouchController

signal holdster
signal aim_shoot
signal snowball_shoot
signal drug
signal arrest
signal item_action

signal camera_has_changed

@export var max_rot_x = 0.5
@export var min_rot_x = -0.5
var camera_controller = null
var sensitivity = 5 

@onready var fg = $Controller/fg
@onready var controller = $Controller
@export var invert = false
@onready var action_button = $ActionButton
@export var visible_action = false
@onready var arm_parent: Control = $ArmParent
@onready var aim_texture: TextureRect = $ArmParent/aim
@onready var ammo_label: Label = $ArmParent/ShootButton/ammo
@onready var shoot_button: Button = $ArmParent/ShootButton
@onready var arrest_button: Button = $ArmParent/ArrestButton
@onready var snowball_parent: Control = $SnowballParent
@onready var snowball_ammo: Label = $SnowballParent/SnowballButton/ammo
@onready var drug_parent: Control = $DrugParent
@onready var action_parent: Control = $ActionParent
@onready var action_texture: TextureRect = $ActionParent/ActionButton/TextureRect
@onready var action_count: Label = $ActionParent/ActionButton/count
@onready var jump_button: TextureRect = $TextureRect
@onready var joystick_control: Control = $JoystickControl


var default_x
var default_y
var bg_width = 237
var bg_height = 237
var fg_width = 171
var fg_height = 171

var event_relative_x
var event_relative_y
var camera_changed = false
var client
var spectator_mode = false
var controller_start_position


func _ready():
	action_button.visible = false
	default_x = fg.position.x
	default_y = fg.position.y
	controller_start_position = controller.position
	hide_all_secondary_huds()
	if Constants.is_client():
		sensitivity *= DataSaver.get_item("camera_sensitivity", 1)


func _process(_delta):
	if camera_controller == null:
		return

	if spectator_mode:
		if camera_controller.to_follow_scene == null or not is_instance_valid(camera_controller.to_follow_scene) or camera_controller.to_follow_scene.visible == false:
			spectate_next_player()


func _physics_process(_delta):
	if camera_changed:
		var rot_x
		if invert:
			rot_x = camera_controller.rotation.x - event_relative_y
		else:
			rot_x = camera_controller.rotation.x + event_relative_y
		camera_controller.rotation.y -= event_relative_x
		rot_x = clamp(rot_x, min_rot_x, max_rot_x)
		camera_controller.rotation.x = rot_x
		camera_changed = false


func _on_controller_gui_input(_event):
	pass


func get_controller_state():
	var x = (fg.position.x - default_x) / (bg_width / 2.0)
	var y = (fg.position.y - default_y) / (bg_height / 2.0)
	
	var min_zero = 0.2
	if Vector2(x, y).length() < min_zero:
		return Vector2(0, 0)
	
	return Vector2(x, y)


func _on_full_screen_gui_input(event):
	if camera_controller == null:
		return 
	
	if spectator_mode:
		if event is InputEventMouseButton and Constants.is_desktop():
			if event.get("pressed"):
				spectate_next_player()

	@warning_ignore("unreachable_code")
	if event is InputEventScreenDrag or event is InputEventMouseMotion:
		event_relative_x = event.get("relative").x / 1000 * sensitivity
		event_relative_y = event.get("relative").y / 1000 * sensitivity
		camera_changed = true
		if Constants.LOCAL_MODE:#For Tutorial
			emit_signal("camera_has_changed")

	#if spectator_mode:
	#	if event is InputEventScreenDrag or event is InputEventScreenTouch or event is InputEventMouseMotion:
	#		if event.get("pressed"):
	#			spectate_next_player()


func spectate_next_player():
	client.spector_player_index += 1
	if GameSettings.get_unfinished_players_count() > 0:
		client.spector_player_index = client.spector_player_index % GameSettings.get_unfinished_players_count()
	camera_controller.to_follow_scene = GameSettings.find_spector_player_scene()


var jump_pressed = false
func _on_texture_rect_gui_input(event):
	if event is InputEventScreenTouch:
		if event.pressed:
			jump_pressed = true
		else:
			jump_pressed = false
	
	#_on_full_screen_gui_input(event)


var action_pressed = false
func _on_action_button_gui_input(event):
	if event is InputEventScreenTouch:
		if event.pressed:
			if Constants.im_dead() or Constants.im_freezed():
				return
			action_pressed = true
		else:
			action_pressed = false


func hide_buttons():
	$Controller.visible = false
	$TextureRect.visible = false
	action_button.visible = false
	arm_parent.visible = false


func show_buttons():
	$Controller.visible = true
	$TextureRect.visible = true
	if visible_action:
		action_button.visible = true


var joystick_pressed = false
var start_controller_x
var start_controller_y
func _on_joystick_control_gui_input(event):
	if spectator_mode:
		if event is InputEventScreenDrag or event is InputEventScreenTouch or event is InputEventMouseMotion:
			if event.get("pressed"):
				spectate_next_player()
	if event is InputEventScreenTouch:
		if event.get("pressed"):
			if joystick_pressed == false:
				joystick_pressed = true
				controller.position.x = event.position.x - float(bg_width / 2.0)
				controller.position.y = event.position.y - float(bg_height / 2.0)
				start_controller_x = controller.position.x
				start_controller_y = controller.position.y
		else:
			joystick_pressed = false
			fg.position.x = default_x
			fg.position.y = default_y
			controller.position = controller_start_position
	if event is InputEventScreenDrag:
			fg.position.x = event.position.x - start_controller_x - default_x - (fg_width / 2.0) + 35.5
			fg.position.y = event.position.y - start_controller_y - default_y - (fg_height / 2.0) + 35.5
			var tolerance = bg_width / 2.0
			fg.position.x = clamp(fg.position.x, default_x - tolerance, default_x + tolerance)
			fg.position.y = clamp(fg.position.y, default_y - tolerance, default_y + tolerance)


@warning_ignore("shadowed_variable_base_class")
func visible_action_button(visible=true):
	if visible and visible_action:
		action_button.visible = true
		if is_instance_valid(Constants.client):
			if is_instance_valid(Constants.client.my_player_scene):
				var action_scene = Constants.client.my_player_scene.actionable_scene
				if action_scene != null:
					if action_scene.get("action_image"):
						var texture = Constants.client.my_player_scene.actionable_scene.action_image
						if texture == null:
							action_button.texture = preload("res://Scenes/ui/assets/action.png")
						else:
							action_button.texture = texture
	else:
		action_button.visible = false


func _on_holdster_button_pressed() -> void:
	holdster.emit()


func _on_shoot_button_pressed() -> void:
	aim_shoot.emit()


func _on_snowball_button_pressed() -> void:
	snowball_shoot.emit()


func _on_arrest_button_pressed() -> void:
	arrest.emit()


func _on_holdster_button_gui_input(event: InputEvent) -> void:
	_on_full_screen_gui_input(event)


func _on_shoot_button_gui_input(event: InputEvent) -> void:
	_on_full_screen_gui_input(event)


func _on_arrest_button_gui_input(event: InputEvent) -> void:
	_on_full_screen_gui_input(event)


func hide_all_secondary_huds():
	drug_parent.visible = false
	arm_parent.visible = false
	snowball_parent.visible = false
	action_parent.visible = false


func _on_drug_button_pressed() -> void:
	drug.emit()


func show_item_ui(ui:HandInventoryItem.UI, item:HandInventoryItem) -> void:
	hide_all_secondary_huds()
	match ui:
		HandInventoryItem.UI.None:
			pass
		HandInventoryItem.UI.Arm:
			arm_parent.visible = true
		HandInventoryItem.UI.Snowball:
			snowball_parent.visible = true
			snowball_ammo.text = str(item.count)
		HandInventoryItem.UI.Drug:
			drug_parent.visible = true
		HandInventoryItem.UI.Action:
			action_parent.visible = true
			action_texture.texture = item.uiActionTexture
			action_count.text = str(item.count)


func _on_item_action_button_pressed() -> void:
	item_action.emit()
