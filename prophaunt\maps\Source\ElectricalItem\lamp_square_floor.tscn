[gd_scene load_steps=4 format=4 uid="uid://b66fffvxvmc0e"]

[ext_resource type="Material" uid="uid://b6gd8jjb1kopc" path="res://prophaunt/Mat/Colormap2.tres" id="1_j2vp1"]

[sub_resource type="ArrayMesh" id="ArrayMesh_tthuc"]
_surfaces = [{
"aabb": AABB(-0.18, -5.50031e-07, -0.18, 0.36, 2.58, 0.36),
"format": 34359742465,
"index_count": 180,
"index_data": PackedByteArray("AQACAAAAAgABAAMABgABAAAAAQAGAAcACAAHAAUAAQAJAAoABQALAAgACwAFAAMACwADAAoACgADAAEABgAFAAcADAAEAAYABAAMAAIABgAPAAwACAAMAA8ADAAIAAsADgAIAA8ACAAOAAkADAAKAA0ACgAMAAsADgAKAAkACgAOAA0AEQASABAAEgARABMAFQAWABQAFgAVABcAFwAVABgAFwAYABkAFAAaABUAGgAbABkAGQAbABcAGQAdABoAHQAZAB8AEAAWABEAFwASABMAEgAXABsAHwAYAB4AGAAfABkAFQAeABgAHgAVABwAFQAdABwAHQAVABoAAgAFAAQABQACAAMABwAIAAEAAQAIAAkABQAGAAQAAgAMAA0AAgANAA4ADwAGAAAADwAAAA4ADgAAAAIAGgAUABsAHQAeABwAHgAdAB8AEQAXABMAFwARABYAEgAUABAAFAASABsAFgAQABQA"),
"name": "Material",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 32,
"vertex_data": PackedByteArray("6lE4viwK1z7uUTi+6lE4vrYeJUD5UTi+6lE4viwK1z7oUTg+6lE4vrYeJUDeUTg+7FE4PiwK1z7oUTg+7FE4PrYeJUDeUTg+7FE4PiwK1z7uUTi+7FE4PrYeJUD5UTi+D7rwPbYeJUAouvC9DLrwvbYeJUAouvC9DLrwvbYeJUDyufA9D7rwPbYeJUDyufA9D7rwPSwK1z4IuvA9DLrwvSwK1z4IuvA9DLrwvSwK1z4UuvC9D7rwPSwK1z4UuvC97FE4PiBaDLXqUTg+7FE4PuClE7XsUTi+6lE4viBaDLXqUTg+6lE4vuClE7XsUTi+vHQTPgLCdT28dBM+rmKyPP/BdT2wYrK8vHQTPvzBdT28dBO+vHQTvvzBdT28dBO+kGKyvP/BdT2wYrK8kGKyvP/BdT2OYrI8rmKyPP/BdT2OYrI8vHQTvgLCdT28dBM+WPvePAjXE0C1+968WPvePAjXE0Dv+t48TPvevAjXE0C1+968TPvevAjXE0Dv+t48")
}]
blend_shape_mode = 0

[sub_resource type="ArrayMesh" id="ArrayMesh_t4gic"]
resource_name = "LampSquareFloor_lampSquareFloor"
_surfaces = [{
"aabb": AABB(-0.18, -5.50031e-07, -0.18, 0.36, 2.58, 0.36),
"attribute_data": PackedByteArray("8HNHP3zsrD7wc0c/fOysPvBzRz987Kw+8HNHP3zsrD7wc0c/fOysPvBzRz987Kw+8HNHP3zsrD7wc0c/fOysPvBzRz987Kw+8HNHP3zsrD7wc0c/fOysPvBzRz987Kw+8HNHP3zsrD7wc0c/fOysPvBzRz987Kw+8HNHP3zsrD7wc0c/fOysPvBzRz987Kw+8HNHP3zsrD7wc0c/fOysPvBzRz987Kw+8HNHP3zsrD7wc0c/fOysPvBzRz987Kw+8HNHP3zsrD7wc0c/fOysPvBzRz987Kw+8HNHP3zsrD7wc0c/fOysPvBzRz987Kw+8HNHP3zsrD7wc0c/fOysPvBzRz987Kw+8HNHP3zsrD7wc0c/fOysPvBzRz987Kw+8HNHP3zsrD7wc0c/fOysPvBzRz987Kw+8HNHP3zsrD7wc0c/fOysPvBzRz987Kw+8HNHP3zsrD7wc0c/fOysPvBzRz987Kw+8HNHP3zsrD7wc0c/fOysPvBzRz987Kw+q2xOP5CVJz2rbE4/kJUnPatsTj+QlSc9q2xOP5CVJz2rbE4/kJUnPatsTj+QlSc9q2xOP5CVJz2rbE4/kJUnPatsTj+QlSc9q2xOP5CVJz2rbE4/kJUnPatsTj+QlSc9q2xOP5CVJz2rbE4/kJUnPatsTj+QlSc9q2xOP5CVJz2rbE4/kJUnPatsTj+QlSc9q2xOP5CVJz2rbE4/kJUnPatsTj+QlSc9q2xOP5CVJz2rbE4/kJUnPatsTj+QlSc9q2xOP5CVJz2rbE4/kJUnPatsTj+QlSc9q2xOP5CVJz2rbE4/kJUnPatsTj+QlSc9q2xOP5CVJz2rbE4/kJUnPatsTj+QlSc9q2xOP5CVJz2rbE4/kJUnPatsTj+QlSc9q2xOP5CVJz2rbE4/kJUnPatsTj+QlSc9q2xOP5CVJz2rbE4/kJUnPatsTj+QlSc9q2xOP5CVJz2rbE4/kJUnPatsTj+QlSc9q2xOP5CVJz2rbE4/kJUnPatsTj+QlSc9"),
"format": 34359742487,
"index_count": 180,
"index_data": PackedByteArray("BQAIAAIACAAFAAsAEgADAAAAAwASABUAGQAWABAABAAcAB8AEAAiABkAIgAQAAoAIgAKAB8AHwAKAAQAFAARABcAJQANABMADQAlAAcAEwAuACUAGgAmAC8AJgAaACMAKgAYAC0AGAAqABsAJAAeACcAHgAkACEALAAgAB0AIAAsACkANAA3ADEANwA0ADoAPwBDAD0AQwA/AEYARgA/AEgARgBIAEsAPQBOAD8ATgBSAEsASwBSAEYATABYAE8AWABMAF4AMgBEADUARwA4ADsAOABHAFMAXwBKAFwASgBfAE0AQABbAEkAWwBAAFUAQQBZAFYAWQBBAFAABgAPAAwADwAGAAkAFgAZAAQABAAZABwAEQAUAA4ABwAlACgABwAoACsALgATAAEALgABACsAKwABAAcATgA9AFIAVwBaAFQAWgBXAF0AMwBFADkARQAzAEIANgA8ADAAPAA2AFEARAAyAD4A"),
"material": ExtResource("1_j2vp1"),
"name": "Material",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 96,
"vertex_data": PackedByteArray("6lE4viwK1z7uUTi+6lE4viwK1z7uUTi+6lE4viwK1z7uUTi+6lE4vrYeJUD5UTi+6lE4vrYeJUD5UTi+6lE4vrYeJUD5UTi+6lE4viwK1z7oUTg+6lE4viwK1z7oUTg+6lE4viwK1z7oUTg+6lE4vrYeJUDeUTg+6lE4vrYeJUDeUTg+6lE4vrYeJUDeUTg+7FE4PiwK1z7oUTg+7FE4PiwK1z7oUTg+7FE4PiwK1z7oUTg+7FE4PrYeJUDeUTg+7FE4PrYeJUDeUTg+7FE4PrYeJUDeUTg+7FE4PiwK1z7uUTi+7FE4PiwK1z7uUTi+7FE4PiwK1z7uUTi+7FE4PrYeJUD5UTi+7FE4PrYeJUD5UTi+7FE4PrYeJUD5UTi+D7rwPbYeJUAouvC9D7rwPbYeJUAouvC9D7rwPbYeJUAouvC9DLrwvbYeJUAouvC9DLrwvbYeJUAouvC9DLrwvbYeJUAouvC9DLrwvbYeJUDyufA9DLrwvbYeJUDyufA9DLrwvbYeJUDyufA9D7rwPbYeJUDyufA9D7rwPbYeJUDyufA9D7rwPbYeJUDyufA9D7rwPSwK1z4IuvA9D7rwPSwK1z4IuvA9D7rwPSwK1z4IuvA9DLrwvSwK1z4IuvA9DLrwvSwK1z4IuvA9DLrwvSwK1z4IuvA9DLrwvSwK1z4UuvC9DLrwvSwK1z4UuvC9DLrwvSwK1z4UuvC9D7rwPSwK1z4UuvC9D7rwPSwK1z4UuvC9D7rwPSwK1z4UuvC97FE4PiBaDLXqUTg+7FE4PiBaDLXqUTg+7FE4PiBaDLXqUTg+7FE4PuClE7XsUTi+7FE4PuClE7XsUTi+7FE4PuClE7XsUTi+6lE4viBaDLXqUTg+6lE4viBaDLXqUTg+6lE4viBaDLXqUTg+6lE4vuClE7XsUTi+6lE4vuClE7XsUTi+6lE4vuClE7XsUTi+vHQTPgLCdT28dBM+vHQTPgLCdT28dBM+vHQTPgLCdT28dBM+rmKyPP/BdT2wYrK8rmKyPP/BdT2wYrK8rmKyPP/BdT2wYrK8vHQTPvzBdT28dBO+vHQTPvzBdT28dBO+vHQTPvzBdT28dBO+vHQTvvzBdT28dBO+vHQTvvzBdT28dBO+vHQTvvzBdT28dBO+kGKyvP/BdT2wYrK8kGKyvP/BdT2wYrK8kGKyvP/BdT2wYrK8kGKyvP/BdT2OYrI8kGKyvP/BdT2OYrI8kGKyvP/BdT2OYrI8rmKyPP/BdT2OYrI8rmKyPP/BdT2OYrI8rmKyPP/BdT2OYrI8vHQTvgLCdT28dBM+vHQTvgLCdT28dBM+vHQTvgLCdT28dBM+WPvePAjXE0C1+968WPvePAjXE0C1+968WPvePAjXE0C1+968WPvePAjXE0Dv+t48WPvePAjXE0Dv+t48WPvePAjXE0Dv+t48TPvevAjXE0C1+968TPvevAjXE0C1+968TPvevAjXE0C1+968TPvevAjXE0Dv+t48TPvevAjXE0Dv+t48TPvevAjXE0Dv+t48/////////7//fwAA////vwAA/3////+//////////7//f///////vwAA/3////+//3//f////z//fwAA////vwAA/3////+//3//f////z//f///////vwAA/3////+//3//f////z//fwAA////v////3////+//3//f////z//f///////v////3////+//////////7//fwAA////v////3////+//////////7//f///////v////3////+//3//f////z//f///////vwAA/3////+//3//f////z//f///////v////3////+//////////7//f///////v////3////+//////////7//f///////vwAA/3////+//////////7//fwAA////vwAA/3////+//////////7//fwAA////v////3////+//3//f////z//fwAA////v////3////+//3//f////z//fwAA////vwAA/3////+//3//r////z//fwAA////v//P/6////+//8///////7//fwAA////v//P/6////+//3//r////z//fwAA////v/8v/6////+//8///////7//fwAA////v/8v/6////+//3//r////z//f///////v//P/6////+//3///////7+w/wAA////v7D/sX////+//8///////7//f///////v//P/6////+//8///////7//f///////v/8v/6////+//3///////7+w/wAA////v04AsX////+//3///////7//f7F/////P04AsX////+//3///////7//f7F/////P7D/sX////+//3//r////z//f///////v/8v/6////+//3///////7+w/wAA////v7D/sX////+//3///////7//f7F/////P7D/sX////+//3///////7+w/wAA////v04AsX////+//3///////7//f7F/////P04AsX////+/")
}]
blend_shape_mode = 0
shadow_mesh = SubResource("ArrayMesh_tthuc")

[node name="lampSquareFloor" type="MeshInstance3D"]
mesh = SubResource("ArrayMesh_t4gic")
skeleton = NodePath("")
