[gd_scene load_steps=6 format=4 uid="uid://dpyvkpmj7v04v"]

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_m8qkr"]
resource_name = "Wall.001"
cull_mode = 2
metallic = 0.2

[sub_resource type="ArrayMesh" id="ArrayMesh_ulpm8"]
_surfaces = [{
"aabb": AABB(-2, -3.00404e-07, -2.4, 4, 4, 0.8),
"format": 34896613377,
"index_count": 468,
"index_data": PackedByteArray("QwAOAB4AQwA5AA4ARQAIACIARQA0AAgAJQAFAAcAJQAkAAUAOQABADYAOQADAAEAMQAFADAAMQAHAAUASwAAAC0ASwA3AAAAOAAIADQAOAAMAAgAQQAAADcAQQAZAAAATQAPADsATQAyAA8AHQABAAMAHQAcAAEARAAMADgARAAjAAwAGgAQABEAGgAZABAAQgAQADwAQgAcABAAQAATABsAQAA9ABMARwAXAD4ARwAnABcAIQAUABUAIQAgABQARgAWACYARgA/ABYAOgAbAA0AOgBAABsAAgAZABoAAgAAABkAPAAZAEEAPAAQABkANgAcAEIANgABABwAEQAcAB0AEQAQABwAPQAeABMAPQBDAB4ABgAgACEABgAEACAAPgAjAEQAPgAXACMAPwAiABYAPwBFACIANQAmAAkANQBGACYAOwAnAEcAOwAPACcAFQAkACUAFQAUACQASgArAEgASgAvACsATAApADAATABJACkALgApAC0ALgAqACkAAgAtAAAAAgAuAC0AOgAvAEoAOgANAC8ASQAtACkASQBLAC0ANQAwAAUANQBMADAASAAyAE0ASAArADIAKgAwACkAKgAxADAAKgBNADEAKgBIAE0ACQBMADUACQAzAEwAKABLAEkAKAAsAEsAAgBKAC4AAgA6AEoAMwBJAEwAMwAoAEkALgBIACoALgBKAEgABwBHACUABwA7AEcABQBGADUABQAkAEYAFABFAD8AFAAgAEUAFQBEACEAFQA+AEQAEQBDAD0AEQAdAEMACgBCAB8ACgA2AEIAEgBBABgAEgA8AEEAAgBAADoAAgAaAEAAJAA/AEYAJAAUAD8AJQA+ABUAJQBHAD4AGgA9AEAAGgARAD0AHwA8ABIAHwBCADwAIQA4AAYAIQBEADgAMQA7AAcAMQBNADsAGAA3AAsAGABBADcABgA0AAQABgA4ADQALAA3AEsALAALADcADgA2AAoADgA5ADYAIAA0AEUAIAAEADQAHQA5AEMAHQADADkAVQBaAFYAVQBZAFoAVwBTAFQAVwBbAFMAWABVAE8AWABZAFUAXQBZAFgAXQBaAFkAXABaAF0AXABbAFoATgBUAFIATgBXAFQAUABXAE4AUABWAFcAUABVAFYAUABPAFUAUABYAE8AUABdAFgAUwBSAFQAUwBRAFIAVwBaAFsAVwBWAFoAUgBcAE4AUgBRAFwAXABTAFsAXABRAFMAXABQAE4AXABdAFAA"),
"name": "Wall.001",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 94,
"vertex_data": PackedByteArray("/////6tMAAD//wAAq0wAAP////9UswAA//8AAFSzAAAAAAAAq0wAAAAA//+rTAAAAAAAAFSzAAAAAP//VLMAAP8/AAD/PwAA/z/LzP8/AAD/vwAA/z8AAP+/y8z/PwAA/z8AAP+/AAD/v8vM/78AAP+/AAD/vwAA/z/LzP+/AAD///9/q0wAAP///39UswAA/7//f6tMAAD/v/9/VLMAAAAA/3+rTAAAAAD/f1SzAAD/P/9/q0wAAP8//39UswAA/7+Ogv8/AAD//46Cq0wAAP//joJUswAA/7+Ogv+/AAD//3B9q0wAAP//cH1UswAA/79wff+/AAD/v3B9/z8AAAAAcH2rTAAAAABwfVSzAAD/P3B9/z8AAP8/cH3/vwAAAACOgqtMAAAAAI6CVLMAAP8/joL/PwAA/z+Ogv+/AAD/f8vMq0wAAP9///+rTAAA/3///1SzAAD/f8vMVLMAAI6Cy8z/PwAAjoL//6tMAACOgv//VLMAAI6Cy8z/vwAAcH3//6tMAABwff//VLMAAHB9y8z/vwAAcH3LzP8/AACPAgAA/z8AAI8Cb/3/PwAAb/0AAP8/AABv/W/9/z8AAI8CAAD/vwAAb/0AAP+/AABv/W/9/78AAI8Cb/3/vwAAb/3/f6tMAABv/f9/VLMAAI8C/39UswAAjwL/f6tMAABv/Y6C/78AAG/9joL/PwAAb/1wff8/AABv/XB9/78AAI8CcH3/vwAAjwJwff8/AACPAo6C/z8AAI8CjoL/vwAA/39v/VSzAAD/f2/9q0wAAI6Cb/3/vwAAjoJv/f8/AABwfW/9/z8AAHB9b/3/vwAA/z/LzP7/AAD/vwAA//8AAP+/y8z+/wAA/z8AAAAAAAD/PwAA//8AAMxMAAAAAAAAzEwAAP//AAAyswAA//8AADKz9L/+/wAAzEz0v/7/AAD/vwAAAAAAADKzAAAAAAAAMrP0vwAAAADMTPS/AAAAAP8/y8wAAAAA/7/LzAAAAAA=")
}]
blend_shape_mode = 0

[sub_resource type="ArrayMesh" id="ArrayMesh_ok42r"]
resource_name = "WallDoorwayUV_WallDoorwayUV_001"
_surfaces = [{
"aabb": AABB(-2, -3.00404e-07, -2.4, 4, 4, 0.8),
"attribute_data": PackedByteArray("302un99Nrp/fTa6f302un4hvoJ+Ib6CfiG+gn9lN8KHZTfCh2U3wodlN8KFzb+mhc2/poXNv6aHNCvaezQr2ns0K9p5xLPuecSz7nnEs+55xLPue0QpCodEKQqHRCkKhayw9oWssPaFrLD2hayw9ocldsjrFAmueQll9Wp14eXGOd02fxyKBJom/LkK9AqWhzKbDVRCOLkJ/d4WiRdjDVZVeHJ+VXhyflV4cn5pebKKaXmyiml5soq44YuuuOGLrrDhX7Kw4V+ywG3+esBt/nrAbf561G8+htRvPobUbz6F8MC/ufDAv7n4wOe1+MDntxyLsRq845+owXhmfMF4ZnzBeGZ82Xm+iNl5vojZeb6LMpqdzqzjR7PteGp/7Xhqf+14anwFfbaIBX22iAV9tohCOrg+tONzrnXiyOq043OtJG36eSRt+nkkbfp5PG9GhTxvRoU8b0aHJXXlxejCp7om/rg9/ML/sFBx8nhQcfJ4UHHyeGxzSoRsc0qEbHNKhQlkSOn0wtO1F2KdzfTC07RRsWd0UbFndLz3Jni89yZ4vPcmeGz0iohs9IqIbPSKiEmxO3hJsTt6bPYEmFWze3JQ9yZ6UPcmelD3Jnn49J6J+PSeifj0nohCOw1URbMnezTzEns08xJ7NPMSetjwiorY8IqK2PCKiAvHDVRNs090WdH1aE2zT3fVCsjqDCoCegwqAnm4+um9yLIaeciyGnsldeXHVbymf1W8pn/MHRBHgTTmf4E05n8ymLkJ6Crmhegq5ocymLkLGb2Gixm9hoom/LkLYTWWi2E1loom/LkJqLLKhaiyyoYIwT+uCME/rlF6+npRevp6AMETsgDBE7JpeyqKaXsqiqjhM7ao4TO22Gy2ithstoqc4Qe6nOEHusBshnrAbIZ6Jv6dzfzC/7CRe8KIkXvCi8wfsRoMw1OodXpmeHV6ZnsldsjqBMMnrDl+Qng5fkJ7Mpq4PgTDJ6xRf96IUX/eizKauD6s40ew9G1uiPRtbovVCeXGmOLzuNhv0nTYb9J1uPhI6qDjH7SYc/J0mHPydib+nc6k4x+0uHFKiLhxSohk9faKKcl3eGT19oopyXd4yPW6ejHJo3TI9bp6McmjdEI4uQow9pqKMPaaiiXLX3ps9RBGoPUqeqD1Kno1y7dwWdLpvvjxFnr48RZ6LcuLdAvEuQqI8oaKiPKGii3Li3Qw9aLtPXfqpL18L2psdCcurIHXIZSBv2L4g8r/oO7PISUzTqSlO5Nn7OzDAoyDcy5YZYNhuAvvDzyEqu2Ugb9heAsvKpyApypIZE9owO7jZyyHevGIgI9o/O+nSryDBxmkgu9Y/O+nSNjr7xgFOIqgzEpTux1Qj01M7GL2fW0Go0h+z7sdUI9OrIHXIlhlg2K8gwcanICnKmhms1k87GswuOmPKr054sEMSxefXVFTMLjpjyk5cmLDiH+Tnt1Tz2eA7G8wFXuauIF/a4KsdOsToO7PI/0y/rhpOs+DsO//G"),
"format": 34896613399,
"index_count": 468,
"index_data": PackedByteArray("tgAlAEwAtgCRACUAvgAcAFYAvgCCABwAXwAUABsAXwBcABQAkgAEAIkAkgALAAQAfAASAHkAfAAZABIA1wAAAHAA1wCMAAAAjwAdAIMAjwAjAB0AsQACAI0AsQA/AAIA3gAnAJcA3gB+ACcASwAGAA0ASwBIAAYAugAiAI4AugBYACIAQwAqAC0AQwBAACoAtAAoAJwAtABGACgAqwAxAEUAqwCfADEAxwA7AKMAxwBjADsAVQA0ADcAVQBSADQAwwA5AGEAwwCnADkAlABEACQAlACqAEQACgBAAEMACgADAEAAnQA+ALAAnQApAD4AigBHALUAigAFAEcALQBIAEsALQAqAEgAngBNADAAngC3AE0AFwBSAFUAFwAQAFIAogBZALsAogA6AFkApgBXADgApgC/AFcAhQBgAB4AhQDCAGAAlwBiAMYAlwAnAGIANwBcAF8ANwA0AFwA1QBtAM0A1QB3AG0A3ABnAHoA3ADOAGcAdABmAHEAdABpAGYACABxAAEACAB0AHEAlAB2ANIAlAAkAHYA0AByAGgA0ADYAHIAhgB4ABEAhgDbAHgAywB/AOEAywBsAH8AaQB5AGYAaQB8AHkAagDgAH0AagDKAOAAHgDaAIUAHgCAANoAZQDZANEAZQBvANkABwDTAHMABwCVANMAgQDPAN0AgQBkAM8AdQDMAGsAdQDUAMwAGgDJAF4AGgCZAMkAEwDFAIcAEwBbAMUAMgDAAKgAMgBQAMAANQC8AFMANQCkALwALAC4AKEALABJALgAHwCyAE4AHwCIALIALwCvAD0ALwCbAK8ACQCtAJYACQBCAK0AWgCpAMQAWgAzAKkAXQClADYAXQDIAKUAQQCgAKwAQQArAKAATwCaAC4ATwCzAJoAVACQABYAVAC9AJAAewCYABgAewDfAJgAPACLACEAPACuAIsAFQCDAA4AFQCPAIMAbgCLANYAbgAhAIsAJgCJACAAJgCSAIkAUQCEAMEAUQAPAIQASgCTALkASgAMAJMA+wANAf8A+wAJAQ0BAwH1APgAAwERAfUABQH6AOcABQEIAfoAFgEHAQQBFgEKAQcBEwELARcBEwEPAQsB4gD2APAA4gAAAfYA6gABAeMA6gD9AAEB6QD5APwA6QDmAPkA7AAGAegA7AAZAQYB9ADxAPcA9ADuAPEAAgEMARABAgH+AAwB8gAVAeUA8gDvABUBEgHzAA4BEgHtAPMAFAHrAOQAFAEYAesA"),
"material": SubResource("StandardMaterial3D_m8qkr"),
"name": "Wall.001",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 282,
"vertex_data": PackedByteArray("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")
}]
blend_shape_mode = 0
shadow_mesh = SubResource("ArrayMesh_ulpm8")

[sub_resource type="BoxShape3D" id="BoxShape3D_gl16q"]
size = Vector3(1.2, 4, 0.8)

[sub_resource type="BoxShape3D" id="BoxShape3D_s6q2e"]
size = Vector3(0.9, 4, 0.8)

[node name="WallDoorway" type="MeshInstance3D" groups=["VisibleGroup0"]]
mesh = SubResource("ArrayMesh_ok42r")
skeleton = NodePath("")

[node name="StaticBody3D" type="StaticBody3D" parent="."]
collision_layer = 2

[node name="CollisionShape3D" type="CollisionShape3D" parent="StaticBody3D"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -1.40026, 2, -1.99972)
shape = SubResource("BoxShape3D_gl16q")

[node name="CollisionShape3D2" type="CollisionShape3D" parent="StaticBody3D"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 1.4004, 2, -2.00106)
shape = SubResource("BoxShape3D_gl16q")

[node name="CollisionShape3D3" type="CollisionShape3D" parent="StaticBody3D"]
transform = Transform3D(-4.37114e-08, -1, 0, 1, -4.37114e-08, 0, 0, 0, 1, 5.96046e-08, 3.50254, -1.99956)
shape = SubResource("BoxShape3D_s6q2e")
