[gd_scene load_steps=4 format=3 uid="uid://bmr0ogfeka23j"]

[ext_resource type="PackedScene" uid="uid://cy82bsxfj1ddv" path="res://prophaunt/scenes/PropObject.tscn" id="1_3jebu"]
[ext_resource type="PackedScene" uid="uid://cxinsgex3icit" path="res://prophaunt/maps/Source/Bathroom/bathroom_sink.tscn" id="2_l522x"]

[sub_resource type="BoxShape3D" id="BoxShape3D_f8ox4"]
size = Vector3(0.936159, 1.51393, 0.813279)

[node name="BathroomCabinetProp" instance=ExtResource("1_3jebu")]

[node name="bathroomSink" parent="Meshes" index="0" instance=ExtResource("2_l522x")]

[node name="CollisionShape3D" type="CollisionShape3D" parent="StaticBody3D" index="0"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0.000614375, 0.756965, 0.215873)
shape = SubResource("BoxShape3D_f8ox4")
