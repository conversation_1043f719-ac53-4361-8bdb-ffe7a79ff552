[gd_scene load_steps=6 format=4 uid="uid://ddsqn368mmxu"]

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_d4akv"]
resource_name = "Wall.001"
cull_mode = 2
metallic = 0.2

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_djaui"]
resource_name = "Material.001"
transparency = 4
cull_mode = 2
albedo_color = Color(0.25827, 0.687782, 1, 0.20614)
roughness = 0.1

[sub_resource type="ArrayMesh" id="ArrayMesh_o3f7c"]
_surfaces = [{
"aabb": AABB(-2, -2.38419e-07, -2.39951, 4, 4, 0.8),
"format": 34896613377,
"index_count": 840,
"index_data": PackedByteArray("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"),
"name": "Wall.001",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 156,
"vertex_data": PackedByteArray("/7/KzP+/AAD/P8rM/78AAP+/bjb/vwAA/z9uNv+/AAD/v8rM/z8AAP8/ysz/PwAA/79uNv8/AAD/P242/z8AAP//AABUswAA//8AAKtMAAAAAAAAVLMAAAAAAACrTAAA/////6tMAAD/////VLMAAAAA//+rTAAAAAD//1SzAAD/v/9/VLMAAP+//3+rTAAA////f1SzAAD///9/q0wAAP+/cH3/vwAA/79wff8/AAD//3B9VLMAAP//cH2rTAAA//+OglSzAAD//46Cq0wAAP+/joL/vwAA/7+Ogv8/AAD/P/9/VLMAAP8//3+rTAAAAAD/f1SzAAAAAP9/q0wAAP8/cH3/vwAA/z9wff8/AAAAAHB9VLMAAAAAcH2rTAAAAACOglSzAAAAAI6Cq0wAAP8/joL/vwAA/z+Ogv8/AAD/f242VLMAAP9/bjarTAAA/38AAFSzAAD/fwAAq0wAAP9/ysxUswAA/3/KzKtMAAD/f///q0wAAP9///9UswAAjoJuNv+/AACOgm42/z8AAI6CAABUswAAjoIAAKtMAABwfW42/78AAHB9bjb/PwAAcH0AAFSzAABwfQAAq0wAAI6Cysz/vwAAjoLKzP8/AACOgv//q0wAAI6C//9UswAAcH3KzP+/AABwfcrM/z8AAHB9//+rTAAAcH3//1SzAABv/Y8C/78AAG/9b/3/vwAAjwKPAv+/AACPAm/9/78AAG/9/39UswAAb/1wff+/AABv/Y6C/78AAI8C/39UswAAjwJwff+/AACPAo6C/78AAP9/jwJUswAA/39v/VSzAACOgo8C/78AAHB9jwL/vwAAjoJv/f+/AABwfW/9/78AAI8CjwL/PwAAjwJv/f8/AABv/Y8C/z8AAG/9b/3/PwAAb/3/f6tMAABv/XB9/z8AAG/9joL/PwAAjwL/f6tMAACPAnB9/z8AAI8CjoL/PwAA/3+PAqtMAAD/f2/9q0wAAI6CjwL/PwAAcH2PAv8/AACOgm/9/z8AAHB9b/3/PwAA/7/KzP7/AAD/P8rM/v8AAP+/bjb//wAA/79uNv+/AAD/P242//8AAP8/bjb/vwAAMrNqQf//AAAys2rB/v8AAMxMasH+/wAAzExqQf//AAD/v242AAAAAMxMakEAAAAA/z9uNgAAAAD/P8rMAAAAAMxMasEAAAAAMrNqwQAAAAAys2pBAAAAAP+/yswAAAAA/79uNv8/AAD/P242/z8AAMxMaoH/fwAAMrNqgf9/AAAyU9GHulwAAMys0Ye6XAAAMlMEu7pcAADMrAS7ulwAADJTBLtuQAAAMlPRh25AAAAyUwS7/38AADJT0Yf/fwAAzKwEu25AAADMrAS7/38AAMys0YduQAAAzKzRh/9/AADMTGqBbkAAAMxMasFuQAAAzExqwf9/AAAys2rBbkAAADKzasH/fwAAMrNqgW5AAAAyU1B9RKMAAMysUH1EowAAMlODsESjAADMrIOwRKMAADJTg7D/fwAAMlNQff9/AAAyU4OwkL8AADJTUH2QvwAAzKyDsP9/AADMrIOwkL8AAMysUH3/fwAAzKxQfZC/AADMTOl2/38AAMxM6bb/fwAAzEzpdpC/AADMTOm2kL8AADKz6bb/fwAAMrPptpC/AAAys+l2kL8AADKz6Xb/fwAA")
}, {
"aabb": AABB(-0.7, 1.95804, -2.12175, 1.4, 0.964132, 0.23367),
"format": 34359742465,
"index_count": 72,
"index_data": PackedByteArray("AgABAAMAAgAAAAEABgAFAAQABgAHAAUAAwAFAAcAAwABAAUAAAAGAAQAAAACAAYAAgAHAAYAAgADAAcAAQAEAAUAAQAAAAQACgAJAAsACgAIAAkADgANAAwADgAPAA0ACQAMAA0ACQAIAAwACwANAA8ACwAJAA0ACAAOAAwACAAKAA4ACgAPAA4ACgALAA8A"),
"name": "Material.001",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 16,
"vertex_data": PackedByteArray("MjMzv6LRB0DGygfAMjMzP6LRB0DGygfAMjMzv9YEO0DGygfAMjMzP9YEO0DGygfAMjMzv6LRB0BSAgXAMjMzP6LRB0BSAgXAMjMzv9YEO0BSAgXAMjMzP9YEO0BSAgXAMjMzv/2g+j+OPfe/MjMzP/2g+j+OPfe/MjMzv7GDMECOPfe/MjMzP7GDMECOPfe/MjMzv/2g+j+krPG/MjMzP/2g+j+krPG/MjMzv7GDMECkrPG/MjMzP7GDMECkrPG/")
}]
blend_shape_mode = 0

[sub_resource type="ArrayMesh" id="ArrayMesh_u24jy"]
resource_name = "WallWindowSlideOpenUV_WallWindowSlideOpenUV_001"
_surfaces = [{
"aabb": AABB(-2, -2.38419e-07, -2.39951, 4, 4, 0.8),
"attribute_data": PackedByteArray("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"),
"format": 34896613399,
"index_count": 840,
"index_data": PackedByteArray("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"),
"material": SubResource("StandardMaterial3D_d4akv"),
"name": "Wall.001",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 520,
"vertex_data": PackedByteArray("/7/KzP+//7//P8rM/78AgP+/bjb/v/+//z9uNv+/AID/v8rM/z////8/ysz/P////79uNv8/////P242/z//////AABUs9X9//8AAFSzKf7//wAAVLNHx///AABUs1zW//8AAKtMwP///wAAq0xh/v//AACrTHDp//8AAKtMMdYAAAAAVLOl/wAAAABUs0T/AAAAAFSzOMYAAAAAVLMs1QAAAACrTJT/AAAAAKtMNv8AAAAAq0y26QAAAACrTCLV/////6tMN+D/////q0wKwP////+rTErq/////6tMW9b/////VLPLn/////9UswfA/////1Szp8T/////VLMw1gAA//+rTDTgAAD//6tMB8AAAP//q0w16gAA//+rTH/UAAD//1Sz358AAP//VLMKwAAA//9Us3TEAAD//1SzVtT/v/9/VLPKn/+//39Us8qf/7//f6tMNOD/v/9/q0w04P///39Us3jA////f1Szs8j///9/VLOI1f///3+rTCbn////f6tMiv7///9/q0yI1f+/cH3/v/+//79wff+/yp//v3B9/z////+/cH3/PzTg//9wfVSzOb///3B9VLM+x///cH1Us+vU//9wfatMLef//3B9q0xx6f//cH2rTFvV//+OglSzb8f//46CVLPhxP//joJUs1vV//+OgqtM6f7//46Cq0w06v//joKrTOvU/7+Ogv+//7//v46C/7/Kn/+/joL/P////7+Ogv8/NOD/P/9/VLOo//8//39Us6j//z//f6tM2///P/9/q0zb/wAA/39Us2m/AAD/f1SzI8gAAP9/VLM91QAA/3+rTCjnAAD/f6tMYv4AAP9/q0wT1f8/cH3/vwCA/z9wff+/qP//P3B9/z////8/cH3/P9v/AABwfVSzHL8AAHB9VLM8xgAAcH1UszLVAABwfatMKOcAAHB9q0y16QAAcH2rTIjVAACOglSzk8YAAI6CVLO8xAAAjoJUs8DVAACOgqtM7f4AAI6Cq0wh6gAAjoKrTE7V/z+Ogv+/AID/P46C/7+o//8/joL/P////z+Ogv8/2///f242VLM6xv9/bjZUszrG/39uNqtM6en/f242q0zp6f9/AABUs+v//38AAFSzemb/fwAAVLO+/f9/AACrTKb+/38AAKtMPBL/fwAAq0zr7/9/ysxUszrG/3/KzFSzOsb/f8rMq0zp6f9/ysyrTOnp/3///6tMAMD/f///q0w97f9///+rTPHt/3///1SzAMD/f///VLMGmf9///9Us56ZjoJuNv+//7+Ogm42/786xo6Cbjb/P///joJuNv8/6emOggAAVLP2/Y6CAABUs/L/joIAAFSzF/yOggAAq0zR/46CAACrTIT/joIAAKtMf+9wfW42/78AgHB9bjb/vzrGcH1uNv8///9wfW42/z/p6XB9AABUs6H/cH0AAFSzvP9wfQAAVLONZnB9AACrTJP/cH0AAKtMrv9wfQAAq0w+Eo6Cysz/v/+/joLKzP+/OsaOgsrM/z///46Cysz/P+npjoL//6tMN+COgv//q0z/v46C//+rTK3tjoL//1Szy5+Ogv//VLP/v46C//9Us1OZcH3KzP+/AIBwfcrM/786xnB9ysz/P///cH3KzP8/6elwff//q0w04HB9//+rTP+/cH3//6tMh+1wff//VLPdn3B9//9Us/+/cH3//1SzMZlv/Y8C/7//v2/9jwL/v9X9b/2PAv+/R8dv/W/9/7//v2/9b/3/v8yfb/1v/f+/pcSPAo8C/78AgI8CjwL/v6X/jwKPAv+/OMaPAm/9/78AgI8Cb/3/v9+fjwJv/f+/csRv/f9/VLPKn2/9/39Us8qfb/3/f1SzuMFv/f9/VLNvx2/9cH3/v/+/b/1wff+/yp9v/XB9/794wG/9cH3/vz7Hb/2Ogv+//79v/Y6C/7/Kn2/9joL/v5XGb/2Ogv+/4MSPAv9/VLOo/48C/39Us6j/jwL/f1Sztb+PAv9/VLOTxo8CcH3/vwCAjwJwff+/qP+PAnB9/79pv48CcH3/vzzGjwKOgv+/AICPAo6C/7+o/48CjoL/v4bFjwKOgv+/u8T/f48CVLONZv9/jwJUszrG/3+PAlSzZf//f48CVLM6xv9/b/1Us+uY/39v/VSzOsb/f2/9VLNTmf9/b/1UszrGjoKPAv+//7+Ogo8C/7/3/Y6CjwL/v779joKPAv+/OsZwfY8C/78AgHB9jwL/v6H/cH2PAv+/mWZwfY8C/786xo6Cb/3/v/+/joJv/f+/y5+Ogm/9/78qmY6Cb/3/vzrGcH1v/f+/AIBwfW/9/7/dn3B9b/3/vwaZcH1v/f+/OsaPAo8C/z///48CjwL/P5T/jwKPAv8/tumPAm/9/z///48Cb/3/PzTgjwJv/f8/Nupv/Y8C/z///2/9jwL/P8D/b/2PAv8/cOlv/W/9/z///2/9b/3/Pzfgb/1v/f8/S+pv/f9/q0w04G/9/3+rTDTgb/3/f6tMIedv/f9/q0zp/m/9cH3/P///b/1wff8/NOBv/XB9/z8m52/9cH3/P3Hpb/2Ogv8///9v/Y6C/z804G/9joL/Pyr/b/2Ogv8/NOqPAv9/q0zb/48C/3+rTNv/jwL/f6tMJ+ePAv9/q0zt/o8CcH3/P///jwJwff8/2/+PAnB9/z8o548CcH3/P7XpjwKOgv8///+PAo6C/z/b/48CjoL/P0r/jwKOgv8/Iur/f48Cq0w5Ev9/jwKrTOnp/3+PAqtMf+//f48Cq0zp6f9/b/2rTIft/39v/atM6en/f2/9q0w27v9/b/2rTOnpjoKPAv8///+Ogo8C/z/R/46CjwL/PzfvjoKPAv8/6elwfY8C/z///3B9jwL/P5P/cH2PAv8/PBJwfY8C/z/p6Y6Cb/3/P///joJv/f8/N+COgm/9/z/x7Y6Cb/3/P+npcH1v/f8///9wfW/9/z804HB9b/3/P7ntcH1v/f8/6en/v8rM/7+8//+/ysz/v4rV/7/KzP7/XMD/v8rM/v+h//+/ysz+/7z//7/KzP7/itX/P8rM/7+8//8/ysz/v4rV/z/KzP7/XMD/P8rM/v+h//8/ysz+/7z//z/KzP7/itX/v242//+h//+/bjb//1zA/79uNv//vP//v242//+K1f+/bjb/v7z//79uNv+/itX/P242//9cwP8/bjb//6H//z9uNv//vP//P242//+K1f8/bjb/v7z//z9uNv+/itUys2pB//+h/zKzakH//1zAMrNqQf///78ys2pB//+K1TKzasH+/1zAMrNqwf7/of8ys2rB/v+8/zKzasH+/4rVzExqwf7/XMDMTGrB/v+h/8xMasH+/7z/zExqwf7/itXMTGpB//9cwMxMakH//6H/zExqQf///7/MTGpB//+K1f+/bjYAAP///79uNgAA////v242AAC8//+/bjYAAIrVzExqQQAA///MTGpBAAD//8xMakEAAP+/zExqQQAAitX/P242AAD///8/bjYAAP///z9uNgAAvP//P242AACK1f8/yswAAP///z/KzAAA////P8rMAAC8//8/yswAAIrVzExqwQAA///MTGrBAAD//8xMasEAALz/zExqwQAAitUys2rBAAD//zKzasEAAP//MrNqwQAAvP8ys2rBAACK1TKzakEAAP//MrNqQQAA//8ys2pBAAD/vzKzakEAAIrV/7/KzAAA////v8rMAAD///+/yswAALz//7/KzAAAitX/v8rM/z+8//+/ysz/P4rV/z/KzP8/vP//P8rM/z+K1f+/bjb/P7z//79uNv8/itX/P242/z+8//8/bjb/P4rVzExqgf9/of/MTGqB/39cwMxMaoH/f7z/zExqgf9/itUys2qB/3+h/zKzaoH/f1zAMrNqgf9/vP8ys2qB/3+K1TJT0Ye6XLz/MlPRh7pcvP8yU9GHulyK1TJT0Ye6XIrVzKzRh7pcvP/MrNGHuly8/8ys0Ye6XIrVzKzRh7pcitUyUwS7uly8/zJTBLu6XLz/MlMEu7pcitUyUwS7ulyK1cysBLu6XLz/zKwEu7pcvP/MrAS7ulyK1cysBLu6XIrVMlMEu25A//8yUwS7bkD//zJTBLtuQLz/MlMEu25AitUyU9GHbkD//zJT0YduQP//MlPRh25AvP8yU9GHbkCK1TJTBLv/f6H/MlMEu/9/XMAyUwS7/3+8/zJTBLv/f4rVMlPRh/9/of8yU9GH/39cwDJT0Yf/f7z/MlPRh/9/itXMrAS7bkD//8ysBLtuQP//zKwEu25AvP/MrAS7bkCK1cysBLv/f6H/zKwEu/9/XMDMrAS7/3+8/8ysBLv/f4rVzKzRh25A///MrNGHbkD//8ys0YduQLz/zKzRh25AitXMrNGH/3+h/8ys0Yf/f1zAzKzRh/9/vP/MrNGH/3+K1cxMaoFuQP//zExqgW5A///MTGqBbkC8/8xMaoFuQIrVzExqwW5A///MTGrBbkD//8xMasFuQLz/zExqwW5AitXMTGrB/3+h/8xMasH/f1zAzExqwf9/vP/MTGrB/3+K1TKzasFuQP//MrNqwW5A//8ys2rBbkC8/zKzasFuQIrVMrNqwf9/of8ys2rB/39cwDKzasH/f7z/MrNqwf9/itUys2qBbkD//zKzaoFuQP//MrNqgW5AvP8ys2qBbkCK1TJTUH1Eo7z/MlNQfUSjvP8yU1B9RKOK1TJTUH1Eo4rVzKxQfUSjvP/MrFB9RKO8/8ysUH1Eo4rVzKxQfUSjitUyU4OwRKO8/zJTg7BEo7z/MlODsESjitUyU4OwRKOK1cysg7BEo7z/zKyDsESjvP/MrIOwRKOK1cysg7BEo4rVMlODsP9///8yU4Ow/3///zJTg7D/f7z/MlODsP9/itUyU1B9/3///zJTUH3/f///MlNQff9/vP8yU1B9/3+K1TJTg7CQv6H/MlODsJC/XMAyU4OwkL+8/zJTg7CQv4rVMlNQfZC/XMAyU1B9kL+h/zJTUH2Qv7z/MlNQfZC/itXMrIOw/3///8ysg7D/f///zKyDsP9/vP/MrIOw/3+K1cysg7CQv6H/zKyDsJC/XMDMrIOwkL+8/8ysg7CQv4rVzKxQff9////MrFB9/3///8ysUH3/f7z/zKxQff9/itXMrFB9kL+h/8ysUH2Qv1zAzKxQfZC/vP/MrFB9kL+K1cxM6Xb/f///zEzpdv9////MTOl2/3+8/8xM6Xb/f4rVzEzptv9////MTOm2/3///8xM6bb/f7z/zEzptv9/itXMTOl2kL9cwMxM6XaQv6H/zEzpdpC/vP/MTOl2kL+K1cxM6baQv6H/zEzptpC/XMDMTOm2kL+8/8xM6baQv4rVMrPptv9///8ys+m2/3///zKz6bb/f7z/MrPptv9/itUys+m2kL+h/zKz6baQv1zAMrPptpC/vP8ys+m2kL+K1TKz6XaQv6H/MrPpdpC/XMAys+l2kL+8/zKz6XaQv4rVMrPpdv9///8ys+l2/3///zKz6Xb/f7z/MrPpdv9/itX//////3////9//3//f///////f/+//7////9//z//v+9+Alv+fQBBIZ35Y0erj1YyWkn/On7iQFy05Uktq1xW03+/WjF/ZkBP4w7jJNVs1cl+KCYif25Ap8oYyxfVctVp/PB6Hfw8eAG2OExGq45WefwGgY78HnmlnE5iLatcVoX+C4GM/Bt5/8skyk/U1tWZ/Fh0G/w5eCziY+Md1PDVbv8PfpAA74Ft/zF/kQDNgB+oQn57ggVbyaqTVW3C0kM3sYtIyaqTVf9//39u/w9+////f23/MX9kqJB9H53zY2mq1FRbw0hFXrTnSa6qXVXigW1asJx0Yq6qXVUEslhJ1rX7S2mq1FT/////kADvgf///3+RAM2AKoA/pdR/v1pngGnal3+VJar9pNfC2sb9OdVi1SrEWL01yBvPBdV71f9///8qgD+l/z//v2eAadp//ZTXUeMN4yvVadVLxEO9o8oby5PVNdUC2oX+W+JV49XVE9VfyfLNyctKyk3VV9X/f///1H+/Wv+//7+Xf5UlrhzwHFDjDuPONEg1MMu2yup/CkBEMx47sevW7IR+vUCZBL9lop6u+K4c8BxQ4w7jzjRINTDLtspr/td80ONbeHv8jplq/td8db2dRpDNMMX/f/9/UOMO4////38wy7bK/379Wvg/8f9G60LtVVp7/3h/Q0CMnMX6/3///64c8Bz/P/+/zjRINdF/wFq1fyRA7zNfO8h+KCamfyxAqQS2Zf////9Q4w7j////fzDLtsqc/Dh7x/8bgO36i5pU/caA8/7pfdjKJcT/f///rhzwHP+//7/ONEg14f7JgPT+6X3h5Ad6x/z3dMf/G4BsvFFJ/3//f+9+Alshnflj/////3L8CIGknE1i/3///9N/v1pP4w7j/3///5r8WnQr4mPjbv8PfpAA74Hcp/F+4oFtWv9//39u/w9+H6hCfh+d82P/////kADvgXmBA1qvnHNiKoA/pdR/v1rV/bXXAtqF/v9///8qgD+lqv2k11HjDeP/f///1H+/Wn3ZC/9b4lXj7zNfO64c8Bwd7GvsUOMO45G+ukOuHPAc2MolxFDjDuP/f/9/AH/9WrHr1uxQ4w7j/3///9F/wFpjNIw7rhzwHP////9Q/ceA6Mhnw1DjDuP/f///yPz6dHW9nUauHPAc/z//v8l+KCanyhjL/7//v4L+DYEAzCPK////fzFaSP9ctOVJ////f2n88XoDtjpMbf8xf5EAzYB9wVlCBLJYSf///39t/zF/bcLSQ1606En///9/kQDNgI+y4knXtfxLZ4Bp2pd/lSUJxG29X8nyzf8//79ngGnaKsRYvaPKG8v/v/+/l3+VJSfKKs3Ky0rKigTJZc40RzWMnMX6MMu2yuHkB3rONEg1EP6NmDDLtsr///9/VFp6/xebOfwwy7bK/z//v8h+KCaZBL9lzjRHNf///3+d/Dp7e/yOmTDLtsr/v/+/3/7KgJvlLXvONEg1SIDav2kqyyr//////3//f0iA2r9pKssqSIDav5XVM9X//////3//f0iA2r+V1TPV/3//f/////+2fyRAaSrLKrZ/JEBpKssq//////9//3+2fyRAldUz1bZ/JECV1TPV/3//f/////9t/9x+ldUz1f//////f/9/tn8kQJXVM9X//////3//f7Z/JEBpKssq//////9//39t/9x+aSrLKkjAtb+RgGz/tn8kQGkqyypIwLW/kYBs/23/3H5pKssqSMC1v5GAbP+2fyRAldUz1ZGAbP9IwLW/SIDav5XVM9WRgGz/SMC1v7Z/JEBpKssqkYBs/0jAtb+2fyRAldUz1UjAtb+RgGz/bf/cfpXVM9WRgGz/SMC1v0iA2r9pKssqSIDav2kqyypIgNq/ldUz1bZ/JEBpKssqtn8kQJXVM9X/f/9//////7Z/JECV1TPV/3//f/////+2fyRAaSrLKkiA2r9IgNq/aSrLKmkqyypIgNq/SIDav5XVM9WV1TPVtn8kQLZ/JEBpKssqaSrLKrZ/JEC2fyRAldUz1ZXVM9WRgGz/SMC1v7Z/JEBpKssqkYBs/0jAtb9IgNq/aSrLKv9//3//////tn8kQGkqyyr/f/9//////0iA2r9pKssqkYBs/0jAtb+2fyRAldUz1f9//3//////tn8kQJXVM9WRgGz/SMC1v0iA2r+V1TPV/3//f/////9IgNq/ldUz1ZGAbP9IwLW/tn8kQJXVM9WRgGz/SMC1v0iA2r+V1TPV/3//f/////9IgNq/ldUz1ZGAbP9IwLW/SIDav2kqyyr/f/9//////0iA2r9pKssqkYBs/0jAtb+2fyRAaSrLKkiA2r9IgNq/aSrLKmkqyypIgNq/SIDav5XVM9WV1TPVtn8kQLZ/JEBpKssqaSrLKrZ/JEC2fyRAldUz1ZXVM9WRgGz/SMC1v7Z/JEBpKssqSMC1v5GAbP9IgNq/aSrLKv9//3//////tn8kQGkqyyr//////3//f0iA2r9pKssqkYBs/0jAtb+2fyRAldUz1f9//3//////tn8kQJXVM9WRgGz/SMC1v0iA2r+V1TPV/3//f/////9IgNq/ldUz1UjAtb+RgGz/tn8kQJXVM9WRgGz/SMC1v0iA2r+V1TPV//////9//3+2fyRAldUz1f9//3//////SIDav5XVM9WRgGz/SMC1v0iA2r9pKssq/3//f/////9IgNq/aSrLKv9//3//////tn8kQGkqyyqRgGz/SMC1v7Z/JEBpKssq")
}, {
"aabb": AABB(-0.7, 1.95804, -2.12175, 1.4, 0.964132, 0.23367),
"attribute_data": PackedByteArray("L7EyPpzpNz8vsTI+nOk3Py+xMj6c6Tc/LbEyPpzpNz8tsTI+nOk3Py2xMj6c6Tc/M7EyPpzpNz8zsTI+nOk3PzOxMj6c6Tc/LbEyPpzpNz8tsTI+nOk3Py2xMj6c6Tc/L7EyPpzpNz8vsTI+nOk3Py+xMj6c6Tc/LbEyPpzpNz8tsTI+nOk3Py2xMj6c6Tc/M7EyPpzpNz8zsTI+nOk3PzOxMj6c6Tc/LbEyPpzpNz8tsTI+nOk3Py2xMj6c6Tc/GbUyPp/pNz8ZtTI+n+k3Pxm1Mj6f6Tc/FbUyPp7pNz8VtTI+nuk3PxW1Mj6e6Tc/G7UyPp7pNz8btTI+nuk3Pxu1Mj6e6Tc/FbUyPp7pNz8VtTI+nuk3PxW1Mj6e6Tc/GbUyPp/pNz8ZtTI+n+k3Pxm1Mj6f6Tc/FbUyPp7pNz8VtTI+nuk3PxW1Mj6e6Tc/G7UyPp7pNz8btTI+nuk3Pxu1Mj6e6Tc/FbUyPp7pNz8VtTI+nuk3PxW1Mj6e6Tc/"),
"format": 34359742487,
"index_count": 72,
"index_data": PackedByteArray("BgADAAoABgABAAMAEwAQAAwAEwAVABAACwARABcACwAFABEAAgAUAA4AAgAIABQABwAWABIABwAJABYABAANAA8ABAAAAA0AHgAbACEAHgAYABsAKgAnACQAKgAtACcAHAAlACgAHAAZACUAIwApAC8AIwAdACkAGgAsACYAGgAgACwAHwAuACsAHwAiAC4A"),
"material": SubResource("StandardMaterial3D_djaui"),
"name": "Material.001",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 48,
"vertex_data": PackedByteArray("MjMzv6LRB0DGygfAMjMzv6LRB0DGygfAMjMzv6LRB0DGygfAMjMzP6LRB0DGygfAMjMzP6LRB0DGygfAMjMzP6LRB0DGygfAMjMzv9YEO0DGygfAMjMzv9YEO0DGygfAMjMzv9YEO0DGygfAMjMzP9YEO0DGygfAMjMzP9YEO0DGygfAMjMzP9YEO0DGygfAMjMzv6LRB0BSAgXAMjMzv6LRB0BSAgXAMjMzv6LRB0BSAgXAMjMzP6LRB0BSAgXAMjMzP6LRB0BSAgXAMjMzP6LRB0BSAgXAMjMzv9YEO0BSAgXAMjMzv9YEO0BSAgXAMjMzv9YEO0BSAgXAMjMzP9YEO0BSAgXAMjMzP9YEO0BSAgXAMjMzP9YEO0BSAgXAMjMzv/2g+j+OPfe/MjMzv/2g+j+OPfe/MjMzv/2g+j+OPfe/MjMzP/2g+j+OPfe/MjMzP/2g+j+OPfe/MjMzP/2g+j+OPfe/MjMzv7GDMECOPfe/MjMzv7GDMECOPfe/MjMzv7GDMECOPfe/MjMzP7GDMECOPfe/MjMzP7GDMECOPfe/MjMzP7GDMECOPfe/MjMzv/2g+j+krPG/MjMzv/2g+j+krPG/MjMzv/2g+j+krPG/MjMzP/2g+j+krPG/MjMzP/2g+j+krPG/MjMzP/2g+j+krPG/MjMzv7GDMECkrPG/MjMzv7GDMECkrPG/MjMzv7GDMECkrPG/MjMzP7GDMECkrPG/MjMzP7GDMECkrPG/MjMzP7GDMECkrPG//3///////78PgP9/////P////3////+//38PgP///z//f///////vwAA/3////+//3/vf////z//fwAA////v////3////+//38AAP///7/vf/9/////PwAA/3////+/7v///////7//f///////v////3////+//3///////78AAO7/////vwAA/3////+//38AAP///7///+7/////v////3////+/7v8AAP///7//fwAA////vwAA/3////+//3//f4suuij/f+7/////P+7/D4D///+//3//f4suuijvf+7/////vxAA/3////8//3//f4suuigPgBAA////v+7//3////8//3//f////z//fxAA////PxAA73////+//////4suRNcPgO7/////v///D4D///+//////4suRNcPgP//////vxAAD4D///+//////4suRNcPgAAA////v+7/73////+//////////7/vfxAA////vwAAD4D///+/")
}]
blend_shape_mode = 0
shadow_mesh = SubResource("ArrayMesh_o3f7c")

[sub_resource type="BoxShape3D" id="BoxShape3D_g56d2"]
size = Vector3(4, 4, 0.8)

[node name="WallWindowSlideOpen" type="MeshInstance3D"]
mesh = SubResource("ArrayMesh_u24jy")
skeleton = NodePath("")

[node name="StaticBody3D" type="StaticBody3D" parent="."]
collision_layer = 2

[node name="CollisionShape3D" type="CollisionShape3D" parent="StaticBody3D" groups=["VisibleGroup0"]]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -0.00026238, 2, -2.00028)
shape = SubResource("BoxShape3D_g56d2")
