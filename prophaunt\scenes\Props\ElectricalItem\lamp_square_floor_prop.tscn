[gd_scene load_steps=4 format=3 uid="uid://d22pnldrxcwcm"]

[ext_resource type="PackedScene" uid="uid://cy82bsxfj1ddv" path="res://prophaunt/scenes/PropObject.tscn" id="1_w6kt4"]
[ext_resource type="PackedScene" uid="uid://b66fffvxvmc0e" path="res://prophaunt/maps/Source/ElectricalItem/lamp_square_floor.tscn" id="2_v2sf0"]

[sub_resource type="BoxShape3D" id="BoxShape3D_bx0gf"]
size = Vector3(0.36525, 2.58769, 0.386523)

[node name="LampSquareFloorProp" instance=ExtResource("1_w6kt4")]

[node name="LampSquareFloor" parent="Meshes" index="0" instance=ExtResource("2_v2sf0")]

[node name="CollisionShape3D" type="CollisionShape3D" parent="StaticBody3D" index="0"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0.00131985, 1.2918, 0.00410156)
shape = SubResource("BoxShape3D_bx0gf")
