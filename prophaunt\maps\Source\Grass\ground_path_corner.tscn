[gd_scene load_steps=5 format=4 uid="uid://qoi5oqiriwe"]

[ext_resource type="Material" uid="uid://dkmcjc7wc3ws6" path="res://prophaunt/Mat/GrassPath.tres" id="1_i1gf0"]

[sub_resource type="ArrayMesh" id="ArrayMesh_l4vbl"]
_surfaces = [{
"aabb": AABB(-1, -0.1, -1, 2, 0.1, 2),
"format": 34896613377,
"index_count": 66,
"index_data": PackedByteArray("AgAAAAEAAQADAAIAAwAEAAIABAAFAAIABAAGAAUACQAHAAgACAAKAAkACAALAAoACwAMAAoACwANAAwADQAOAAwAAwAMAA4ADgAEAAMAAQAKAAwADAADAAEAAAAJAAoACgABAAAAEAAPAAsACwAIABAACAARABAACAAHABEADwANAAsA"),
"lods": [0.325572, PackedByteArray("AgAAAAEAAQAEAAIABAAFAAIABAAGAAUACQAHABAAEAAHABEAEAABAAkAAAAJAAEAAQAQAA4ADgAEAAEADQAOABAADwANABAA")],
"name": "Material",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 18,
"vertex_data": PackedByteArray("jLcAAAAAAAD1ngAAxlsAAHJIAAAAAAAAxlsAAPWeAAAAAAAAjLcAAAAAAAAAAAAAAAAAAHJIAAAF4f7/AAAAAN/C/v+CcAAAa8f+/wAAAAC0rP7/tWMAAIJw/v/fwgAAtWP+/7SsAAAAAP7/BeEAAAAA/v9rxwAAAAD/////AAD///////8AAP///v8AAAAA")
}]
blend_shape_mode = 0

[sub_resource type="ArrayMesh" id="ArrayMesh_og01p"]
resource_name = "GroundPathCorner_GroundPathCorner"
_surfaces = [{
"aabb": AABB(-1, -0.1, -1, 2, 0.1, 2),
"attribute_data": PackedByteArray("pgGegVH8r9qUPzaS5NF/zeTRf82mAZfM62yMv4SyS62Eskutg317/YN9e/0+plCCpgF7/aYBe/2JMv/9DPsG8HjGMOBs+wnkbPsJ5NnMx9XZzMfV2czH1YCfOrgLqjeyC6o3sguqN7LWkCyDrZwJg62cCYNQgkeDUILG+576hP7k0X/NPqZQgg=="),
"format": 34896613399,
"index_count": 66,
"index_data": PackedByteArray("BQAAAAIAAgAGAAUABgAJAAUACQAMAAUACgAOAA0AEQAPABAAEAATABEAEAAWABMAFgAXABMAFgAaABcAGgAbABcABwAYABwAHAALAAcAAwAUABkAGQAIAAMAAQASABUAFQAEAAEAHgAdABYAFgAQAB4AEAAfAB4AEAAPAB8AHQAaABYA"),
"lods": [0.325572, PackedByteArray("BQAAAAIAAgAJAAUACQAMAAUACgAOAA0AEQAPAB4AHgAPAB8AHgAgABEAAQARACAAIAAeABsAGwAhACAAGgAbAB4AHQAaAB4A")],
"material": ExtResource("1_i1gf0"),
"name": "Material",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 34,
"vertex_data": PackedByteArray("jLcAAAAAVNWMtwAAAACD6/WeAADGW1TV9Z4AAMZbQ+31ngAAxlt/63JIAAAAAFTVxlsAAPWeVNXGWwAA9Z6l6cZbAAD1ntrtAAAAAIy3VNUAAAAAjLdU1QAAAACMtzbqAAAAAAAAVNUAAAAAAABU1QAAAABySP//BeH+/wAAGtbfwv7/gnDm1WvH/v8AABnWa8f+/wAAhOu0rP7/tWPv1bSs/v+1YzDttKz+/7Vjf+uCcP7/38J/1bVj/v+0rH3VtWP+/7Ssk+m1Y/7/tKzB7QAA/v8F4STVAAD+/2vHENUAAP7/a8ce6gAA/////zbV////////tdX///7/AAAH1vWeAADGW8DZAAAAAIy3ydSqqqqqceOtNKqqqqpz2/42deOxNKqqqqqqqqqqg9JJN8jaODaqqqqq/38AAKnRJTapqqqq/38AAP+//3/d1L0p/NT7Kd7Uvylw46w099TwKYjbGDd047A0OtV2KjvVeCqf0m435NpYNnHV5Cp91f0qzNFVNmbVzioa1TUq6dTUKUbY1zm30/g9")
}]
blend_shape_mode = 0
shadow_mesh = SubResource("ArrayMesh_l4vbl")

[sub_resource type="ConcavePolygonShape3D" id="ConcavePolygonShape3D_xb2ev"]
data = PackedVector3Array(-0.434, -0.1, -1, 0.434, -0.1, -1, 0.2419, -0.1, -0.283, 0.2419, -0.1, -0.283, -0.283, -0.1, 0.2419, -0.434, -0.1, -1, -0.283, -0.1, 0.2419, -1, -0.1, 0.434, -0.434, -0.1, -1, -1, -0.1, 0.434, -1, -0.1, -1, -0.434, -0.1, -1, -1, -0.1, 0.434, -1, -0.1, -0.434, -1, -0.1, -1, 0.558, 0, -1, 0.758, 0, -1, 0.5225, 0, -0.121, 0.5225, 0, -0.121, 0.3493, 0, -0.221, 0.558, 0, -1, 0.5225, 0, -0.121, -0.121, 0, 0.5225, 0.3493, 0, -0.221, -0.121, 0, 0.5225, -0.221, 0, 0.3493, 0.3493, 0, -0.221, -0.121, 0, 0.5225, -1, 0, 0.758, -0.221, 0, 0.3493, -1, 0, 0.758, -1, 0, 0.558, -0.221, 0, 0.3493, -0.283, -0.1, 0.2419, -0.221, 0, 0.3493, -1, 0, 0.558, -1, 0, 0.558, -1, -0.1, 0.434, -0.283, -0.1, 0.2419, 0.2419, -0.1, -0.283, 0.3493, 0, -0.221, -0.221, 0, 0.3493, -0.221, 0, 0.3493, -0.283, -0.1, 0.2419, 0.2419, -0.1, -0.283, 0.434, -0.1, -1, 0.558, 0, -1, 0.3493, 0, -0.221, 0.3493, 0, -0.221, 0.2419, -0.1, -0.283, 0.434, -0.1, -1, 1, 0, 1, -1, 0, 1, -0.121, 0, 0.5225, -0.121, 0, 0.5225, 0.5225, 0, -0.121, 1, 0, 1, 0.5225, 0, -0.121, 1, 0, -1, 1, 0, 1, 0.5225, 0, -0.121, 0.758, 0, -1, 1, 0, -1, -1, 0, 1, -1, 0, 0.758, -0.121, 0, 0.5225)

[node name="GroundPathCorner" type="MeshInstance3D"]
mesh = SubResource("ArrayMesh_og01p")
skeleton = NodePath("")

[node name="StaticBody3D" type="StaticBody3D" parent="."]

[node name="CollisionShape3D" type="CollisionShape3D" parent="StaticBody3D"]
shape = SubResource("ConcavePolygonShape3D_xb2ev")
