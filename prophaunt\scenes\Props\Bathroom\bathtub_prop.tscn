[gd_scene load_steps=4 format=3 uid="uid://cq6hjfvjkvvgi"]

[ext_resource type="PackedScene" uid="uid://cy82bsxfj1ddv" path="res://prophaunt/scenes/PropObject.tscn" id="1_4yy0c"]
[ext_resource type="PackedScene" uid="uid://cj4a75x0kkmj7" path="res://prophaunt/maps/Source/Bathroom/bathtub.tscn" id="2_dlyo1"]

[sub_resource type="BoxShape3D" id="BoxShape3D_f8ox4"]
size = Vector3(3.99548, 1.42169, 1.88716)

[node name="BathtubProp" instance=ExtResource("1_4yy0c")]

[node name="Bathtub" parent="Meshes" index="0" instance=ExtResource("2_dlyo1")]

[node name="CollisionShape3D" type="CollisionShape3D" parent="StaticBody3D" index="0"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -0.00186926, 0.710849, 0.00173661)
shape = SubResource("BoxShape3D_f8ox4")
