[gd_resource type="VisualShader" load_steps=44 format=3 uid="uid://tdq8wuw4yrma"]

[ext_resource type="Texture2D" uid="uid://d1wlaw220he2x" path="res://assets/characters/Goku/Aura.png" id="1_b4670"]

[sub_resource type="VisualShaderNodeFloatOp" id="VisualShaderNodeFloatOp_evvef"]
output_port_for_preview = 0
default_input_values = [0, 0.0, 1, 3.0]
operator = 3

[sub_resource type="Gradient" id="Gradient_d81a7"]

[sub_resource type="GradientTexture2D" id="GradientTexture2D_p5qhc"]
gradient = SubResource("Gradient_d81a7")
fill_from = Vector2(0, 1)
fill_to = Vector2(0, 0)

[sub_resource type="VisualShaderNodeTexture" id="VisualShaderNodeTexture_1wtw3"]
output_port_for_preview = 0
texture = SubResource("GradientTexture2D_p5qhc")

[sub_resource type="VisualShaderNodeFloatOp" id="VisualShaderNodeFloatOp_tiphw"]
output_port_for_preview = 0
operator = 2

[sub_resource type="VisualShaderNodeVectorDecompose" id="VisualShaderNodeVectorDecompose_6kyc4"]
default_input_values = [0, Quaternion(0, 0, 0, 0)]
op_type = 2

[sub_resource type="VisualShaderNodeColorParameter" id="VisualShaderNodeColorParameter_v6btc"]
parameter_name = "ColorParameter"

[sub_resource type="VisualShaderNodeVectorFunc" id="VisualShaderNodeVectorFunc_wh2io"]
output_port_for_preview = 0
default_input_values = [0, Vector2(0, 0)]
op_type = 0

[sub_resource type="VisualShaderNodeInput" id="VisualShaderNodeInput_o5hei"]
input_name = "uv"

[sub_resource type="VisualShaderNodeVectorOp" id="VisualShaderNodeVectorOp_m4frg"]
default_input_values = [0, Quaternion(0, 0, 0, 0), 1, Quaternion(0, 0, 0, 0)]
op_type = 2
operator = 2

[sub_resource type="VisualShaderNodeVectorOp" id="VisualShaderNodeVectorOp_14uxq"]
output_port_for_preview = 0
default_input_values = [0, Vector2(0, 0), 1, Vector2(0, 0)]
op_type = 0
operator = 1

[sub_resource type="VisualShaderNodeVectorOp" id="VisualShaderNodeVectorOp_bqb40"]
output_port_for_preview = 0
default_input_values = [0, Vector2(0, 0), 1, Vector2(0, 0)]
op_type = 0
operator = 1

[sub_resource type="VisualShaderNodeFloatConstant" id="VisualShaderNodeFloatConstant_wme8l"]
constant = 10.0

[sub_resource type="VisualShaderNodeVectorOp" id="VisualShaderNodeVectorOp_np7e6"]
output_port_for_preview = 0
default_input_values = [0, Vector2(0, 0), 1, Vector2(0, 0)]
op_type = 0
operator = 2

[sub_resource type="VisualShaderNodeVec2Constant" id="VisualShaderNodeVec2Constant_vqvqn"]
constant = Vector2(0.5, 0.5)

[sub_resource type="VisualShaderNodeInput" id="VisualShaderNodeInput_p74qj"]
input_name = "time"

[sub_resource type="VisualShaderNodeFloatFunc" id="VisualShaderNodeFloatFunc_lmyi0"]
function = 0

[sub_resource type="VisualShaderNodeFloatOp" id="VisualShaderNodeFloatOp_77ww8"]
operator = 2

[sub_resource type="VisualShaderNodeFloatOp" id="VisualShaderNodeFloatOp_ht2tw"]
operator = 2

[sub_resource type="VisualShaderNodeFloatConstant" id="VisualShaderNodeFloatConstant_roxrk"]
constant = 0.02

[sub_resource type="VisualShaderNodeVectorDecompose" id="VisualShaderNodeVectorDecompose_yr0tg"]
default_input_values = [0, Quaternion(0, 0, 0, 0)]
op_type = 2

[sub_resource type="VisualShaderNodeTexture" id="VisualShaderNodeTexture_e7vqr"]
output_port_for_preview = 0
source = 5
texture = ExtResource("1_b4670")

[sub_resource type="VisualShaderNodeTexture2DParameter" id="VisualShaderNodeTexture2DParameter_3n7ll"]
parameter_name = "Texture2DParameter"
texture_repeat = 2

[sub_resource type="VisualShaderNodeFloatConstant" id="VisualShaderNodeFloatConstant_o5dr8"]
constant = 5.0

[sub_resource type="VisualShaderNodeVec2Constant" id="VisualShaderNodeVec2Constant_ikw0n"]
constant = Vector2(0.5, 1.5)

[sub_resource type="VisualShaderNodeInput" id="VisualShaderNodeInput_35ss8"]
input_name = "time"

[sub_resource type="VisualShaderNodeFloatOp" id="VisualShaderNodeFloatOp_d8s04"]
operator = 2

[sub_resource type="CompressedTexture2D" id="CompressedTexture2D_eghox"]

[sub_resource type="VisualShaderNodeTexture" id="VisualShaderNodeTexture_c4che"]
output_port_for_preview = 0
source = 5
texture = SubResource("CompressedTexture2D_eghox")

[sub_resource type="VisualShaderNodeFloatConstant" id="VisualShaderNodeFloatConstant_vbuj2"]
constant = 2.0

[sub_resource type="VisualShaderNodeFloatFunc" id="VisualShaderNodeFloatFunc_hvxgf"]
function = 2

[sub_resource type="VisualShaderNodeVectorOp" id="VisualShaderNodeVectorOp_ib3ej"]
output_port_for_preview = 0
default_input_values = [0, Vector2(0, 0), 1, Vector2(0, 0)]
op_type = 0
operator = 1

[sub_resource type="VisualShaderNodeFloatOp" id="VisualShaderNodeFloatOp_3w0me"]
operator = 2

[sub_resource type="VisualShaderNodeVectorOp" id="VisualShaderNodeVectorOp_8jclh"]
output_port_for_preview = 0
default_input_values = [0, Vector2(0, 0), 1, Vector2(0, 0)]
op_type = 0
operator = 2

[sub_resource type="VisualShaderNodeVectorFunc" id="VisualShaderNodeVectorFunc_wskvl"]
output_port_for_preview = 0
default_input_values = [0, Vector2(0, 0)]
op_type = 0

[sub_resource type="VisualShaderNodeVectorOp" id="VisualShaderNodeVectorOp_t2k33"]
output_port_for_preview = 0
default_input_values = [0, Vector2(0, 0), 1, Vector2(0, 0)]
op_type = 0
operator = 1

[sub_resource type="VisualShaderNodeTexture2DParameter" id="VisualShaderNodeTexture2DParameter_wpv7r"]
parameter_name = "Texture2DParameter2"
texture_repeat = 1

[sub_resource type="VisualShaderNodeInput" id="VisualShaderNodeInput_y1q07"]
input_name = "uv"

[sub_resource type="VisualShaderNodeVectorOp" id="VisualShaderNodeVectorOp_of5ux"]
default_input_values = [0, Vector2(0, 0), 1, Vector2(0, 0)]
op_type = 0

[sub_resource type="VisualShaderNodeUVFunc" id="VisualShaderNodeUVFunc_has83"]
default_input_values = [1, Vector2(2, 2), 2, Vector2(0, 0)]

[sub_resource type="VisualShaderNodeVectorDecompose" id="VisualShaderNodeVectorDecompose_3fbnb"]
output_port_for_preview = 0
default_input_values = [0, Quaternion(0, 0, 0, 0)]
op_type = 2

[sub_resource type="VisualShaderNodeBillboard" id="VisualShaderNodeBillboard_5f3h8"]
billboard_type = 2

[resource]
code = "shader_type spatial;
render_mode blend_mix, depth_draw_opaque, cull_disabled, diffuse_lambert, specular_schlick_ggx, unshaded;

uniform sampler2D Texture2DParameter2 : repeat_enable;
uniform sampler2D tex_frg_23;
uniform sampler2D Texture2DParameter : repeat_disable;
uniform vec4 ColorParameter : source_color;



void vertex() {
	mat4 n_out2p0;
// GetBillboardMatrix:2
	{
		mat4 __mvm = VIEW_MATRIX * mat4(INV_VIEW_MATRIX[0], MODEL_MATRIX[1], vec4(normalize(cross(INV_VIEW_MATRIX[0].xyz, MODEL_MATRIX[1].xyz)), 0.0), MODEL_MATRIX[3]);
		__mvm = __mvm * mat4(vec4(1.0, 0.0, 0.0, 0.0), vec4(0.0, 1.0 / length(MODEL_MATRIX[1].xyz), 0.0, 0.0), vec4(0.0, 0.0, 1.0, 0.0), vec4(0.0, 0.0, 0.0, 1.0));
		n_out2p0 = __mvm;
	}


// Output:0
	MODELVIEW_MATRIX = n_out2p0;


}

void fragment() {
// Input:39
	vec2 n_out39p0 = UV;


// Vector2Constant:44
	vec2 n_out44p0 = vec2(0.500000, 0.500000);


// VectorOp:40
	vec2 n_out40p0 = n_out39p0 - n_out44p0;


// VectorFunc:31
	vec2 n_out31p0 = normalize(n_out40p0);


// FloatConstant:42
	float n_out42p0 = 10.000000;


// Input:45
	float n_out45p0 = TIME;


// FloatOp:47
	float n_out47p0 = n_out42p0 * n_out45p0;


// FloatFunc:46
	float n_out46p0 = sin(n_out47p0);


// FloatConstant:49
	float n_out49p0 = 0.020000;


// FloatOp:48
	float n_out48p0 = n_out46p0 * n_out49p0;


// VectorOp:43
	vec2 n_out43p0 = n_out31p0 * vec2(n_out48p0);


// VectorOp:41
	vec2 n_out41p0 = n_out39p0 - n_out43p0;


// Input:69
	vec2 n_out69p0 = UV;


// Vector2Constant:57
	vec2 n_out57p0 = vec2(0.500000, 1.500000);


// VectorOp:63
	vec2 n_out63p0 = n_out69p0 - n_out57p0;


// VectorFunc:66
	vec2 n_out66p0 = normalize(n_out63p0);


// FloatConstant:56
	float n_out56p0 = 5.000000;


// Input:58
	float n_out58p0 = TIME;


// FloatOp:59
	float n_out59p0 = n_out56p0 * n_out58p0;


// FloatFunc:61
	float n_out61p0 = tan(n_out59p0);


// FloatConstant:60
	float n_out60p0 = 2.000000;


// FloatOp:64
	float n_out64p0 = n_out61p0 * n_out60p0;


// VectorOp:65
	vec2 n_out65p0 = n_out66p0 * vec2(n_out64p0);


// VectorOp:67
	vec2 n_out67p0 = n_out69p0 - n_out65p0;


	vec4 n_out6p0;
// Texture2D:6
	n_out6p0 = texture(Texture2DParameter2, n_out67p0);


// VectorDecompose:9
	float n_out9p0 = n_out6p0.x;
	float n_out9p1 = n_out6p0.y;
	float n_out9p2 = n_out6p0.z;
	float n_out9p3 = n_out6p0.w;


// FloatOp:12
	float n_in12p1 = 3.00000;
	float n_out12p0 = n_out9p0 / n_in12p1;


// Texture2D:23
	vec4 n_out23p0 = texture(tex_frg_23, UV);


// VectorDecompose:25
	float n_out25p0 = n_out23p0.x;
	float n_out25p1 = n_out23p0.y;
	float n_out25p2 = n_out23p0.z;
	float n_out25p3 = n_out23p0.w;


// FloatOp:24
	float n_out24p0 = n_out12p0 * n_out25p0;


// VectorOp:7
	vec2 n_out7p0 = n_out41p0 + vec2(n_out24p0);


	vec4 n_out54p0;
// Texture2D:54
	n_out54p0 = texture(Texture2DParameter, n_out7p0);


// ColorParameter:28
	vec4 n_out28p0 = ColorParameter;


// VectorOp:4
	vec4 n_out4p0 = n_out54p0 * n_out28p0;


// VectorDecompose:5
	float n_out5p0 = n_out54p0.x;
	float n_out5p1 = n_out54p0.y;
	float n_out5p2 = n_out54p0.z;
	float n_out5p3 = n_out54p0.w;


// Output:0
	ALBEDO = vec3(n_out4p0.xyz);
	ALPHA = n_out5p0;


}
"
modes/cull = 2
flags/unshaded = true
nodes/vertex/2/node = SubResource("VisualShaderNodeBillboard_5f3h8")
nodes/vertex/2/position = Vector2(-80, 440)
nodes/vertex/connections = PackedInt32Array(2, 0, 0, 10)
nodes/fragment/0/position = Vector2(1140, 140)
nodes/fragment/4/node = SubResource("VisualShaderNodeVectorOp_m4frg")
nodes/fragment/4/position = Vector2(940, -280)
nodes/fragment/5/node = SubResource("VisualShaderNodeVectorDecompose_yr0tg")
nodes/fragment/5/position = Vector2(560, 320)
nodes/fragment/6/node = SubResource("VisualShaderNodeTexture_c4che")
nodes/fragment/6/position = Vector2(-1210, 660)
nodes/fragment/7/node = SubResource("VisualShaderNodeVectorOp_of5ux")
nodes/fragment/7/position = Vector2(-760, -60)
nodes/fragment/9/node = SubResource("VisualShaderNodeVectorDecompose_3fbnb")
nodes/fragment/9/position = Vector2(-890, 660)
nodes/fragment/12/node = SubResource("VisualShaderNodeFloatOp_evvef")
nodes/fragment/12/position = Vector2(-660, 740)
nodes/fragment/23/node = SubResource("VisualShaderNodeTexture_1wtw3")
nodes/fragment/23/position = Vector2(-940, 1140)
nodes/fragment/24/node = SubResource("VisualShaderNodeFloatOp_tiphw")
nodes/fragment/24/position = Vector2(-260, 1020)
nodes/fragment/25/node = SubResource("VisualShaderNodeVectorDecompose_6kyc4")
nodes/fragment/25/position = Vector2(-580, 1240)
nodes/fragment/28/node = SubResource("VisualShaderNodeColorParameter_v6btc")
nodes/fragment/28/position = Vector2(460, -40)
nodes/fragment/31/node = SubResource("VisualShaderNodeVectorFunc_wh2io")
nodes/fragment/31/position = Vector2(-2440, 60)
nodes/fragment/39/node = SubResource("VisualShaderNodeInput_o5hei")
nodes/fragment/39/position = Vector2(-3220, -120)
nodes/fragment/40/node = SubResource("VisualShaderNodeVectorOp_14uxq")
nodes/fragment/40/position = Vector2(-2720, 60)
nodes/fragment/41/node = SubResource("VisualShaderNodeVectorOp_bqb40")
nodes/fragment/41/position = Vector2(-1720, -180)
nodes/fragment/42/node = SubResource("VisualShaderNodeFloatConstant_wme8l")
nodes/fragment/42/position = Vector2(-3120, 520)
nodes/fragment/43/node = SubResource("VisualShaderNodeVectorOp_np7e6")
nodes/fragment/43/position = Vector2(-1940, 200)
nodes/fragment/44/node = SubResource("VisualShaderNodeVec2Constant_vqvqn")
nodes/fragment/44/position = Vector2(-3220, 100)
nodes/fragment/45/node = SubResource("VisualShaderNodeInput_p74qj")
nodes/fragment/45/position = Vector2(-3200, 660)
nodes/fragment/46/node = SubResource("VisualShaderNodeFloatFunc_lmyi0")
nodes/fragment/46/position = Vector2(-2460, 560)
nodes/fragment/47/node = SubResource("VisualShaderNodeFloatOp_77ww8")
nodes/fragment/47/position = Vector2(-2720, 540)
nodes/fragment/48/node = SubResource("VisualShaderNodeFloatOp_ht2tw")
nodes/fragment/48/position = Vector2(-2160, 680)
nodes/fragment/49/node = SubResource("VisualShaderNodeFloatConstant_roxrk")
nodes/fragment/49/position = Vector2(-2460, 740)
nodes/fragment/54/node = SubResource("VisualShaderNodeTexture_e7vqr")
nodes/fragment/54/position = Vector2(90, -180)
nodes/fragment/55/node = SubResource("VisualShaderNodeTexture2DParameter_3n7ll")
nodes/fragment/55/position = Vector2(-410, 180)
nodes/fragment/56/node = SubResource("VisualShaderNodeFloatConstant_o5dr8")
nodes/fragment/56/position = Vector2(-3190, 1530)
nodes/fragment/57/node = SubResource("VisualShaderNodeVec2Constant_ikw0n")
nodes/fragment/57/position = Vector2(-3290, 1110)
nodes/fragment/58/node = SubResource("VisualShaderNodeInput_35ss8")
nodes/fragment/58/position = Vector2(-3270, 1670)
nodes/fragment/59/node = SubResource("VisualShaderNodeFloatOp_d8s04")
nodes/fragment/59/position = Vector2(-2790, 1550)
nodes/fragment/60/node = SubResource("VisualShaderNodeFloatConstant_vbuj2")
nodes/fragment/60/position = Vector2(-2550, 1750)
nodes/fragment/61/node = SubResource("VisualShaderNodeFloatFunc_hvxgf")
nodes/fragment/61/position = Vector2(-2560, 1550)
nodes/fragment/63/node = SubResource("VisualShaderNodeVectorOp_ib3ej")
nodes/fragment/63/position = Vector2(-2790, 1070)
nodes/fragment/64/node = SubResource("VisualShaderNodeFloatOp_3w0me")
nodes/fragment/64/position = Vector2(-2280, 1650)
nodes/fragment/65/node = SubResource("VisualShaderNodeVectorOp_8jclh")
nodes/fragment/65/position = Vector2(-2010, 1210)
nodes/fragment/66/node = SubResource("VisualShaderNodeVectorFunc_wskvl")
nodes/fragment/66/position = Vector2(-2510, 1070)
nodes/fragment/67/node = SubResource("VisualShaderNodeVectorOp_t2k33")
nodes/fragment/67/position = Vector2(-1790, 830)
nodes/fragment/68/node = SubResource("VisualShaderNodeTexture2DParameter_wpv7r")
nodes/fragment/68/position = Vector2(-1560, 980)
nodes/fragment/69/node = SubResource("VisualShaderNodeInput_y1q07")
nodes/fragment/69/position = Vector2(-3320, 890)
nodes/fragment/70/node = SubResource("VisualShaderNodeUVFunc_has83")
nodes/fragment/70/position = Vector2(-1590, 620)
nodes/fragment/connections = PackedInt32Array(23, 0, 25, 0, 25, 0, 24, 1, 28, 0, 4, 1, 9, 0, 12, 0, 6, 0, 9, 0, 12, 0, 24, 0, 40, 0, 31, 0, 31, 0, 43, 0, 43, 0, 41, 1, 44, 0, 40, 1, 5, 0, 0, 1, 42, 0, 47, 0, 45, 0, 47, 1, 49, 0, 48, 1, 46, 0, 48, 0, 47, 0, 46, 0, 48, 0, 43, 1, 54, 0, 4, 0, 54, 0, 5, 0, 55, 0, 54, 2, 39, 0, 40, 0, 39, 0, 41, 0, 41, 0, 7, 0, 24, 0, 7, 1, 7, 0, 54, 0, 63, 0, 66, 0, 56, 0, 59, 0, 58, 0, 59, 1, 60, 0, 64, 1, 59, 0, 61, 0, 68, 0, 6, 2, 61, 0, 64, 0, 69, 0, 67, 0, 66, 0, 65, 0, 69, 0, 63, 0, 57, 0, 63, 1, 64, 0, 65, 1, 65, 0, 67, 1, 4, 0, 0, 0, 67, 0, 6, 0)
