[gd_scene load_steps=12 format=4 uid="uid://dvgp1wmt4d53y"]

[ext_resource type="PackedScene" uid="uid://cy82bsxfj1ddv" path="res://prophaunt/scenes/PropObject.tscn" id="1_ss0c1"]
[ext_resource type="Texture2D" uid="uid://cq8xh43fvjb7s" path="res://Scenes/FreeRide/Assets/Buildings/Restaurant/restaurant.png" id="2_i43vu"]
[ext_resource type="Material" uid="uid://bay0yvspay8sg" path="res://assets/characters/Mat/NextPassMat.tres" id="3_po7un"]

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_s3oqj"]
cull_mode = 1
albedo_color = Color(0, 0, 0, 1)
grow = true
grow_amount = 0.02

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_7n6n0"]
resource_name = "restaurant"
next_pass = SubResource("StandardMaterial3D_s3oqj")
cull_mode = 2
diffuse_mode = 3
vertex_color_use_as_albedo = true
albedo_texture = ExtResource("2_i43vu")
roughness = 0.14
backlight_enabled = true
backlight = Color(0.435294, 0.0980392, 0.0509804, 1)

[sub_resource type="ArrayMesh" id="ArrayMesh_a533n"]
_surfaces = [{
"aabb": AABB(-1, -1, -0.799998, 2, 2.00002, 0.8),
"format": 34359742465,
"index_count": 396,
"index_data": PackedByteArray("BQAHABAAAQAEAAMAAgABAAMAAgAAAAEACAACAAMACAAJAAIABgABAAAABgAFAAEABgACAAkABgAAAAIABwAFAAgABgAIAAUABgAJAAgABAABAAwADAABAAsABQANAAEABQARAA0ADQALAAEADQAKAAsAOwBHAD8AOwBEAEcACwAPAA4ACwAKAA8ABQAQAA4ADwAFAA4ADwARAAUAFwAiACAAEwAWABUAGgAUABUAGgAbABQAFAATABUAFAASABMAGAATABIAGAAXABMAGAAUABsAGAASABQAGQAVABYAGQAaABUAGAAaABcAGAAbABoAFgATAB4AIgAWAB4AIgAZABYAHgATAB0AFwAfABMAFwAjAB8AHwAdABMAHwAcAB0AIQAdABwAIQAgAB0AIgAdACAAIgAeAB0AIQAfACMAIQAcAB8AGQAiABcAIQAXACAAIQAjABcAGQAXABoAKQA0ADIAJQAoACcALAAmACcALAAtACYAJgAlACcAJgAkACUAKgAlACQAKgApACUAKgAmAC0AKgAkACYAKwAnACgAKwAsACcAKgAsACkAKgAtACwAKAAlADAANAAoADAANAArACgAMAAlAC8AKQAxACUAKQA1ADEAMQAvACUAMQAuAC8AMwAvAC4AMwAyAC8ANAAvADIANAAwAC8AMwAxADUAMwAuADEAKwA0ACkAMwApADIAMwA1ACkAKwApACwAFgAoABkAFgArACgANwA5ADgANwA2ADkACgARAA8ACgANABEAEAALAA4AEAAMAAsAAwAHAAgAAwAEAAcARQBJAEIARQBLAEkAPgBAAEoAPgBBAEAARgA+AEoARgA6AD4AQQA6ADwAQQA+ADoAPABGAD0APAA6AEYAPwBKAEAARABGAEMARQBDAEYARQBCAEMAQAA7AD8AQAA9ADsARgA7AD0ASQBKAEgASQBLAEoARgBEADsASgBHAEgARwBKAD8AQwBJAEgAQwBCAEkARQBKAEsARQBGAEoAQAA8AD0AQABBADwASABEAEMASABHAEQAEAAEAAwAEAAHAAQA"),
"name": "restaurant",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 76,
"vertex_data": PackedByteArray("ZAR4PxEAgD8uqr2+AACAP/vOdz+Y+8y+ZAR4PxEAgD8Ej++8AACAP/vOdz+9NwY2AACAPwAAQD+9NwY2AACAv/vOdz9eFM2+ZAR4vxEAgD8uqr2+AACAvwAAQD+9NwY2AACAv/vOdz+9NwY2ZAR4vxEAgD8Ej++8ZAR4PxEAgD+/10S/AACAP/vOdz+rzEy/AACAPwAAQD+rzEy/ZAR4PxEAgD9cjdy+AACAv/vOdz+rzEy/ZAR4vxEAgD+/10S/AACAvwAAQD+rzEy/ZAR4vxEAgD9cjdy+MzNzPwZkN78uqr2+zcxsPwAAQL9EUM2+MzNzPwZkN78Ej++8zcxsPwAAQL+9NwY2AABAPwAAQL+9NwY23sxsPwAAQD/XE82+RDNzP/VjNz8uqr2+EQBAPwAAQD+9NwY23sxsPwAAQD+9NwY2RDNzP/VjNz8Ej++8MzNzPwZkN7+/10S/zcxsPwAAQL+rzEy/AABAPwAAQL+rzEy/MzNzPwZkN79cjdy+3sxsPwAAQD+rzEy/RDNzP/VjNz+/10S/EQBAPwAAQD+rzEy/RDNzP/VjNz9cjdy+MzNzvwZkNz8uqr2+zcxsvwAAQD9EUM2+MzNzvwZkNz8Ej++8zcxsvwAAQD+9NwY2AABAvwAAQD+9NwY23sxsvwAAQL/XE82+RDNzv/VjN78uqr2+EQBAvwAAQL+9NwY23sxsvwAAQL+9NwY2RDNzv/VjN78Ej++8MzNzvwZkNz+/10S/zcxsvwAAQD+rzEy/AABAvwAAQD+rzEy/MzNzvwZkNz9cjdy+3sxsvwAAQL+rzEy/RDNzv/VjN7+/10S/EQBAvwAAQL+rzEy/RDNzv/VjN79cjdy+EQBAPwAAQD/NzMy9AABAPwAAQL/NzMy9EQBAvwAAQL/NzMy9AABAvwAAQD/NzMy9UwR4PxkAgL9cjdy+AACAPwAAQL+rzEy/UwR4PxEAgL+/10S/AACAP/vOd7+rzEy/dQR4vwgAgL9cjdy+AACAvwAAQL+rzEy/AACAv+rOd7+rzEy/dQR4vwgAgL+/10S/UwR4PxkAgL8Ej++8AACAP/vOd7+9NwY2AACAPwAAQL+9NwY2UwR4PxEAgL8uqr2+AACAP/vOd79eFM2+AACAvwAAQL+9NwY2AACAv+rOd7+9NwY2dQR4vwgAgL8Ej++8AACAv+rOd7+Y+8y+dQR4vwgAgL8uqr2+")
}]
blend_shape_mode = 0

[sub_resource type="ArrayMesh" id="ArrayMesh_psvhb"]
resource_name = "FoodHolder_crate_Cube_177"
lightmap_size_hint = Vector2i(134, 130)
_surfaces = [{
"aabb": AABB(-1, -1, -0.799998, 2, 2.00001, 0.8),
"attribute_data": PackedByteArray("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"),
"format": ***********,
"index_count": 396,
"index_data": PackedByteArray("AAABAAIAAwAEAAUABgAHAAgABgAJAAcACgALAAwACgANAAsADgAPABAADgARAA8AEgATABQAEgAVABMAAQAAABYAFwAYABkAFwAaABgABAADABsAGwADABwAHQAeAB8AHQAgAB4AIQAiACMAIQAkACIAJQAmACcAJQAoACYAKQAqACsAKQAsACoAAAACAC0ALgAvADAALgAxAC8AMgAzADQANQA2ADcAOAA5ADoAOAA7ADkAPAA9AD4APAA/AD0AQABBAEIAQABDAEEARABFAEYARABHAEUASABJAEoASABLAEkATABNAE4ATABPAE0ANgA1AFAAUQBSAFMAUQBUAFIAUAA1AFUAVgBXAFgAVgBZAFcAWgBbAFwAWgBdAFsAXgBfAGAAXgBhAF8AYgBjAGQAYgBlAGMAZgBnAGgAZgBpAGcAagAzADIAawBsAG0AawBuAGwAagAyAG8AcABxAHIAcwB0AHUAdgB3AHgAdgB5AHcAegB7AHwAegB9AHsAfgB/AIAAfgCBAH8AggCDAIQAggCFAIMAhgCHAIgAhgCJAIcAigCLAIwAigCNAIsAdABzAI4AjwCQAJEAjwCSAJAAjgBzAJMAlACVAJYAlACXAJUAmACZAJoAmACbAJkAnACdAJ4AnACfAJ0AoAChAKIAoACjAKEApAClAKYApACnAKUAqABxAHAAqQCqAKsAqQCsAKoAqABwAK0ArgCvALAArgCxAK8AsgCzALQAsgC1ALMAtgC3ALgAtgC5ALcAugC7ALwAugC9ALsAvgC/AMAAvgDBAL8AwgDDAMQAwgDFAMMAxgDHAMgAxgDJAMcAygDLAMwAygDNAMsAzgDPANAAzgDRAM8A0gDTANQA0gDVANMA1gDXANgA2QDaANsA3ADdAN4A3ADfAN0A4ADhAOIA4ADjAOEA2gDkAOUA5gDnAOgA5gDpAOcA2gDZAOQA1wDqAOsA6gDXANYA7ADtAO4A7ADvAO0A8ADxAPIA8ADzAPEA9AD1APYA9AD3APUA+AD5APoA+AD7APkA/AD9AP4A/AD/AP0A"),
"material": SubResource("StandardMaterial3D_7n6n0"),
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 256,
"vertex_data": PackedByteArray("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")
}]
blend_shape_mode = 0
shadow_mesh = SubResource("ArrayMesh_a533n")

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_jhyii"]
resource_name = "restaurant"
next_pass = ExtResource("3_po7un")
cull_mode = 2
diffuse_mode = 3
vertex_color_use_as_albedo = true
albedo_texture = ExtResource("2_i43vu")
roughness = 0.0
backlight_enabled = true
backlight = Color(0.505882, 0.0784314, 0.0392157, 1)

[sub_resource type="ArrayMesh" id="ArrayMesh_vx42b"]
_surfaces = [{
"aabb": AABB(-0.363289, 0.*********, -0.404685, 0.754078, 0.777159, 0.820999),
"format": 34359742465,
"index_count": 774,
"index_data": PackedByteArray("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"),
"name": "restaurant",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 131,
"vertex_data": PackedByteArray("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")
}]
blend_shape_mode = 0

[sub_resource type="ArrayMesh" id="ArrayMesh_c8koh"]
resource_name = "Cheese_crate_cheese_Cube_001"
_surfaces = [{
"aabb": AABB(-0.363289, 0.*********, -0.404685, 0.754078, 0.777159, 0.820999),
"attribute_data": PackedByteArray("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"),
"format": ***********,
"index_count": 774,
"index_data": PackedByteArray("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"),
"material": SubResource("StandardMaterial3D_jhyii"),
"name": "restaurant",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 283,
"vertex_data": PackedByteArray("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")
}]
blend_shape_mode = 0
shadow_mesh = SubResource("ArrayMesh_vx42b")

[sub_resource type="BoxShape3D" id="BoxShape3D_1nqqm"]
size = Vector3(2.02954, 1.00952, 2.0448)

[node name="CheeseBasketProp" instance=ExtResource("1_ss0c1")]

[node name="FoodHolder" type="MeshInstance3D" parent="Meshes" index="0" groups=["VisibleGroupInterior"]]
transform = Transform3D(1, 0, 0, 0, 0, -1, 0, 1, 0, 0, 0, 0)
mesh = SubResource("ArrayMesh_psvhb")
skeleton = NodePath("")

[node name="Cheese" type="MeshInstance3D" parent="Meshes" index="1" groups=["VisibleGroup0"]]
transform = Transform3D(0.996846, -0.0708501, -0.0357496, 0.0664764, 0.991544, -0.111448, 0.0433433, 0.10872, 0.993127, -0.432823, 0.0952316, 0.496051)
mesh = SubResource("ArrayMesh_c8koh")
skeleton = NodePath("")

[node name="Cheese2" type="MeshInstance3D" parent="Meshes" index="2" groups=["VisibleGroup0"]]
transform = Transform3D(0.275229, -0.411283, 0.868963, -0.138138, 0.877574, 0.459111, -0.951403, -0.246397, 0.18472, -0.372243, 0.0952316, -0.371245)
mesh = SubResource("ArrayMesh_c8koh")
skeleton = NodePath("")

[node name="Cheese3" type="MeshInstance3D" parent="Meshes" index="3" groups=["VisibleGroup0"]]
transform = Transform3D(-0.105907, -0.801807, 0.588124, -0.470365, 0.561491, 0.680797, -0.876094, -0.204532, -0.436607, 0.0385769, 0.215473, 0.154675)
mesh = SubResource("ArrayMesh_c8koh")
skeleton = NodePath("")

[node name="Cheese4" type="MeshInstance3D" parent="Meshes" index="4" groups=["VisibleGroup0"]]
transform = Transform3D(-0.0179845, -0.777405, 0.628744, -0.470365, 0.561492, 0.680797, -0.882289, -0.283495, -0.375762, 0.463324, 0.215473, -0.326638)
mesh = SubResource("ArrayMesh_c8koh")
skeleton = NodePath("")

[node name="Cheese5" type="MeshInstance3D" parent="Meshes" index="5" groups=["VisibleGroup0"]]
transform = Transform3D(-0.812165, -0.553305, 0.185044, -0.316331, 0.684117, 0.657206, -0.490227, 0.475224, -0.730644, 0.463324, 0.215473, 0.349376)
mesh = SubResource("ArrayMesh_c8koh")
skeleton = NodePath("")

[node name="Cheese6" type="MeshInstance3D" parent="Meshes" index="6" groups=["VisibleGroup0"]]
transform = Transform3D(0.934315, 0.261045, 0.242715, -0.321965, 0.325875, 0.8889, 0.152948, -0.908659, 0.388517, 0.309198, 0.490829, 0.371978)
mesh = SubResource("ArrayMesh_c8koh")
skeleton = NodePath("")

[node name="CollisionShape3D" type="CollisionShape3D" parent="StaticBody3D" index="0"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -0.00549316, 0.504517, 0.00482178)
shape = SubResource("BoxShape3D_1nqqm")
