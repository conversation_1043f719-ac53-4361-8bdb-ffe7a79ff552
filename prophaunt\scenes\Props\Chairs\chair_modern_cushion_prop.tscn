[gd_scene load_steps=4 format=3 uid="uid://bf65dphgrng6f"]

[ext_resource type="PackedScene" uid="uid://cy82bsxfj1ddv" path="res://prophaunt/scenes/PropObject.tscn" id="1_wa2ar"]
[ext_resource type="PackedScene" uid="uid://81fs1resstdl" path="res://prophaunt/maps/Source/Chairs/chair_modern_cushion.tscn" id="2_e25s6"]

[sub_resource type="BoxShape3D" id="BoxShape3D_k0b8b"]
size = Vector3(0.8172, 1.86035, 0.817139)

[node name="ChairModernCushionProp" instance=ExtResource("1_wa2ar")]

[node name="ChairModernCushion" parent="Meshes" index="0" instance=ExtResource("2_e25s6")]

[node name="CollisionShape3D" type="CollisionShape3D" parent="StaticBody3D" index="0"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -0.00442499, 0.929443, -0.00329591)
shape = SubResource("BoxShape3D_k0b8b")
